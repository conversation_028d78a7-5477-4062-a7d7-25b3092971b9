import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { LoginScreen } from 'screens/Login/LoginScreen';
import { RegisterScreen } from 'screens/Register/RegisterScreen';
import { ForgotPassword } from 'screens/Login/ForgotPassword';
import { ProfileSetupScreen } from 'screens/MultiStepForm/ProfileSetupScreen';
import { GoalsScreen } from 'screens/MultiStepForm/GoalsSetupScreen';
import { SelectDeviceScreen } from 'screens/MultiStepForm/SelectDeviceScreen';
import { PairDeviceScreen } from 'screens/MultiStepForm/PairDeviceScreen';
import InitMultiStepForm from 'screens/MultiStepForm/InitMultiStepForm';
import { ResendVerificationMail } from 'screens/Login/ResendVerificationMail';

const RootStack = createStackNavigator();

const screenOptionStyle = {
    headerShown: false,
    animation: "slide_from_right",
    transitionSpec: {
        open: {
            animation: 'timing',
            config: { duration: 300 }
        },
        close: {
            animation: 'timing',
            config: { duration: 300 }
        }
    }
};

const RootStackNav = () => (
    <RootStack.Navigator screenOptions={screenOptionStyle}>
        <RootStack.Screen name="Login" component={LoginScreen} />
        <RootStack.Screen name="MultiStep" component={InitMultiStepForm} />
        <RootStack.Screen name="Register" component={RegisterScreen} />
        <RootStack.Screen name="Forgotpwd" component={ForgotPassword} />
        <RootStack.Screen name="ResendVerification" component={ResendVerificationMail} />
    </RootStack.Navigator>
);

export default RootStackNav;