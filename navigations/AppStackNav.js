import React, { memo } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Dashboard from 'screens/Dashboard/Dashboard';
import { ProfileScreen } from 'screens/Setting Screens/Profile/ProfileScreen';
import { RemindersScreen } from 'screens/Setting Screens/Reminders/views/RemindersScreen';
import { DeviceManagementScreen } from 'screens/Setting Screens/Device Management/DeviceManagementScreen';
import { GoalsScreen } from 'screens/Setting Screens/Goals/GoalsScreen';
import IntegrationScreen from 'screens/Setting Screens/App Integration/IntegrationScreen2';
import { FAQScreen } from 'screens/Setting Screens/FAQ/FAQScreen';
import { ContactScreen } from 'screens/Setting Screens/Contact/ContactScreen';
import NutritionScreen from 'screens/Menu Screens/Nutrition/views/NutritionScreen';
import AddReminderScreen from 'screens/Setting Screens/Reminders/views/AddReminderScreen';
import EditReminderScreen from 'screens/Setting Screens/Reminders/views/EditReminderScreen';
import ActivityScreen from 'screens/Menu Screens/Activity/views/ActivityScreen';
import MoodScreen from 'screens/Menu Screens/Mood/MoodScreen';
import VideoScreen from 'screens/Menu Screens/Video Library/VideoScreen';
import SleepScreen from 'screens/Menu Screens/Sleep/views/SleepScreen';
import DeviceScreen from 'screens/Menu Screens/Device/views/DeviceScreen';
import EditTimerScreen from 'screens/Menu Screens/Device/views/EditTimerScreen';
import RecipeScreen from 'screens/Menu Screens/Recipe/RecipeScreen';
import HelpScreen from 'screens/Setting Screens/Help/HelpScreen';
import { HelpScreenDetails } from 'screens/Setting Screens/Help/HelpScreenDetails';
import EditNutritionScreen from 'screens/Menu Screens/Nutrition/views/EditNutritionScreen';
import RecordSleep from 'screens/Menu Screens/Sleep/views/RecordSleep';
import AddMealScreen from 'screens/Menu Screens/Nutrition/views/AddMealScreen';
import EditMealScreen from 'screens/Menu Screens/Nutrition/views/EditMealScreen';
import EditActivitiesScreen from 'screens/Menu Screens/Activity/views/EditActivitiesScreen';
import RecipeDetailsScreen from 'screens/Menu Screens/Recipe/RecipeDetailsScreen';
import EditNutritionHistory from 'screens/Menu Screens/Nutrition/views/EditNutritionHistory';
import AddMealType from 'screens/Menu Screens/Nutrition/views/AddMealType';
import ChatBot from 'screens/Chatbot/ChatBot';
import WeightScreen from 'screens/Menu Screens/Weight Loss/views/WeightScreen';
import RecordWeight from 'screens/Menu Screens/Weight Loss/views/RecordWeight';
import SummaryScreen from 'screens/Menu Screens/Summary/SummaryScreen';
import AddUserMeal from 'screens/Menu Screens/Nutrition/views/AddUserMeal';
import EditMealIngredientScreen from 'screens/Menu Screens/Nutrition/views/EditMealIngredientScreen';
import AddMealIngredientScreen from 'screens/Menu Screens/Nutrition/views/AddMealIngredientScreen';
import EditActivityScreen from 'screens/Menu Screens/Activity/views/EditActivityScreen';
import VideoDetailsScreen from 'screens/Menu Screens/Video Library/VideoDetailsScreen';
import AddDeviceScreen from 'screens/Setting Screens/Device Management/AddDeviceScreen';
import { Colors } from 'constants/theme/colors';
import LogoutScreen from 'screens/Setting Screens/Logout/LogoutScreen';

const AppStack = createStackNavigator();

const config = {
    // animation: 'spring',
    // config: {
    //     stiffness: 1000,
    //     damping: 500,
    //     mass: 3,
    //     overshootClamping: true,
    //     restDisplacementThreshold: 0.01,
    //     restSpeedThreshold: 0.01,
    // },
};

const DashStackNav = () => {
    return (
        <AppStack.Navigator screenOptions={{
            headerShown: false, animation: "slide_from_right", transitionSpec: {
                open: {
                    animation: 'timing',
                    config: { duration: 300 } // Adjust duration in milliseconds
                },
                close: {
                    animation: 'timing',
                    config: { duration: 300 } // Adjust duration in milliseconds
                }
            }
        }} initialRouteName="dashboard" >
            <AppStack.Screen name="dashboard" component={Dashboard} options={{
                animation: "slide_from_left"
            }} listeners={({ navigation }) => ({
                focus: () => {
                    navigation.getParent()?.setOptions({
                        tabBarStyle: {
                            backgroundColor: Colors.primaryGreen,
                            elevation: 5,
                            position: "absolute",
                            bottom: 10,
                            borderRadius: 22,
                            marginHorizontal: '5%',
                            height: 60,
                            width: "90%",
                            paddingBottom: 0,
                            alignItems: "center",
                            justifyContent: "center"
                        }
                    });
                },
            })}
            />

            {/* Setting Navigation List */}
            <AppStack.Screen name="Profile" component={ProfileScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Goals" component={GoalsScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Device Management" component={DeviceManagementScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Add Device" component={AddDeviceScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Reminders" component={RemindersScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Add Reminder" component={AddReminderScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Edit Reminder" component={EditReminderScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="App Integration" component={IntegrationScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="FAQs" component={FAQScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Help" component={HelpScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Help Details" component={HelpScreenDetails}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Contact Us" component={ContactScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />
            <AppStack.Screen name="Logout" component={LogoutScreen}
                listeners={({ navigation }) => ({
                    focus: () => {
                        navigation.getParent()?.setOptions({
                            tabBarStyle: {
                                backgroundColor: Colors.primaryGreen,
                                elevation: 5,
                                position: "absolute",
                                bottom: 10,
                                borderRadius: 22,
                                marginHorizontal: '5%',
                                height: 60,
                                width: "90%",
                                paddingBottom: 0,
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        });
                    },
                })}
            />

            {/* Menu Tab Navigation List */}
            <AppStack.Screen name="Nutrition" component={NutritionScreen} />
            <AppStack.Screen name="Edit Nutrition" component={EditNutritionScreen} />
            <AppStack.Screen name="Edit Nutrition History" component={EditNutritionHistory} />
            <AppStack.Screen name="Add Meal Type" component={AddMealType} />
            <AppStack.Screen name="Add Meal" component={AddMealScreen} />
            <AppStack.Screen name="Add User Meal" component={AddUserMeal} />
            <AppStack.Screen name="Edit Meal" component={EditMealScreen} />
            <AppStack.Screen name="Add Meal Ingredient" component={AddMealIngredientScreen} />
            <AppStack.Screen name="Edit Meal Ingredient" component={EditMealIngredientScreen} />
            <AppStack.Screen name="Activity" component={ActivityScreen} />
            <AppStack.Screen name="Edit Activities" component={EditActivitiesScreen} />
            <AppStack.Screen name="Edit Activity" component={EditActivityScreen} />
            <AppStack.Screen name="Mood" component={MoodScreen} />
            <AppStack.Screen name='Weight Loss Tracking' component={WeightScreen} />
            <AppStack.Screen name='Record_Weight' component={RecordWeight} />
            <AppStack.Screen name="Video Library" component={VideoScreen} />
            <AppStack.Screen name="Sleep" component={SleepScreen} />
            <AppStack.Screen name="Record_Sleep" component={RecordSleep} />
            <AppStack.Screen name="Device" component={DeviceScreen} />
            <AppStack.Screen name="Edit Timer" component={EditTimerScreen} />
            <AppStack.Screen name="Recipes" component={RecipeScreen} />
            <AppStack.Screen name="Recipe Details" component={RecipeDetailsScreen} />
            <AppStack.Screen name="Summary" component={SummaryScreen} />
            <AppStack.Screen name="Chat Support" component={ChatBot}
            //  listeners={({ navigation }) => ({
            //     focus: () => {
            //         navigation.getParent()?.setOptions({
            //             tabBarStyle: { height: 0 }
            //         });
            //     },
            // })}
            />
            <AppStack.Screen name="Video Details" component={VideoDetailsScreen} options={{
                animation: "scale_from_center",
                presentation: "modal",
            }} />
        </AppStack.Navigator>
    );
};

export default memo(DashStackNav);
