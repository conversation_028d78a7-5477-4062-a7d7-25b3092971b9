
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Animated,
    Keyboard,
    Dimensions,
    Platform
} from 'react-native';
import { CommonActions, useNavigation, useRoute } from '@react-navigation/native';
import { TouchableWithoutFeedback } from 'react-native';
import { Colors } from 'constants/theme/colors';
import Setting from 'assets/icons/settings.png';
import AIIcon from 'assets/icons/ai-icon.png';
import Logo from 'assets/name_logo.png';
import Lemon from 'assets/illustrator/illustrator_1.png';
import Lemon2 from 'assets/illustrator/illustrator_5.png';
import Leaf from 'assets/illustrator/illustrator_2.png';
import { MenuList } from 'components/CustomCards/MenuList';
import { SettingsList } from 'components/CustomCards/SettingsList';
import { SlidePanel } from 'components/CustomCards/SlidePanel';
import useLayoutStore from 'store/layoutStore';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { memo } from 'react';
import { resetNavigate } from 'navigations/utils/resetNavigate';

const screenWidth = Dimensions.get('window').width;
const screenHeight = Dimensions.get('window').height;

const AppLayout = ({ children, illustration, bgColor = Colors.white, paddingHorizontal = 16, paddingTop = 30 }) => {
    const navigation = useNavigation();
    const insets = useSafeAreaInsets();

    const isMenuOpen = useLayoutStore(state => state.isMenuOpen);
    const isSettingOpen = useLayoutStore(state => state.isSettingOpen);
    const isChatbotOpen = useLayoutStore(state => state.isChatbotOpen);

    const toggleMenuDrawer = useLayoutStore(state => state.toggleMenu);
    const toggleSettingsDrawer = useLayoutStore(state => state.toggleSettings);
    const toggleChatbotDrawer = useLayoutStore(state => state.toggleChatbot);

    const [menuSlideAnim] = useState(new Animated.Value(-screenWidth));
    const [settingsSlideAnim] = useState(new Animated.Value(screenWidth));

    const lastTapTime = useRef(0);

    const toggleMenu = useCallback(() => {
        const toValue = isMenuOpen ? 0 : -screenWidth;
        // setMenuVisible(!menuVisible);
        Animated.timing(menuSlideAnim, {
            toValue,
            duration: 300,
            useNativeDriver: true,
        }).start();

        if (isSettingOpen) {
            toggleSettings();
        }
    }, [isMenuOpen, isSettingOpen, screenWidth, menuSlideAnim]);

    useEffect(() => {
        toggleMenu();
    }, [isMenuOpen]);

    const toggleSettings = useCallback(() => {
        const toValue = isSettingOpen ? 0 : screenWidth;
        Animated.timing(settingsSlideAnim, {
            toValue,
            duration: 300,
            useNativeDriver: true,
        }).start();
    }, [isSettingOpen, screenWidth, settingsSlideAnim]);


    useEffect(() => {
        toggleSettings();
    }, [isSettingOpen]);


    const handleOutsidePress = useCallback(() => {
        if (isMenuOpen) {
            toggleMenu();
            toggleMenuDrawer();
        }
        if (isSettingOpen) {
            toggleSettingsDrawer();
            toggleSettings();
        }
        if (isChatbotOpen) {
            toggleChatbotDrawer(false);
        }
        Keyboard.dismiss();
    }, [isMenuOpen, isSettingOpen, isChatbotOpen, toggleMenuDrawer, toggleSettingsDrawer, toggleChatbotDrawer]);

    const handleMenuNavigation = useCallback((screen) => {
        requestAnimationFrame(() => {
            toggleMenuDrawer();
            toggleMenu();

            const state = navigation.getState();
            const currentRoute = state.routes[state.index];

            if (currentRoute.name === screen) {
                return;
            }

            setTimeout(() => {
                resetNavigate(navigation, screen);
            }, 300)
        });
    }, [navigation, toggleMenuDrawer, toggleMenu]);

    const handleSettingsNavigation = useCallback((screen) => {
        requestAnimationFrame(() => {
            toggleSettings();
            toggleSettingsDrawer();

            const state = navigation.getState();
            const currentRoute = state.routes[state.index];

            if (currentRoute.name === screen) {
                return;
            }

            setTimeout(() => {
                resetNavigate(navigation, screen);
            }, 300)

        });
    }, [navigation, toggleSettingsDrawer, toggleSettings]);


    const handleChatbotNavigation = useCallback((screen) => {
        const now = Date.now();

        const state = navigation.getState();
        const currentRoute = state.routes[state.index];

        if (currentRoute.name === "Chat Support") {
            return;
        }

        if (now - lastTapTime.current >= 500) {
            lastTapTime.current = now;
            resetNavigate(navigation, "Chat Support");
        }
    }, [navigation]);

    return (
        <TouchableWithoutFeedback onPress={handleOutsidePress}>
            <View style={styles.container}>
                {/* Header */}
                <View style={styles.header}>
                    <View style={styles.headerLeft}>
                        <Image source={Logo} style={styles.logo} resizeMode="contain" />
                    </View>
                    <View style={styles.headerRight}>
                        <TouchableOpacity
                            style={styles.iconButton}
                            onPress={() => {
                                handleChatbotNavigation();
                                // toggleChatbotDrawer(true);
                            }}
                        >
                            <Image source={AIIcon} style={styles.chatIcon} resizeMode="contain" />
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.iconButton} onPress={toggleSettingsDrawer}>
                            <Image source={Setting} style={styles.icon} resizeMode="contain" />
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Decorative Images */}
                {illustration && (
                    <>
                        {!(isMenuOpen || isSettingOpen) && <View style={styles.lemon} pointerEvents='none'>
                            <Image source={Lemon} style={{ width: "100%", height: "100%" }} resizeMode="contain" />
                        </View>}
                        <View style={styles.lemon2} pointerEvents='none'>
                            <Image source={Lemon2} style={{ width: "100%", height: "100%" }} resizeMode="contain" />
                        </View>
                        <View style={styles.leaf} pointerEvents='none'>
                            <Image source={Leaf} style={{ width: "100%", height: "100%" }} resizeMode="contain" />
                        </View>
                    </>
                )}

                {/* Main Content
                <ScrollViewContext.Provider value={{
                    scrollViewRef,
                    setScrollViewRef: (ref) => {
                        scrollViewRef.current = ref;
                    }
                }}> */}

                <View style={[styles.contentWrapper, { backgroundColor: bgColor, paddingHorizontal: paddingHorizontal, paddingTop: paddingTop }]}>
                    {children}
                </View>
                {/*</ScrollViewContext.Provider> */}

                {/* Overlay for Sliding Panels */}
                {(isMenuOpen || isSettingOpen) && (
                    <TouchableOpacity
                        style={styles.overlay}
                        activeOpacity={1}
                        onPress={handleOutsidePress}
                    />
                )}

                {/* Sliding Panels with adjusted bottom padding */}
                <SlidePanel
                    visible={isMenuOpen}
                    side="left"
                    slideAnim={menuSlideAnim}
                    bottomInset={insets.bottom} // Pass bottom inset
                >
                    <MenuList
                        onNavigate={handleMenuNavigation}
                        bottomPadding={insets.bottom + 60}
                    />
                </SlidePanel>

                <SlidePanel
                    visible={isSettingOpen}
                    side="right"
                    slideAnim={settingsSlideAnim}
                    bottomInset={insets.bottom} // Pass bottom inset
                >
                    <SettingsList
                        onNavigate={handleSettingsNavigation}
                        bottomPadding={insets.bottom + 60}
                    />
                </SlidePanel>
            </View>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.primaryGreen,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 5,
        paddingHorizontal: 12,
        position: 'absolute',
        top: Platform.OS === 'ios' ? 20 : 10,
        left: 0,
        right: 0,
        height: 90,
        elevation: 5,
    },
    headerLeft: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    headerRight: {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 10,
        justifyContent: 'space-around',
    },
    logo: {
        width: 120,
        // height: 30,
        marginLeft: 10,
    },
    iconButton: {
        // marginRight: 10,
        // padding: 2,
    },
    icon: {
        width: 28,
        height: 28,
        tintColor: Colors.white,
    },
    chatIcon: {
        width: 48,
        height: 48,
        borderRadius: 50,
    },
    contentWrapper: {
        marginTop: 100,
        backgroundColor: Colors.white,
        borderTopLeftRadius: 50,
        borderTopRightRadius: 50,
        // paddingBottom: 50,
        paddingHorizontal: 20,
        flex: 1,
        // minHeight: screenHeight,
        minWidth: screenWidth,
        overflow: 'hidden'
    },
    lemon: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? '19%' : '16%',
        right: '-20%',
        width: '35%',
        height: '35%',
        resizeMode: 'contain',
        zIndex: 10,
        transform: [{ translateY: '-50%' }, { rotate: '95deg' }]
    },
    lemon2: {
        position: 'absolute',
        top: '85%',
        right: '-15%',
        width: '30%',
        height: '30%',
        resizeMode: 'contain',
        zIndex: 10,
        transform: [{ translateY: '-50%' }, { rotate: '-5deg' }]
    },
    leaf: {
        position: 'absolute',
        top: '60%',
        left: '-30%',
        width: '50%',
        height: '50%',
        resizeMode: 'contain',
        zIndex: 5,
        transform: [{ translateY: '-50%' }, { rotate: '35deg' }],
    },
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
});

export default memo(AppLayout);