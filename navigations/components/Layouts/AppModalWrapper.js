import { Animated, Keyboard, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { memo, useCallback, useEffect, useRef, useState } from 'react'
import { SlidePanel } from 'components/CustomCards/SlidePanel'
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MenuList } from 'components/CustomCards/MenuList';
import { CommonActions, useNavigation } from '@react-navigation/native';
import { screenHeight, screenWidth } from 'constants/sizes';
import { TouchableWithoutFeedback } from 'react-native';
import useLayoutStore from 'store/layoutStore';
import { SettingsList } from 'components/CustomCards/SettingsList';
import Modal from 'react-native-modal';
import { Colors } from 'constants/theme/colors';

const AppModalWrapper = () => {
    const insets = useSafeAreaInsets();
    const navigation = useNavigation();

    const isMenuOpen = useLayoutStore(state => state.isMenuOpen);
    const isSettingOpen = useLayoutStore(state => state.isSettingOpen);
    const isChatbotOpen = useLayoutStore(state => state.isChatbotOpen);

    const toggleMenuDrawer = useLayoutStore(state => state.toggleMenu);
    const toggleSettingsDrawer = useLayoutStore(state => state.toggleSettings);
    const toggleChatbotDrawer = useLayoutStore(state => state.toggleChatbot);

    const menuSlideAnim = useRef(new Animated.Value(-screenWidth)).current;
    const settingsSlideAnim = useRef(new Animated.Value(screenWidth)).current;

    const toggleMenu = useCallback(() => {
        const toValue = isMenuOpen ? 0 : -screenWidth;
        // setMenuVisible(!menuVisible);
        Animated.timing(menuSlideAnim, {
            toValue,
            duration: 300,
            useNativeDriver: true,
        }).start();

        if (isSettingOpen) {
            toggleSettings();
        }
    }, [isMenuOpen, isSettingOpen, screenWidth, menuSlideAnim]);

    useEffect(() => {
        toggleMenu();
    }, [isMenuOpen]);

    const toggleSettings = useCallback(() => {
        const toValue = isSettingOpen ? 0 : screenWidth;
        Animated.timing(settingsSlideAnim, {
            toValue,
            duration: 300,
            useNativeDriver: true,
        }).start();

        if (isMenuOpen) {
            toggleMenu();
        }
    }, [isSettingOpen, screenWidth, settingsSlideAnim]);

    useEffect(() => {
        toggleSettings();
    }, [isSettingOpen]);

    const handleOutsidePress = useCallback(() => {
        if (isMenuOpen) {
            toggleMenu();
            toggleMenuDrawer();
        }
        if (isSettingOpen) {
            toggleSettingsDrawer();
            toggleSettings();
        }
        if (isChatbotOpen) {
            toggleChatbotDrawer(false);
        }
        Keyboard.dismiss();
    }, [isMenuOpen, isSettingOpen, isChatbotOpen, toggleMenuDrawer, toggleSettingsDrawer, toggleChatbotDrawer]);

    const handleMenuNavigation = useCallback((screen) => {


        toggleMenuDrawer();
        toggleMenu();
        const state = navigation.getState();
        const dashboardIndex = state.routes.findIndex(route => route.name);
        if (dashboardIndex) {

        }

        // navigation.dispatch(
        //     CommonActions.reset({
        //         index: 1,
        //         routes: [
        //             { name: 'dashboard' },
        //             // tate.routes[dashboardIndex],
        //             { name: screen }
        //         ]
        //     })
        // );

        // navigation.navigate('dashboard');
        navigation.navigate(screen);

        // setTimeout(() => {
        //     toggleMenuDrawer();
        //     toggleMenu();
        // }, 0);
    }, [navigation, toggleMenuDrawer, toggleMenu]);

    const handleSettingsNavigation = useCallback((screen) => {
        toggleSettings();
        toggleSettingsDrawer();
        const state = navigation.getState();
        const dashboardIndex = state.routes.findIndex(route => route.name === 'dashboard');
        navigation.dispatch(
            CommonActions.reset({
                index: 1,
                routes: [
                    // state.routes[dashboardIndex],
                    { name: screen }
                ]
            })
        );
    }, [navigation, toggleSettingsDrawer, toggleSettings]);

    return (
        // <Modal isVisible={isMenuOpen || isSettingOpen} coverScreen={true}>
        //     <TouchableWithoutFeedback onPress={handleOutsidePress}>
        <>
            <SlidePanel
                visible={isMenuOpen}
                side="left"
                slideAnim={menuSlideAnim}
                bottomInset={insets.bottom}
                closePanel={handleOutsidePress}
            >
                <MenuList
                    onNavigate={handleMenuNavigation}
                    bottomPadding={insets.bottom + 60}
                />
            </SlidePanel>

            <SlidePanel
                visible={isSettingOpen}
                side="right"
                slideAnim={settingsSlideAnim}
                bottomInset={insets.bottom}
                closePanel={handleOutsidePress}
            >
                <SettingsList
                    onNavigate={handleSettingsNavigation}
                    bottomPadding={insets.bottom + 60}
                />
            </SlidePanel>
        </>
        //     </TouchableWithoutFeedback>
        // </Modal>
    )
}

export default memo(AppModalWrapper);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    overlay: {
        // ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
})