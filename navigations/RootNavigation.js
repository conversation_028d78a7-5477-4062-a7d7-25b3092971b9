// RootNavigation.js
// This file provides a way to navigate from outside of React components
// For example, from Axios interceptors or other non-React code

import { createNavigationContainerRef } from '@react-navigation/native';
import { createRef } from 'react';

export const navigationRef = createNavigationContainerRef();

export function navigate(name, params) {
    if (navigationRef.isReady()) {
        // Verify the screen exists in the navigator
        navigationRef.current.navigate(name, params);
    } else {
        // If navigation isn't ready yet, we can queue this navigation
        // or handle it differently
        console.warn('Navigation attempted before navigator was ready');
    }
}

export function reset(name, params) {
    if (navigationRef.current) {
        navigationRef.current.reset({
            index: 0,
            routes: [{ name, params }],
        });
    }
}

export function goBack() {
    if (navigationRef.current) {
        navigationRef.current.goBack();
    }
}