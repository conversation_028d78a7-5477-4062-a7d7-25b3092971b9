import React, { useEffect, useRef } from "react";
import { View, StyleSheet, Modal, Platform, AppState } from "react-native";
import { useAuth } from "../context/AuthContext";
import RootStackNav from './RootStackNav';
import StartUpStackNav from './StartUpStackNav';
import BottomTabStackNav from './BottomTabStackNav';
import { Colors } from 'constants/theme/colors';
import CustomLoader from 'components/CustomAction/CustomLoader';
import { CustomAlert } from "components/CustomAction";
import useGlobalErrorStore from "store/globalErrorStore";
import GlobalLoader from "./GlobalLoader";
import Toast from "react-native-toast-message";
import authService from 'services/authService';

import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import { registerBackgroundTask } from 'services/backgroundService';
import useHealthPermissionStore from "store/healthPermissionStore";
import useAppleHealthPermissionStore from "store/appleHealthPermissionStore";
import { useHealthData } from "hooks/useHealthData";
import { getIsFirstCall, readSampleData, setIsFirstCall } from "services/health/healthDataService";
import checkWithinLastHour from "utils/checkWithinLastHour";
import { readAppleHealthData } from "services/health/appleHealthDataService";

// TaskManager.defineTask('background-fetch', async () => {
//   const now = Date.now();

//   console.log(`Got background fetch call at date: ${new Date(now).toLocaleTimeString()}`);

//   if (Platform.OS == "android") {
//     await readSampleData(1);
//   }

//   return BackgroundFetch.BackgroundFetchResult.NewData;
// });

export const AppContent = () => {
	const { state } = useAuth();
	const healthDataError = useGlobalErrorStore(state => state.healthDataError);
	const setHealthDataError = useGlobalErrorStore(state => state.setHealthDataError);

	useEffect(() => {
		if (healthDataError) {
			Toast.show({
				type: 'error',
				text1: "Health error",
				text2: healthDataError,
				position: 'bottom',
				autoHide: true,
				bottomOffset: 80,
				hideOnPress: true,
			});

			setHealthDataError(null);
		}
	}, [healthDataError]);

	// Show loading screen while checking authentication status
	if (state.isLoading) {
		return (
			<View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
				<CustomLoader />
			</View>
		);
	}

	const StartUp = () => {
		// Check if user is authenticated
		if (!state.userToken || !state.user) {
			return <RootStackNav />;
		}

		if (!state?.user?.isAccountCompleted) {
			return <StartUpStackNav />;
		}

		const lastErrorTimeRef = useRef(null);


		const hasHealthPermission = useHealthPermissionStore(state => state?.hasHealthPermission);
		const hasBackgroundPermission = useHealthPermissionStore(state => state?.hasBackgroundPermission);
		const hasAppleHealthPermission = useAppleHealthPermissionStore(state => state?.hasAppleHealthPermission);
		const setHealthDataError = useGlobalErrorStore(state => state?.setHealthDataError);

		const healthDataHook = useHealthData();

		// Initialize the hook with try-catch for error handling
		let healthData = {};
		try {
			healthData = healthDataHook();
		} catch (error) {
			console.error('Error initializing health data hook:', error);
		}
		const {
			hasApplePermissions = false,
		} = healthData;


		useEffect(() => {
			let intervalId;
			let isFetching = false;

			const fetchAndroidData = async (count) => {
				if (isFetching) return;

				isFetching = true;

				const res = await readSampleData(count, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
				if (!res.success && !checkWithinLastHour(lastErrorTimeRef.current)) {
					lastErrorTimeRef.current = new Date();
					setHealthDataError(res.error);
				}

				isFetching = false;
			};

			const fetchAppleHealthData = async (count) => {
				if (isFetching) return;

				isFetching = true;

				try {
					if (Platform.OS === 'ios' && hasApplePermissions) {
						console.log('Fetching Apple Health data...');
						const res = await readAppleHealthData(count, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);

						if (!res.success && !checkWithinLastHour(lastErrorTimeRef.current)) {
							lastErrorTimeRef.current = new Date();
							setHealthDataError(res.error);
						}
					} else if (Platform.OS === 'ios') {
						console.log('Apple HealthKit permissions not granted according to hook - hasApplePermissions:', hasApplePermissions);
					}
				} catch (error) {
					console.error('Error in fetchAppleHealthData:', error);
				} finally {
					isFetching = false;
				}
			};

			if (Platform.OS === 'android' && hasHealthPermission) {
				(async () => {
					const isFirstCall = await getIsFirstCall();

					if (isFirstCall) {
						await setIsFirstCall();
					}

					await fetchAndroidData(isFirstCall ? 30 : 7);

					intervalId = setInterval(() => {
						fetchAndroidData(1);
					}, 10 * 60 * 1000);

				})();
			} else if (Platform.OS === 'ios') {
				(async () => {
					const isFirstCall = await getIsFirstCall();

					if (isFirstCall) {
						await setIsFirstCall();
					}

					await fetchAppleHealthData(isFirstCall ? 30 : 7);

					intervalId = setInterval(() => {
						fetchAppleHealthData(1);
					}, 10 * 60 * 1000);
				})();
			}

			return () => {
				if (intervalId) {
					clearInterval(intervalId);
				}
			};
		}, [hasApplePermissions, hasHealthPermission]); // Use hasApplePermissions from hook instead of store

		useEffect(() => {
			const fetchInitialData = async () => {
				try {
					if (Platform.OS === "android" && hasHealthPermission) {
						readSampleData(1, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
					} else if (Platform.OS === "ios" && hasApplePermissions) {
						console.log('Initial fetch: Fetching Apple Health data...');
						try {
							// Check if this is the first call to determine how many days to fetch
							const isFirstCall = await getIsFirstCall();
							const daysToFetch = isFirstCall ? 30 : 1; // First time: 30 days, subsequent: 1 day

							const res = await readAppleHealthData(daysToFetch, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
							console.log(`Initial Apple Health Data (${daysToFetch} days):`, res);
						} catch (err) {
							console.error('Error fetching initial Apple Health data:', err);
						}
					} else if (Platform.OS === "ios") {
						console.log('Initial fetch: Apple HealthKit permissions not granted according to hook - hasApplePermissions:', hasApplePermissions);
					}
				} catch (error) {
					console.error('Error in fetchInitialData:', error);
				}
			};

			fetchInitialData();
		}, [hasApplePermissions, hasHealthPermission]);

		// useEffect(() => {
		//   if (Platform.OS == "android" && hasBackgroundPermission) {
		//     registerBackgroundTask();
		//   }
		// }, [hasBackgroundPermission]);


		const appState = useRef(AppState.currentState);

		useEffect(() => {
			const subscription = AppState.addEventListener("change", async (nextAppState) => {
				if (
					appState.current.match(/inactive|background|unknown|extension/) &&
					nextAppState === "active"
				) {
					try {
						if (Platform.OS === "android" && hasHealthPermission) {
							const res = await readSampleData(1, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
							if (!res.success && !checkWithinLastHour(lastErrorTimeRef.current)) {
								lastErrorTimeRef.current = new Date();
								setHealthDataError(res.error);
							}
						} else if (Platform.OS === "ios" && hasApplePermissions) {
							console.log('Foreground: Fetching Apple Health data...');
							try {
								// When app comes to foreground, fetch 1 day of data (same as Android)
								const res = await readAppleHealthData(1, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
								console.log('Apple Health Data (foreground):', res);

								if (!res.success && !checkWithinLastHour(lastErrorTimeRef.current)) {
									lastErrorTimeRef.current = new Date();
									setHealthDataError(res.error);
								}
							} catch (err) {
								console.error('Error fetching foreground Apple Health data:', err);
							}
						} else if (Platform.OS === "ios") {
							console.log('Foreground: Apple HealthKit permissions not granted according to hook - hasApplePermissions:', hasApplePermissions);
						}
					} catch (error) {
						console.error('Error in AppState change handler:', error);
					}
				}

				appState.current = nextAppState;
			});

			return () => {
				subscription.remove();
			};
		}, [hasApplePermissions, hasHealthPermission]); // Use hasApplePermissions from hook instead of store

		// useEffect(() => {
		//   TaskManager.isTaskRegisteredAsync("background-fetch").then((isRegistered) => {
		//     console.log(`Is task registered? ${isRegistered}`);
		//   });
		// }, []);


		useEffect(() => {
			(async () => {
				let success = false;

				while (!success) {
					success = (await authService.updateUserTimeZone()).success;
				}
			})();
		}, []);

		return (
			<>
				{/* <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName="tab">
					<Stack.Screen name="Chat Support" component={ChatBot} />
					<Stack.Screen name="tab" component={BottomTabStackNav} />
				</Stack.Navigator> */}
				<BottomTabStackNav />
				{/* <CustomAlert title="Error" message={healthDataError} visible={!!healthDataError} buttons={[{ text: "OK", onPress: () => setHealthDataError(null), style: "allowButton" }]} onClose={() => setHealthDataError(null)} /> */}
				<GlobalLoader />
			</>
		);
	};

	return (
		<>
			<StartUp />
		</>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		backgroundColor: Colors.primaryGreen,
	},
});