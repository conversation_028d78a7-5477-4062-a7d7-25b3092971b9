// utils/LazyScreenWrapper.js
import React, { Suspense } from 'react';
import { View, ActivityIndicator } from 'react-native';

const LazyScreenWrapper = (importFunc) => {
    const LazyComponent = React.lazy(importFunc);

    return (props) => (
        <Suspense fallback={
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" />
            </View>
        }>
            <LazyComponent {...props} />
        </Suspense>
    );
};

export default LazyScreenWrapper;
