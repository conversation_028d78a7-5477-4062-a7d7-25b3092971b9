import * as Linking from 'expo-linking';

const prefix = Linking.createURL('/');

export const linkingConfig = {
    prefixes: [prefix],
    config: {
        screens: {
            // Root Stack Navigator
            login: 'Login',
            register: 'Register',
            forgotpwd: 'Forgotpwd',
            multistep: 'MultiStep',
            // back : "back",
            home: {
                screens: {
                    // DashStackNav screens
                    dashboard: 'dashboard',

                    // Setting Screens
                    Profile: 'profile',
                    Goals: 'goals',
                    'Device Management': 'device-management',
                    Reminders: 'reminders',
                    'Add Reminder': 'add-reminder',
                    'Edit Reminder': 'edit-reminder',
                    'App Integration': 'app-integration',
                    FAQs: 'faqs',
                    Help: 'help',
                    Contact: 'contact',
                    Logout: 'logout',

                    // Menu Screens
                    Nutrition: 'nutrition',
                    Activity: 'activity',
                    Mood: 'mood',
                    'Weight Loss Tracking': 'weight-loss',
                    'Video Library': 'video-library',
                    Sleep: 'sleep',
                    Device: 'device',
                    Recipes: 'recipes',
                    'Chat Support': 'chat-support',
                }
            },
            // menu : "menu"
        },
    },
    debug: true,
};