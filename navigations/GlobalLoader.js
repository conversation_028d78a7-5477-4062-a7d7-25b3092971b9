import { Modal, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import useNutritionMealRecordStore from "store/nutritionMealRecordStore";
import { CustomLoader } from 'components/CustomAction';
import { Colors } from 'constants/theme/colors';
import { StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useGlobalLoadingState from 'store/globalLoadingState';

const GlobalLoader = () => {
    const insets = useSafeAreaInsets();

    // const isLoadingMealRecords = useNutritionMealRecordStore(state => state.isLoadingMealRecords);
    const isLoading = useGlobalLoadingState(state => state.isLoading);

    return (
        (isLoading) ? <View style={[styles.container, { paddingTop: insets.top }]}>
            <CustomLoader />
        </View> : null
    );
};

export default GlobalLoader;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        bottom: 50,
        justifyContent: "center",
        alignItems: 'stretch',
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        backgroundColor: Colors.white
    },
});