import { z } from "zod";

const reminderSchema = z.object({
    id: z.string(),
    userId: z.string(),
    label: z.string(),
    sound: z.object({
        ios: z.string().optional(),
        android: z.string().optional(),
    }).optional(),
    categoryString: z.string(),
    time: z.string().datetime(),
    frontend_screen_url: z.string(),
    createdAt: z.string().datetime(),
});

export default z.array(reminderSchema);
