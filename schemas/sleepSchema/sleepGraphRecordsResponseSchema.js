import { z } from "zod";

const weeklySleepRecordSchema = z.object({
    period: z.string(),
    averageSleep: z.number().nullable().transform((val) => val ?? 0),
});

const monthlySleepRecordSchema = z.object({
    period: z.string(),
    averageSleep: z.number().nullable().transform((val) => val ?? 0),
    startDate: z.string(),
    endDate: z.string(),
    year: z.string(),
});

const halfYearlySleepRecordSchema = z.object({
    period: z.string(),
    averageSleep: z.number().nullable().transform((val) => val ?? 0),
    year: z.string(),
});

const yearlySleepRecordSchema = z.object({
    period: z.string(),
    averageSleep: z.number().nullable().transform((val) => val ?? 0),
    year: z.string(),
});

const sleepGraphRecordsResponseSchema = z.discriminatedUnion("filter", [
    z.object({
        filter: z.literal("weekly"),
        data: z.array(weeklySleepRecordSchema),
    }),
    z.object({
        filter: z.literal("monthly"),
        data: z.array(monthlySleepRecordSchema),
    }),
    z.object({
        filter: z.literal("half_yearly"),
        data: z.array(halfYearlySleepRecordSchema),
    }),
    z.object({
        filter: z.literal("yearly"),
        data: z.array(yearlySleepRecordSchema),
    }),
]);

export default sleepGraphRecordsResponseSchema;
