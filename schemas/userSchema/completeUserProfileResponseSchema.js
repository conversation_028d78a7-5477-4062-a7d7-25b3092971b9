import { z } from 'zod'

const completeUserProfileResponseSchema = z.object({
    id: z.string(),
    email: z.string().email(),
    name: z.string(),
    isEmailVerified: z.boolean(),
    isAccountCompleted: z.boolean(),
    isDeleted: z.boolean(),
    age: z.number().optional(),
    gender: z.string().optional(),
    height: z.number().optional(),
    weight: z.number().optional(),
    deviceUsageLimit: z.number().nullable().optional(),
    diet_preference: z.string().optional(),
    goals: z.array(z.object({
        id: z.string(),
        goal_type: z.string(),
        selected_goal: z.string(),
    })).optional(),
    timeZone: z.string().optional(),
    app_permissions: z.array(z.object({
        app_permission: z.string(),
        isPermissionAllowed: z.boolean(),
    })).optional(),
})

export default completeUserProfileResponseSchema;