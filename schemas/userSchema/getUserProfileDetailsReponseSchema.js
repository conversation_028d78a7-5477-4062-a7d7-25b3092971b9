
import { z } from "zod";

const getUserProfileDetailsSchema = z.object({
    id: z.string(),
    name: z.string(),
    email: z.string().email(),
    isEmailVerified: z.boolean(),
    isDeleted: z.boolean(),
    isAccountCompleted: z.boolean(),
    age: z.number().optional(),
    gender: z.string().optional(),
    height: z.number().optional(),
    weight: z.number().optional(),
    diet_preference: z.string().optional(),
    goals: z.array(z.object({
        goal_type: z.string(),
        selected_goal: z.string(),
    })).optional(),
    timeZone: z.string().optional(),
    deviceUsageLimit: z.number().nullable().optional(),
    app_permissions: z.array(z.any()).optional(),
});

export default getUserProfileDetailsSchema;