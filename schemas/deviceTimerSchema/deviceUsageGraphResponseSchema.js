import { z } from "zod";

const weeklyTimerSchema = z.object({
  period: z.string(),
  averageDeviceUsage: z.number().nullable(),
});

const monthlyTimerSchema = z.object({
  period: z.string(),
  averageDeviceUsage: z.number().nullable(),
  startDate: z.string(),
  endDate: z.string(),
});

const halfYearlyTimerSchema = z.object({
  period: z.string(),
  averageDeviceUsage: z.number().nullable(),
  year: z.number(),
});

const yearlyTimerSchema = z.object({
  period: z.string(),
  averageDeviceUsage: z.number().nullable(),
  year: z.number(),
});

export const deviceUsageGraphResponseSchema = z.discriminatedUnion("filter", [
  z.object({
    filter: z.literal("weekly"),
    data: z.array(weeklyTimerSchema),
  }),
  z.object({
    filter: z.literal("monthly"),
    data: z.array(monthlyTimerSchema),
  }),
  z.object({
    filter: z.literal("half_yearly"),
    data: z.array(halfYearlyTimerSchema),
  }),
  z.object({
    filter: z.literal("yearly"),
    data: z.array(yearlyTimerSchema),
  }),
]);
