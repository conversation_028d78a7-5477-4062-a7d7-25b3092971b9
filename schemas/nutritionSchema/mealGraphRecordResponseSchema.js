import { z } from "zod";

const nutrientDataSchema = z.object({
    protein: z.number(),
    calories: z.number(),
    fats: z.number(),
    fiber: z.number(),
    carbs: z.number(),
});

const weeklyMealSchema = z.object({
    period: z.string(),
    data: nutrientDataSchema.nullable(),
});

const monthlyMealSchema = z.object({
    period: z.string(),
    data: nutrientDataSchema.nullable(),
    startDate: z.string(),
    endDate: z.string(),
});

const halfYearlyMealSchema = z.object({
    period: z.string(),
    data: nutrientDataSchema.nullable(),
    year: z.number(),
});

const yearlyMealSchema = z.object({
    period: z.string(),
    data: nutrientDataSchema.nullable(),
    year: z.number(),
});

const mealGraphRecordResponseSchema = z.discriminatedUnion("filter", [
    z.object({
        filter: z.literal("weekly"),
        data: z.array(weeklyMealSchema),
    }),
    z.object({
        filter: z.literal("monthly"),
        data: z.array(monthlyMealSchema),
    }),
    z.object({
        filter: z.literal("half_yearly"),
        data: z.array(halfYearlyMealSchema),
    }),
    z.object({
        filter: z.literal("yearly"),
        data: z.array(yearlyMealSchema),
    }),
]);

export default mealGraphRecordResponseSchema;