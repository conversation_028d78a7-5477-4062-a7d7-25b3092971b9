
import { z } from "zod";

const mealEntrySchema = z.object({
    recipeId: z.string(),
    quantity: z.string(),
    measurement: z.string(),
    title: z.string(),
    thumbnailUrl: z.string().url().optional(),
    protein: z.number(),
    calories: z.number(),
    fats: z.number(),
    fiber: z.number(),
    carbs: z.number(),
    author: z.string().nullable().optional(),
});

const mealRecordSchema = z.object({
    id: z.string(),
    mealName: z.string(),
    date: z.string().datetime(),
    mealTime: z.string(),
    meals: z.array(mealEntrySchema),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
});

export const mealRecordResponseSchema = mealRecordSchema;