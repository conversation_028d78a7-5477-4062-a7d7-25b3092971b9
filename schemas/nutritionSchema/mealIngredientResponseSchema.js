
import { z } from "zod";

const ingredientNutritionSchema = z.object({
    quantity: z.string(),
    protein: z.number(),
    calories: z.number(),
    fats: z.number(),
    fiber: z.number(),
    carbs: z.number(),
});

const mealIngredientResponseSchema = z.object({
    id: z.string(),
    name: z.string(),
    thumbnailUrl: z.string().url().optional(),
    ingredientNutritionByQuantity: z.array(ingredientNutritionSchema),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
});

export default mealIngredientResponseSchema;