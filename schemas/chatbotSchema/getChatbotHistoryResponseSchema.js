import { z } from "zod";

const getChatbotHistoryResponseSchema = z.object({
    messages: z.array(
        z.object({
            _id: z.string(),
            questionId: z.string().optional(),
            content: z.string(),
            role: z.string(),
            timestamp: z.string().datetime(),
        })
    ),
    sessionId: z.string(),
    page: z.number(),
    totalMessages: z.number(),
});

export default getChatbotHistoryResponseSchema;
