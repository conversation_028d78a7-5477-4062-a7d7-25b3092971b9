import { z } from "zod";

const weeklyMoodSchema = z.object({
    period: z.string(),
    averageMoodType: z.string().nullable().transform((val) => val ?? ''),
    averageHungerLevel: z.string().nullable().transform((val) => val ?? ''),
    year: z.string(),
});

const monthlyMoodSchema = z.object({
    period: z.string(),
    averageMoodType: z.string().nullable().transform((val) => val ?? ''),
    averageHungerLevel: z.string().nullable().transform((val) => val ?? ''),
    startDate: z.string(),
    endDate: z.string(),
});

const halfYearlyMoodSchema = z.object({
    period: z.string(),
    averageMoodType: z.string().nullable().transform((val) => val ?? ''),
    averageHungerLevel: z.string().nullable().transform((val) => val ?? ''),
    year: z.string(),
});

const yearlyMoodSchema = z.object({
    period: z.string(),
    averageMoodType: z.string().nullable().transform((val) => val ?? ''),
    averageHungerLevel: z.string().nullable().transform((val) => val ?? ''),
    year: z.string(),
});

const moodGraphResponseSchema = z.discriminatedUnion("filter", [
    z.object({
        filter: z.literal("weekly"),
        data: z.array(weeklyMoodSchema),
    }),
    z.object({
        filter: z.literal("monthly"),
        data: z.array(monthlyMoodSchema),
    }),
    z.object({
        filter: z.literal("half_yearly"),
        data: z.array(halfYearlyMoodSchema),
    }),
    z.object({
        filter: z.literal("yearly"),
        data: z.array(yearlyMoodSchema),
    }),
]);

export default moodGraphResponseSchema;
