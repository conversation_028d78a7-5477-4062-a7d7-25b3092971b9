
import { z } from "zod";

const createRecipeResponseSchema = z.object({
    id: z.string(),
    title: z.string(),
    ingredients: z.array(z.string()),
    thumbnailUrl: z.string().url().optional(),
    nutritionByQuantity: z.array(
        z.object({
            quantity: z.string(),
            protein: z.number(),
            calories: z.number(),
            fats: z.number(),
            fiber: z.number(),
            carbs: z.number(),
        })
    ),
    author: z.string(),
    createdAt: z.string().datetime(),
});

export default createRecipeResponseSchema;