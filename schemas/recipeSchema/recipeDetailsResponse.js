import { z } from "zod";

const nutritionSchema = z.object({
    quantity: z.string(),
    protein: z.number(),
    calories: z.number(),
    fats: z.number(),
    fiber: z.number(),
    carbs: z.number(),
});

export const recipeDetailsSchema = z.object({
    id: z.string(),
    title: z.string(),
    ingredients: z.array(z.string()),
    nutritionByQuantity: z.array(nutritionSchema),
    createdAt: z.string().datetime(),


    author: z.string().optional(),
    directions: z.string().optional(),
    timeToPrep: z.number().optional(),
    thumbnailUrl: z.string().url().optional(),
    mealType: z.string().optional(),
    category: z.string().optional(),
});