import { z } from "zod";

const weeklyResponseSchema = z.array(
    z.object({
        averageWeight: z.number().nullable(),
        period: z.string(),
    })
);

const monthlyResponseSchema = z.array(
    z.object({
        averageWeight: z.number().nullable(),
        period: z.string(),
        startDate: z.string(),
        endDate: z.string(),
        year: z.string(),
    })
);

const halfYearlyResponseSchema = z.array(
    z.object({
        averageWeight: z.number().nullable(),
        period: z.string(),
        year: z.string(),
    })
);

const yearlyResponseSchema = z.array(
    z.object({
        averageWeight: z.number().nullable(),
        period: z.string(),
        year: z.string(),
    })
);

const weightGraphResponseSchemas = {
    weekly: weeklyResponseSchema,
    monthly: monthlyResponseSchema,
    half_yearly: halfYearlyResponseSchema,
    yearly: yearlyResponseSchema,
};

export default weightGraphResponseSchemas;
