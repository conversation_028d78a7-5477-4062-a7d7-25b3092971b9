
import { z } from "zod";

const carouselItemSchema = z.object({
    id: z.string(),
    tag: z.string(),
    greetings: z.string(),
    subheading: z.string(),
    content: z.string(),
    image: z.string().optional(),
    date: z.string().datetime(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
});

const carousalSchemaResponseSchema = z.array(carouselItemSchema);

export default carousalSchemaResponseSchema;