import { z } from 'zod';

const goalSchema = z.object({
    id: z.string(),
    goal_type: z.string(),
    selected_goal: z.string(),
});

const appPermissionSchema = z.object({}).passthrough();

const userSchema = z.object({
    id: z.string(),
    email: z.string().email(),
    name: z.string(),
    isEmailVerified: z.boolean(),
    isAccountCompleted: z.boolean(),
    isDeleted: z.boolean(),
    age: z.number().optional(),
    gender: z.string().optional(),
    height: z.number().optional(),
    weight: z.number().optional(),
    deviceUsageLimit: z.number().nullable(),
    diet_preference: z.string().optional(),
    timeZone: z.string().nullable(),
    goals: z.array(goalSchema),
    app_permissions: z.array(appPermissionSchema),
});

const loginResponseSchema = z.object({
    error: z.boolean(),
    statusCode: z.number(),
    msg: z.string(),
    accessToken: z.string(),
    expiry: z.string().datetime(),
    user: userSchema,
});

export default loginResponseSchema;