import { z } from "zod";

const weeklyActivitySchema = z.object({
    period: z.string(),
    averageBurnedCalories: z.number().nullable(),
    year: z.string(),
});

const monthlyActivitySchema = z.object({
    period: z.string(),
    averageBurnedCalories: z.number().nullable(),
    startDate: z.string(),
    endDate: z.string(),
    year: z.string(),
});

const halfYearlyActivitySchema = z.object({
    period: z.string(),
    averageBurnedCalories: z.number().nullable(),
    year: z.number(),
});

const yearlyActivitySchema = z.object({
    period: z.string(),
    averageBurnedCalories: z.number().nullable(),
    year: z.number(),
});

const activityGraphResponseSchema = z.discriminatedUnion("filter", [
    z.object({
        filter: z.literal("weekly"),
        data: z.array(weeklyActivitySchema),
    }),
    z.object({
        filter: z.literal("monthly"),
        data: z.array(monthlyActivitySchema),
    }),
    z.object({
        filter: z.literal("half_yearly"),
        data: z.array(halfYearlyActivitySchema),
    }),
    z.object({
        filter: z.literal("yearly"),
        data: z.array(yearlyActivitySchema),
    }),
]);

export default activityGraphResponseSchema;