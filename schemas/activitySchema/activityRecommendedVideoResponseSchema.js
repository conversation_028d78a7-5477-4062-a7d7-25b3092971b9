
import { z } from "zod";

const activityRecommendedVideoResponseSchema = z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    isPublished: z.boolean(),
    completionTime: z.number().optional(),
    tag: z.string().optional(),
    subTags: z.array(z.string()).optional(),
    calorieEstimate: z.number().optional(),
    videoUrl: z.string().url(),
    createdAt: z.string().datetime(),
});

export default activityRecommendedVideoResponseSchema;