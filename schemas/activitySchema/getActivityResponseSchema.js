import { z } from "zod";

const getActivityResponseSchema = z.object({
    __v: z.number(),
    _id: z.string(),
    userId: z.string(),
    source: z.string(),
    activityType: z.string(),
    durationInMinutes: z.number(),
    steps: z.number(),
    burnedCalories: z.number(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    isDeleted: z.boolean(),
    localDate: z.string(),
});

export default getActivityResponseSchema;
