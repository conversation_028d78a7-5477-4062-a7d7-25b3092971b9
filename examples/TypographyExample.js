import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Typography } from '../constants/typography';
import { Colors } from '../constants/theme/colors';

/**
 * Example component showing how to use the Typography constants
 */
const TypographyExample = () => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Headings</Text>
        <Text style={Typography.STYLES.H1}>Heading 1</Text>
        <Text style={Typography.STYLES.H2}>Heading 2</Text>
        <Text style={Typography.STYLES.H3}>Heading 3</Text>
        <Text style={Typography.STYLES.H4}>Heading 4</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Body Text</Text>
        <Text style={Typography.STYLES.BODY_LARGE_BOLD}>Body Large Bold</Text>
        <Text style={Typography.STYLES.BODY_LARGE_REGULAR}>Body Large Regular</Text>
        <Text style={Typography.STYLES.BODY_MEDIUM_BOLD}>Body Medium Bold</Text>
        <Text style={Typography.STYLES.BODY_MEDIUM_REGULAR}>Body Medium Regular</Text>
        <Text style={Typography.STYLES.BODY_REGULAR_BOLD}>Body Regular Bold</Text>
        <Text style={Typography.STYLES.BODY_REGULAR}>Body Regular</Text>
        <Text style={Typography.STYLES.BODY_SMALL_BOLD}>Body Small Bold</Text>
        <Text style={Typography.STYLES.BODY_SMALL_REGULAR}>Body Small Regular</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Captions and Small Text</Text>
        <Text style={Typography.STYLES.CAPTION_BOLD}>Caption Bold</Text>
        <Text style={Typography.STYLES.CAPTION_REGULAR}>Caption Regular</Text>
        <Text style={Typography.STYLES.TINY_BOLD}>Tiny Bold</Text>
        <Text style={Typography.STYLES.TINY_REGULAR}>Tiny Regular</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Special Styles</Text>
        <View style={styles.buttonExample}>
          <Text style={Typography.STYLES.BUTTON_TEXT}>Button Text</Text>
        </View>
        <Text style={Typography.STYLES.LINK_TEXT}>Link Text</Text>
        <Text style={Typography.STYLES.ERROR_TEXT}>Error Text</Text>
        <Text style={Typography.STYLES.SUCCESS_TEXT}>Success Text</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom Combinations</Text>
        <Text style={{
          fontFamily: Typography.FONT_FAMILY.EXO.BOLD,
          fontSize: Typography.FONT_SIZE.HEADING_3,
          color: Typography.TEXT_COLOR.PRIMARY,
          textAlign: Typography.TEXT_ALIGN.CENTER,
        }}>
          Custom Text Style
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    padding: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    ...Typography.STYLES.BODY_MEDIUM_BOLD,
    color: Colors.primaryPurple,
    marginBottom: 10,
    textDecorationLine: 'underline',
  },
  buttonExample: {
    backgroundColor: Colors.primaryGreen,
    padding: 10,
    borderRadius: 8,
    marginVertical: 5,
    alignItems: 'center',
  },
});

export default TypographyExample;
