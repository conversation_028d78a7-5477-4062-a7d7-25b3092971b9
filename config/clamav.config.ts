import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as clamav from 'clamav.js';
import { CustomLogger } from 'src/common/services';

@Injectable()
export class ClamAVConfigService implements OnModuleInit {
  private readonly CLAMAV_PORT: number;
  private readonly CLAMAV_HOST: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: CustomLogger,
  ) {
    this.CLAMAV_PORT = Number(this.configService.get('CLAMAV_PORT') || 3310);
    this.CLAMAV_HOST = this.configService.get('CLAMAV_HOST') || 'localhost';
  }

  async onModuleInit() {
    this.logger.log('Initializing ClamAV connection...');
    this.logger.log(`Attempting to connect to ClamAV at ${this.CLAMAV_HOST}:${this.CLAMAV_PORT}`);

    try {
      await this.testClamAVConnection();
    } catch (error) {
      this.logger.error({
        error: true,
        statusCode: 500,
        message: `ClamAV connection failed: ${error.message}`,
        path: 'clamav-config-service',
        errorId: Date.now(),
        timestamp: new Date(),
      });
    }
  }

  private async testClamAVConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      clamav.ping(this.CLAMAV_PORT, this.CLAMAV_HOST, 1000, (err) => {
        if (err) {
          this.logger.warn(`ClamAV: Connection failed - ${err.message}`);
          reject(new Error(`ClamAV not available at ${this.CLAMAV_HOST}:${this.CLAMAV_PORT}`));
        } else {
          this.logger.log(`ClamAV: Connection established at ${this.CLAMAV_HOST}:${this.CLAMAV_PORT}`);
          this.logger.log('ClamAV: Service is ready for virus scanning');
          resolve();
        }
      });
    });
  }

  getClamAVConfig() {
    return {
      host: this.CLAMAV_HOST,
      port: this.CLAMAV_PORT,
    };
  }

  async isClamAVAvailable(): Promise<boolean> {
    try {
      await this.testClamAVConnection();
      return true;
    } catch {
      return false;
    }
  }
}
