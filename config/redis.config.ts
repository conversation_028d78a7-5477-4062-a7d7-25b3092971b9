import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';
import { CustomLogger } from 'src/common/services';

@Injectable()
export class RedisConfigService implements OnModuleInit, OnModuleDestroy {
  private redisClient: Redis;
  private retryCount: number = 0;
  private MaxRetryCount: number = 1;

  constructor(private readonly logger: CustomLogger) {}

  async onModuleInit() {
    this.logger.log('Initializing Redis connection...');

    const redisConfig = {
      type: process.env.REDIS_TYPE,
      url: process.env.REDIS_URL,
    };

    if (!redisConfig.type || !redisConfig.url) {
      console.warn('Redis configuration is missing!');
      return;
    }

    this.logger.log(`Attempting to connect to Redis at ${redisConfig.url}`);

    try {
      this.redisClient = new Redis(redisConfig.url, {
        retryStrategy: (times) => {
          if (this.retryCount >= this.MaxRetryCount) {
            this.logger.log(
              'Max retries reached, stopping reconnection attempts.',
            );
            return null; // Returning null will stop retries
          }

          const delay = Math.min(times * 50, 2000);
          this.retryCount++;
          this.logger.log(`Retrying Redis connection in ${delay}ms...`);
          return delay;
        },
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
      });

      this.redisClient
        .on('connect', () => this.logger.log('Redis: Connection established'))
        .on('error', (err) => console.warn('Redis: Connection error : ' + err))
        .on('ready', () => this.logger.log('Redis: Client is ready'))
        .on('close', () => console.warn('Redis: Connection closed'))
        .on('reconnecting', () =>
          this.logger.log('Redis: Attempting to reconnect'),
        );

      // Wait for ready event
      await new Promise((resolve, reject) => {
        this.redisClient.once('ready', resolve);
        this.redisClient.once('error', reject);
      });

      // Test the connection
      const pingResponse = await this.redisClient.ping();
      this.logger.log(`Redis PING response: ${pingResponse}`);
    } catch (error) {
      this.logger.error(error);
    }
  }

  async onModuleDestroy() {
    if (this.redisClient) {
      this.logger.log('Disconnecting Redis client...');
      await this.redisClient.quit(); // Using quit() instead of disconnect()
      this.logger.log('Redis client disconnected');
    }
  }

  isRedisConnected(): boolean {
    const isConnected = this.redisClient && this.redisClient.status === 'ready';
    this.logger.log(
      `Redis connection status: ${isConnected ? 'connected' : 'disconnected'}`,
    );
    return isConnected;
  }

  getClient(): Redis {
    if (!this.redisClient) {
      throw new Error('Redis client not initialized');
    }
    return this.redisClient;
  }
}
