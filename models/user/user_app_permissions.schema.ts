import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum USER_APP_PERMISSIONS {
  APPLE = 'apple',
  GOOGLE = 'google',
}

@Schema({ timestamps: true })
export class AppPermission extends Document {
  @Prop({ type: String, enum: USER_APP_PERMISSIONS, required: true })
  app_permission: USER_APP_PERMISSIONS;

  @Prop({ type: Boolean, default: false })
  isPermissionAllowed: boolean;

  @Prop({ type: [String], default: [] }) // Array of strings for additional permissions
  otherPermissions: string[];

  createdAt: Date;

  updatedAt: Date;
}

export const AppPermissionSchema = SchemaFactory.createForClass(AppPermission);
