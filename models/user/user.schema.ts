import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { UserGoal, UserGoalSchema } from './user_goals.schema';
import { EncryptionService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import * as argon2 from 'argon2';
import { AppPermission } from './user_app_permissions.schema';

export enum GENDER_TYPES {
  MALE = 'male',
  FEMALE = 'female',
  OTHERS = 'others',
}

export enum ROLE_VALUES {
  ADMIN = 'admin',
  USER = 'user',
}

export enum DIET_PREFERENCE {
  VEG = 'veg',
  NON_VEG = 'non-veg',
  VEGAN = 'vegan',
  OTHERS = 'others',
}

@Schema({ timestamps: true })
export class User extends Document {
  @Prop({ type: String, required: true, unique: true })
  email: string;

  @Prop({
    type: String,
    required: true,
    minlength: [8, 'password must be of atleast 8 letters !!'],
    select: false,
  })
  password: string;

  @Prop({ type: String, enum: ROLE_VALUES, default: ROLE_VALUES.USER })
  role: ROLE_VALUES;

  @Prop({
    type: String,
    required: false,
    minlength: [3, 'name must be of atleast 3 letters !!'],
  })
  name: string;

  @Prop({ type: String, default: null })
  timeZone: string;

  @Prop({ type: Boolean, default: false })
  isEmailVerified: boolean;

  @Prop({ type: Boolean, default: false })
  isAccountCompleted: boolean;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  @Prop({ type: Number, required: false, min: 0 })
  age: number;

  @Prop({ type: String, enum: GENDER_TYPES, required: false })
  gender: GENDER_TYPES;

  @Prop({ type: Number, required: false, min: 0 })
  height: number;

  @Prop({ type: Number, required: false, min: 0 })
  weight: number;

  @Prop({ type: Boolean, required: true })
  acceptTerms: boolean;

  @Prop({
    type: String,
    enum: DIET_PREFERENCE,
    required: false,
  })
  diet_preference: DIET_PREFERENCE;

  @Prop({ type: [UserGoalSchema], default: [] })
  goals: UserGoal[];

  @Prop({
    type: Number,
    required: false,
    default: null,
    validate: {
      validator: (value: number | null) => {
        if (value === null || value === undefined) return true;
        return value >= 2 && value <= 3;
      },
      message: 'deviceUsageLimit must be null or a number between 2 and 3',
    },
  })
  deviceUsageLimit: number | null;

  @Prop({
    type: [{ type: Types.ObjectId, ref: AppPermission.name }],
    default: [],
  })
  app_permissions: Types.ObjectId[];

  createdAt: Date;

  updatedAt: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);

UserSchema.pre('save', async function (next) {
  const user = this as any;
  const encryptionService = new EncryptionService(new ConfigService());

  if (user.email) {
    user.email = encryptionService.encrypt(user.email);
  }

  if (user.password) {
    user.password = await argon2.hash(user.password);
  }

  next();
});
