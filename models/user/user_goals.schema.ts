import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum GOAL_TYPES {
  PHYSICAL = 'physical',
  MOVEMENT = 'movement',
  MINDFULNESS = 'mindfulness',
  SLEEP = 'sleep',
}

@Schema({ timestamps: true })
export class UserGoal extends Document {
  @Prop({ type: String, enum: GOAL_TYPES, required: true })
  goal_type: GOAL_TYPES;

  @Prop({ type: String, required: true })
  selected_goal: string;

  createdAt: Date;

  updatedAt: Date;
}

export const UserGoalSchema = SchemaFactory.createForClass(UserGoal);
