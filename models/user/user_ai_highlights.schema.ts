import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum AI_LABELS {
  UPDATE = 'update', // requires user id
  FUN_FACT = 'fun_fact', // do not requires user id => public for all users
}

@Schema({ timestamps: true })
export class UserA<PERSON><PERSON>ighlights extends Document {
  @Prop({ type: Types.ObjectId, required: false })
  userId: Types.ObjectId;

  @Prop({ type: String, required: true })
  heading: string;

  @Prop({ type: String, required: false })
  subHeading: string;

  @Prop({ type: String, required: false })
  description: string;

  @Prop({ type: String, enum: AI_LABELS, required: true })
  label: string;

  createdAt: Date;

  updatedAt: Date;
}

export const UserAIHighlightsSchema =
  SchemaFactory.createForClass(UserAIHighlights);
