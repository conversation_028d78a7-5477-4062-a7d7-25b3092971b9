import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum CAROUSEL_TAGS {
  TODAYTIP = "Today's Tip",
  UPDATE = 'Update',
}

@Schema({ timestamps: true })
export class Carousel extends Document {
  @Prop({ type: String, required: true })
  userId: string;

  @Prop({ type: String, enum: CAROUSEL_TAGS, required: true })
  tag: CAROUSEL_TAGS;

  @Prop({ type: String })
  image: string;

  @Prop({ type: Date, required: true })
  date: Date;

  @Prop({ type: String, required: true, minlength: 3 })
  greetings: string;

  @Prop({ type: String, required: true, minlength: 3 })
  subheading: string;

  @Prop({ type: String, required: true, minlength: 5 })
  content: string;

  createdAt: Date;
  updatedAt: Date;
}

export const CarouselSchema = SchemaFactory.createForClass(Carousel);

CarouselSchema.index({ userId: 1, date: 1 });
