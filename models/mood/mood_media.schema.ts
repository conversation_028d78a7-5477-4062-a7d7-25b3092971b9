import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HUNGER_LEVELS, MOOD_TYPES } from 'models/user-records/Mood-Records';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class MoodMedia extends Document {
  @Prop({ type: String, required: true })
  title: string;

  @Prop({ type: String, required: true })
  description: string;

  @Prop({ type: String, enum: MOOD_TYPES, required: true })
  moodType: string;

  @Prop({ type: String, enum: HUNGER_LEVELS, required: true })
  hungerLevel: string;

  @Prop({ type: String, required: true })
  videoUrl: string;

  @Prop({ type: Boolean, default: false })
  isPublished: boolean;

  @Prop({ type: Boolean, default: false })
  isRecommended: boolean;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const MoodMediaSchema = SchemaFactory.createForClass(MoodMedia);

MoodMediaSchema.index({ moodType: 1, hungerLevel: 1 });
