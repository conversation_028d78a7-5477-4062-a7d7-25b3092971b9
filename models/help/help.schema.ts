import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

class HelpTopic {
  @Prop({ type: String, required: true })
  id: string;

  @Prop({ type: String, required: true, minlength: 3 })
  title: string;

  @Prop({ type: String, required: true, minlength: 3 })
  description: string;
}

export enum CATEGORY_TYPES {
  DEVICE = 'device',
  LOGIN = 'login',
  SUPPORT = 'support',
  TROUBLESHOOTING = 'troubleshooting',
  ACCOUNT = 'account',
  NOTIFICATIONS = 'notifications',
  SECURITY = 'security',
}

@Schema({ timestamps: true })
export class Help extends Document {
  @Prop({ type: String, enum: CATEGORY_TYPES, required: true })
  category: CATEGORY_TYPES;

  @Prop({ type: [HelpTopic], default: [] })
  topics: HelpTopic[];

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const HelpSchema = SchemaFactory.createForClass(Help);
