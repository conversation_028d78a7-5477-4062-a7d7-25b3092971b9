import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum ACTIVITY_TAGS {
  WORKOUTS = 'workouts',
  WELLNESS = 'wellness',
}

export const TagSubTagMap: Record<ACTIVITY_TAGS, string[]> = {
  [ACTIVITY_TAGS.WELLNESS]: ['meditation', 'personal_hygiene'],
  [ACTIVITY_TAGS.WORKOUTS]: [
    'exercise',
    'yoga',
    'stretch',
    'strength',
    'abs',
    'cardio',
    'balance',
    'flexibility',
    'endurance',
  ],
};

@Schema({ timestamps: true })
export class Activity extends Document {
  @Prop({ type: String, required: true })
  title: string;

  @Prop({ type: String, required: true })
  description: string;

  @Prop({ type: Boolean, required: true })
  isPublished: boolean;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  @Prop({ type: Number, required: true })
  completionTime: number;

  @Prop({ type: String, enum: Object.values(ACTIVITY_TAGS), required: true })
  tag: ACTIVITY_TAGS;

  @Prop({ type: [String], required: true })
  subTags: string[];

  @Prop({ type: Number, required: true })
  calorieEstimate: number;

  @Prop({ type: String, required: true })
  videoUrl: string;

  createdAt: Date;

  updatedAt: Date;
}

export const ActivitySchema = SchemaFactory.createForClass(Activity);
