import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum SERVING_NUTRITION_QUANTITY {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

export enum SLICE_NUTRITION_QUANTITY {
  SLICE1 = 's1',
  SLICE2 = 's2',
  SLICE3 = 's3',
  SLICE4 = 's4',
}

export enum PIECE_NUTRITION_QUANTITY {
  PIECE1 = 'p1',
  PIECE2 = 'p2',
  PIECE3 = 'p3',
  PIECE4 = 'p4',
}

@Schema({ timestamps: true })
export class RecipesNutritionByQuantity extends Document {
  @Prop({
    type: String,
    enum: [
      ...Object.values(SERVING_NUTRITION_QUANTITY),
      ...Object.values(SLICE_NUTRITION_QUANTITY),
      ...Object.values(PIECE_NUTRITION_QUANTITY),
    ],
    required: true,
  })
  quantity:
    | SERVING_NUTRITION_QUANTITY
    | SLICE_NUTRITION_QUANTITY
    | PIECE_NUTRITION_QUANTITY;

  @Prop({ type: Number, required: true, min: 0 })
  protein: number;

  @Prop({ type: Number, required: true, min: 0 })
  calories: number;

  @Prop({ type: Number, required: true, min: 0 })
  fats: number;

  @Prop({ type: Number, required: true, min: 0 })
  fiber: number;

  @Prop({ type: Number, required: true, min: 0 })
  carbs: number;

  createdAt: Date;

  updatedAt: Date;
}

export const RecipesNutritionByQuantitySchema = SchemaFactory.createForClass(
  RecipesNutritionByQuantity,
);
