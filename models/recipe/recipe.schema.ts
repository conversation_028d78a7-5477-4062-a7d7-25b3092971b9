import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types, Schema as MongooseSchema } from 'mongoose';
import {
  RecipesNutritionByQuantity,
  RecipesNutritionByQuantitySchema,
} from './nutrition_by_quantity.schema';
import { DIET_PREFERENCE, User } from 'models/user';

export enum MEAL_TYPES {
  BREAKFAST = 'breakfast',
  LUNCH = 'lunch',
  DINNER = 'dinner',
  SNACKS = 'snacks',
}

@Schema({ timestamps: true })
export class Recipes extends Document {
  @Prop({ type: String, required: true })
  title: string;

  @Prop({
    type: [MongooseSchema.Types.Mixed],
  })
  ingredients: (string | Types.ObjectId)[];

  @Prop({ type: String })
  directions: string;

  @Prop({ type: Number })
  timeToPrep: number; // always in minutes

  @Prop({ type: Number, default: 1 })
  numOfServings: number;

  @Prop({ type: String })
  thumbnailUrl: string;

  @Prop({ type: String, enum: MEAL_TYPES })
  mealType: string;

  @Prop({ type: String, enum: DIET_PREFERENCE })
  category: string;

  @Prop({ type: [RecipesNutritionByQuantitySchema], required: true })
  nutritionByQuantity: RecipesNutritionByQuantity[];

  @Prop({ type: Boolean, default: true })
  isPublished: boolean;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  // for visibility => for admin recipes it will be null and for user recipes it will be user Id
  @Prop({ type: Types.ObjectId, required: false, ref: User.name })
  author: Types.ObjectId;

  createdAt: Date;

  updatedAt: Date;
}

export const RecipesSchema = SchemaFactory.createForClass(Recipes);
