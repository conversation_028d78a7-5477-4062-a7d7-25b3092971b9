import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { EncryptionService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import { User } from 'models/user';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class AdminOtpToken extends Document<Types.ObjectId> {
  @Prop({ type: Types.ObjectId, ref: User.name, required: true })
  userId: Types.ObjectId;

  @Prop({ required: true })
  otp: string;

  @Prop({ required: true })
  expiry: Date;

  @Prop({ type: Boolean, default: false })
  isExpired: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const AdminOtpTokenSchema = SchemaFactory.createForClass(AdminOtpToken);

AdminOtpTokenSchema.pre('save', async function (next) {
  const token = this as AdminOtpToken;
  const encryptionService = new EncryptionService(new ConfigService());

  if (token.otp) {
    token.otp = encryptionService.encrypt(token.otp);
  }

  next();
});

AdminOtpTokenSchema.index({ userId: 1 });
