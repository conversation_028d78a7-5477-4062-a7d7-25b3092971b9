import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Faq extends Document {
  @Prop({ type: String, required: true, minlength: 3 })
  question: string;

  @Prop({ type: String, required: true, minlength: 3 })
  answer: string;

  // topic wise question answer

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const FaqSchema = SchemaFactory.createForClass(Faq);
