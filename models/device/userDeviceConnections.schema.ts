import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user';
import { Document, Types } from 'mongoose';
import { Device } from './device.schema';

export enum DEVICE_CONNECTION_STATUS {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
}

@Schema({ timestamps: true })
export class UserDeviceConnections extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: Device.name })
  deviceId: Types.ObjectId;

  @Prop({ type: String, required: true })
  serialId: string;

  @Prop({
    type: String,
    enum: DEVICE_CONNECTION_STATUS,
    default: DEVICE_CONNECTION_STATUS.DISCONNECTED,
  })
  connectionStatus: string;

  createdAt: Date;

  updatedAt: Date;
}

export const UserDeviceConnectionsSchema = SchemaFactory.createForClass(
  UserDeviceConnections,
);
