import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum DEVICE_NAMES {
  GVT = 'gvt',
}

export enum DEVICE_TYPES {
  MASSAGE = 'massage',
}

@Schema({ timestamps: true })
export class Device extends Document {
  @Prop({ type: String, enum: DEVICE_NAMES, required: true })
  name: DEVICE_NAMES;

  @Prop({ type: String, required: true })
  description: string;

  @Prop({ type: String, required: true })
  deviceGuide: string;

  @Prop({ type: String, required: true })
  version: string;

  @Prop({ type: String, enum: DEVICE_TYPES, required: true })
  type: DEVICE_TYPES;

  @Prop({ type: String, required: true })
  thumbnailUrl: string;

  @Prop({ type: [String], required: true })
  serialIds: string[];

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const DeviceSchema = SchemaFactory.createForClass(Device);
