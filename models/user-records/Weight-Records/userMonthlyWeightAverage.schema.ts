import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user/user.schema';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class MonthlyWeightSummary extends Document {
  @Prop({ type: Types.ObjectId, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Number, required: true })
  month: number;

  @Prop({ type: Number, required: true })
  year: number;

  @Prop({ type: Number, required: true })
  averageWeight: number;

  createdAt: Date;

  updatedAt: Date;
}

export const MonthlyWeightSummarySchema =
  SchemaFactory.createForClass(MonthlyWeightSummary);
