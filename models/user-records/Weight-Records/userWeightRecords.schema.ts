import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user/user.schema';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class UserWeightRecords extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Number, required: true })
  weight: number;

  @Prop({ type: Date, required: true })
  date: Date;

  createdAt: Date;

  updatedAt: Date;
}

export const UserWeightRecordsSchema =
  SchemaFactory.createForClass(UserWeightRecords);
