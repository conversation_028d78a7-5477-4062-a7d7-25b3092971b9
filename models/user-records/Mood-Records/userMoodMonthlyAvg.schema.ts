import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user/user.schema';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class MonthlyMoodAverage extends Document {
  @Prop({ type: Types.ObjectId, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Number, required: true })
  month: number;

  @Prop({ type: Number, required: true })
  year: number;

  @Prop({
    type: Number,
    required: true,
  })
  moodTypeScore: number;

  @Prop({ required: true, type: Number })
  hungerLevelScore: number;

  createdAt: Date;

  updatedAt: Date;
}

export const MonthlyMoodSchema =
  SchemaFactory.createForClass(MonthlyMoodAverage);
