import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { DailyMoodAverage } from './userMoodDailyAvg.schema';
import { User } from 'models/user/user.schema';

// mood-highlight.schema.ts
@Schema({ timestamps: true })
export class MoodH<PERSON>light extends Document {
  @Prop({ type: Types.ObjectId, ref: User.name, required: true })
  userId: Types.ObjectId;

  @Prop({ type: [String], default: [] })
  highlight_text: string[];

  @Prop({ type: Types.ObjectId, ref: DailyMoodAverage.name })
  record_id: Types.ObjectId;

  @Prop()
  record_createdAt: Date;

  @Prop()
  record_updatedAt: Date;

  createdAt: Date;

  updatedAt: Date;
}

export const MoodHighlightSchema = SchemaFactory.createForClass(MoodHighlight);
