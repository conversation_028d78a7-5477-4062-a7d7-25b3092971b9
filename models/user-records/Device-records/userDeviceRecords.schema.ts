import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'models/user/user.schema';

// Per Day Record of device
@Schema({ timestamps: true })
export class UserDeviceRecord extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Number })
  deviceUsageLimit: number;

  @Prop({ type: Number, default: 0 })
  totalUsageTime: number;

  @Prop({ type: String })
  localDate: string;

  @Prop({ type: Date })
  utcDate: Date;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const UserDeviceRecordSchema =
  SchemaFactory.createForClass(UserDeviceRecord);
