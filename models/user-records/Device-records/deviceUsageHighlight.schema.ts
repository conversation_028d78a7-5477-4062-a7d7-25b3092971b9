import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'models/user/user.schema';

@Schema({ timestamps: true })
export class DeviceUsageHighlight extends Document {
  @Prop({ type: Types.ObjectId, ref: User.name, required: true })
  userId: Types.ObjectId;

  @Prop({ type: [String], required: true })
  highlight_text: string[];

  @Prop({ type: Types.ObjectId, required: true })
  record_id: Types.ObjectId;

  @Prop({ type: Date, required: true })
  record_createdAt: Date;

  @Prop({ type: Date, required: true })
  record_updatedAt: Date;

  createdAt: Date;

  updatedAt: Date;
}

export const DeviceUsageHighlightSchema =
  SchemaFactory.createForClass(DeviceUsageHighlight);
