import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user/user.schema';
import { Document, Types } from 'mongoose';

// monthly device records avg
@Schema({ timestamps: true })
export class MonthlyDeviceUsageSummary extends Document {
  @Prop({ type: Types.ObjectId, ref: User.name, required: true })
  userId: Types.ObjectId;

  @Prop({ type: Number, required: true })
  month: number;

  @Prop({ type: Number, required: true })
  year: number;

  @Prop({ type: Number, required: true })
  averageDeviceUsage: number;

  createdAt: Date;

  updatedAt: Date;
}

export const MonthlyDeviceUsageSummarySchema = SchemaFactory.createForClass(
  MonthlyDeviceUsageSummary,
);
