import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user/user.schema';
import { Document, Types } from 'mongoose';

// each user device record
@Schema({ timestamps: true })
export class DeviceUsageSession extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Date, required: true })
  startTime: Date;

  @Prop({ type: Number, required: true })
  durationSet: number;

  @Prop({ type: Number, default: 0 })
  durationConsumed: number;

  @Prop({ type: Date })
  endTime: Date;

  @Prop({ type: Boolean, default: false })
  isSessionExpired: boolean;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const DeviceUsageSessionSchema =
  SchemaFactory.createForClass(DeviceUsageSession);
