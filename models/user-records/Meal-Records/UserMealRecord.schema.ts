import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { MealEntry, MealEntrySchema } from './UserMealEntry.schema';
import { User } from 'models/user/user.schema';

export enum MEAL_NAMES {
  BREAKFAST = 'Breakfast',
  LUNCH = 'Lunch',
  DINNER = 'Dinner',
  MID_MORNING_SNACK = 'Mid-Morning Snack',
  AFTERNOON_SNACK = 'Afternoon Snack',
  LATE_NIGHT_SNACK = 'Late Night Snack',
}

@Schema({ timestamps: true })
export class UserMealRecords extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: String, enum: MEAL_NAMES, required: true })
  mealName: MEAL_NAMES;

  @Prop({ type: Date, required: true })
  date: Date; // stores user local date

  @Prop({ type: String, required: true })
  mealTime: string;

  @Prop({ type: [MealEntrySchema], default: [] })
  meals: MealEntry[];

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  createdAt: Date; // stores created At date with timeStamps in UTC
  updatedAt: Date; // stores updated At date with timeStamps in UTC
}

export const UserMealRecordsSchema =
  SchemaFactory.createForClass(UserMealRecords);
