import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  SERVING_NUTRITION_QUANTITY,
  SLICE_NUTRITION_QUANTITY,
  PIECE_NUTRITION_QUANTITY,
} from '../../recipe';
import { Types } from 'mongoose';

export enum NUTRITION_MEASUREMENT {
  GRAMS = 'grams',
  MILLIGRAMS = 'milligrams',
  OZ = 'oz',
}

@Schema({ timestamps: true })
export class MealEntry {
  @Prop({ type: Types.ObjectId, required: true })
  recipeId: Types.ObjectId;

  @Prop({
    type: String,
    enum: [
      ...Object.values(SERVING_NUTRITION_QUANTITY),
      ...Object.values(SLICE_NUTRITION_QUANTITY),
      ...Object.values(PIECE_NUTRITION_QUANTITY),
    ],
    required: true,
  })
  quantity:
    | SERVING_NUTRITION_QUANTITY
    | SLICE_NUTRITION_QUANTITY
    | PIECE_NUTRITION_QUANTITY;

  @Prop({ type: String, enum: NUTRITION_MEASUREMENT, required: true })
  measurement: NUTRITION_MEASUREMENT;

  @Prop({ type: String, required: true })
  title: string;

  @Prop({ type: String })
  thumbnailUrl: string;

  @Prop({ type: Number, default: 0 })
  protein: number;

  @Prop({ type: Number, default: 0 })
  calories: number;

  @Prop({ type: Number, default: 0 })
  fats: number;

  @Prop({ type: Number, default: 0 })
  fiber: number;

  @Prop({ type: Number, default: 0 })
  carbs: number;

  createdAt: Date;

  updatedAt: Date;
}

export const MealEntrySchema = SchemaFactory.createForClass(MealEntry);
