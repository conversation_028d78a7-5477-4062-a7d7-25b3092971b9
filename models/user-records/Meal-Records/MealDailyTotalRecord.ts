import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class MealDailyTotalRecord extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Date, required: true })
  date: Date;

  @Prop({ type: Number, required: true })
  month: number;

  @Prop({ type: Number, required: true })
  year: number;

  @Prop({ type: Number, default: 0 })
  calories: number;

  @Prop({ type: Number, default: 0 })
  protein: number;

  @Prop({ type: Number, default: 0 })
  fats: number;

  @Prop({ type: Number, default: 0 })
  fiber: number;

  @Prop({ type: Number, default: 0 })
  carbs: number;

  createdAt: Date;

  updatedAt: Date;
}

export const MealDailyTotalRecordSchema =
  SchemaFactory.createForClass(MealDailyTotalRecord);
