import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'models/user/user.schema';

@Schema({ timestamps: true })
export class MealDailyAverageRecords extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Number, required: true })
  month: number;

  @Prop({ type: Number, required: true })
  year: number;

  @Prop({ type: Date, required: true })
  date: Date;

  @Prop({ type: Number, default: 0 })
  protein: number;

  @Prop({ type: Number, default: 0 })
  calories: number;

  @Prop({ type: Number, default: 0 })
  fats: number;

  @Prop({ type: Number, default: 0 })
  fiber: number;

  @Prop({ type: Number, default: 0 })
  carbs: number;

  createdAt: Date;

  updatedAt: Date;
}

export const MealDailyAverageRecordsSchema = SchemaFactory.createForClass(
  MealDailyAverageRecords,
);
