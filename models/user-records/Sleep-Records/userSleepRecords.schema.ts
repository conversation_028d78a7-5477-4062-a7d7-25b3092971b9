import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user/user.schema';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class UserSleepRecords extends Document {
  @Prop({ type: Types.ObjectId, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Date, required: true })
  date: Date;

  @Prop({ type: Number, required: true })
  numOfHours: number;

  createdAt: Date;

  updatedAt: Date;
}

export const UserSleepRecordsSchema =
  SchemaFactory.createForClass(UserSleepRecords);
