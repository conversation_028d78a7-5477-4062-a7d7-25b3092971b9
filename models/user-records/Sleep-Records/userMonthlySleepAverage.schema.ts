import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user/user.schema';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class MonthlySleepSummary extends Document {
  @Prop({ type: Types.ObjectId, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Number, required: true }) // Store in YYYYMM format
  month: number;

  @Prop({ type: Number, required: true }) // Store year separately
  year: number;

  @Prop({ type: Number, required: true }) // Monthly average sleep duration
  averageSleep: number;

  createdAt: Date;

  updatedAt: Date;
}

export const MonthlySleepSummarySchema =
  SchemaFactory.createForClass(MonthlySleepSummary);
