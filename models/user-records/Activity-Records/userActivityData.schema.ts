import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ timestamps: true })
export class UserActivityRecords_ActivityData {
  @Prop({ type: Number, min: 0 })
  energyLevel: number;

  @Prop({ type: Number, min: 0 })
  steps: number;

  @Prop({ type: Number, min: 0 })
  heartRate: number;

  createdAt: Date;

  updatedAt: Date;
}

export const UserActivityRecords_ActivityDataSchema =
  SchemaFactory.createForClass(UserActivityRecords_ActivityData);
