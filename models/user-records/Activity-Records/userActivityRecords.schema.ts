import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import {
  UserActivityRecords_ActivityData,
  UserActivityRecords_ActivityDataSchema,
} from './userActivityData.schema';
import { User } from 'models/user/user.schema';
import { UserDeviceConnections } from 'models/device';
import { Activity } from 'models/activity';

@Schema({ timestamps: true })
export class UserActivityRecords extends Document {
  @Prop({ type: Types.ObjectId, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: UserDeviceConnections.name })
  userDeviceConnectionId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: Activity.name })
  activityId: Types.ObjectId;

  @Prop({ type: Date, required: true })
  date: Date;

  @Prop({ type: Boolean, required: true })
  isCompleted: boolean;

  @Prop({ type: UserActivityRecords_ActivityDataSchema })
  activityData: UserActivityRecords_ActivityData;

  createdAt: Date;

  updatedAt: Date;
}

export const UserActivityRecordsSchema =
  SchemaFactory.createForClass(UserActivityRecords);
