import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum STANDARD_QUANTITY_TYPE {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

export enum PIECE_QUANTITY_TYPE {
  PIECES_1 = '1_piece',
  PIECES_2 = '2_pieces',
  PIECES_3 = '3_pieces',
  PIECES_4 = '4_pieces',
}

@Schema({ timestamps: true })
export class IngredientNutritionByQuantity extends Document {
  @Prop({
    type: String,
    enum: [
      ...Object.values(STANDARD_QUANTITY_TYPE),
      ...Object.values(PIECE_QUANTITY_TYPE),
    ],
    required: true,
  })
  quantity: STANDARD_QUANTITY_TYPE | PIECE_QUANTITY_TYPE;

  @Prop({ type: Number, required: true, min: 0 })
  protein: number;

  @Prop({ type: Number, required: true, min: 0 })
  calories: number;

  @Prop({ type: Number, required: true, min: 0 })
  fats: number;

  @Prop({ type: Number, required: true, min: 0 })
  fiber: number;

  @Prop({ type: Number, required: true, min: 0 })
  carbs: number;

  createdAt: Date;
  updatedAt: Date;
}

export const IngredientNutritionByQuantitySchema = SchemaFactory.createForClass(
  IngredientNutritionByQuantity,
);
