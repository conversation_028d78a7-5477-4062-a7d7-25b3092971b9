import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { IngredientNutritionByQuantity } from './ingredient_nutrition_by_quantity.schema';

@Schema({ timestamps: true })
export class Ingredient extends Document {
  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: String, required: true })
  thumbnailUrl: string;

  @Prop({ type: [IngredientNutritionByQuantity], required: true })
  ingredientNutritionByQuantity: IngredientNutritionByQuantity[];

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  createdAt: Date;
  updatedAt: Date;
}

export const IngredientSchema = SchemaFactory.createForClass(Ingredient);
