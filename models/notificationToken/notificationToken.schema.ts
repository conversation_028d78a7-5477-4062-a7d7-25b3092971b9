import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class NotificationToken extends Document {
  @Prop({ type: String, required: true })
  userId: string;

  @Prop({ type: String, required: true })
  deviceId: string;

  @Prop({ type: String, required: true })
  notificationToken: string;

  @Prop({ type: Boolean, required: true, default: true })
  isNotificationActive: boolean; // tells that is device in app notification is enabled or not

  @Prop({ type: Boolean, required: true, default: true })
  isActive: boolean; // tells that device is active or not.

  @Prop({ type: String, required: true })
  osType: string; // defines os of user hand-held device ex : android, ios, windows .etc.

  @Prop({ type: String, required: true })
  deviceType: string; // defines type of user hand-held device ex: mobile, tablet, ipad .etc.

  createdAt: Date;

  updatedAt: Date;
}

export const NotificationTokenSchema =
  SchemaFactory.createForClass(NotificationToken);

NotificationTokenSchema.index(
  { userId: 1, deviceId: 1, notificationToken: 1 },
  { unique: true },
);
