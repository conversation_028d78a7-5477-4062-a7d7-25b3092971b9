import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class NotificationCounter extends Document {
  @Prop({ type: String, required: true })
  deviceId: string;

  @Prop({ type: String, required: true })
  userId: string;

  @Prop({ type: String, required: true })
  notificationTokenId: string;

  @Prop({ type: Number, default: 0 })
  counter: number;

  createdAt: Date;

  updatedAt: Date;
}

export const NotificationCounterSchema =
  SchemaFactory.createForClass(NotificationCounter);
