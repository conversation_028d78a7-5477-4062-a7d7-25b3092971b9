import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class ReminderCategory extends Document<Types.ObjectId> {
  @Prop({ type: Types.ObjectId, ref: User.name, required: true })
  userId: Types.ObjectId;

  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  @Prop({ type: Boolean, default: true })
  isActive: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const ReminderCategorySchema =
  SchemaFactory.createForClass(ReminderCategory);
