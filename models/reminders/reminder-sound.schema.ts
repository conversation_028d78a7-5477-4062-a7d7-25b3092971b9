import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Reminders_Sound extends Document {
  @Prop({ type: String, required: true })
  ios: string;

  @Prop({ type: String, required: true })
  android: string;

  createdAt: Date;

  updatedAt: Date;
}

export const Reminders_SoundSchema =
  SchemaFactory.createForClass(Reminders_Sound);
