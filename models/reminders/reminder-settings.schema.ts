import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {
  Reminders_Sound,
  Reminders_SoundSchema,
} from './reminder-sound.schema';
import { Types } from 'mongoose';
import { ReminderCategory } from './reminder-category.schema';
import { User } from 'models/user';

@Schema({ timestamps: true })
export class ReminderSettings extends Document {
  @Prop({ type: Types.ObjectId, ref: ReminderCategory.name, required: true })
  categoryId: Types.ObjectId;

  @Prop({ type: String, required: true })
  categoryString: string;

  @Prop({ type: String, required: true })
  label: string;

  @Prop({ type: Reminders_SoundSchema, required: true })
  sound: Reminders_Sound;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  @Prop({ type: String, ref: User.name, required: true })
  userId: Types.ObjectId;

  @Prop({ type: Date, required: true })
  time: Date;

  @Prop({ type: String, required: true })
  frontend_screen_url: string;

  createdAt: Date;

  updatedAt: Date;
}

export const RemindersSettingsSchema =
  SchemaFactory.createForClass(ReminderSettings);
