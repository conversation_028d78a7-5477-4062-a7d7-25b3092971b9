# Server Config
SERVER_PORT = 5002
NODE_ENV = 'development'

# Development Mongo DB config
MONGO_USERNAME = ssedghi
MONGO_PASSWORD = pQ6sa2a7sCzbaCKX
MONGO_HOST =cluster0.bzfqe2q.mongodb.net
MONGO_PORT = '27017'
MONGO_DATABASE = 'appetec_db?retryWrites=true&w=majority&appName=Cluster0'

# Microservice
CHATBOT_MICROSERVICE_API_KEY = MICROSERVICE_API_KEY_558844

HEALTH_MICROSERVICE_API_KEY = MICROSERVICE_API_KEY_998877
HEALTH_MICROSERVICE_URL = https://appetec.boilerplate.solutions/health-api

# Apptec-server URL
APPETEC_SERVER_URL = https://appetec.boilerplate.solutions/api

#chatbot session expirey
CHATBOT_SESSION_EXPIRY = 86400000 

# Crypto Key
CRYPTO_KEY=9e8f8b1acdf21a1b6d03f83db0fe2b15a3ddaa0102030405060708090a0b0c0d

# Open AI Key
OPENAI_API_KEY = ********************************************************************************************************************************************************************

# Redis config
REDIS_TYPE = 'single'
REDIS_URL = 'redis://localhost:6379'

#Admin Contact Informantion
ADMIN_EMAIL_INFO=<EMAIL>
ADMIN_PHONE_INFO=+919999999999

CHATBOT_PROMPT_TEMPLATE_HELP="You are a helpful assistant. Use the provided help topics to respond clearly.\n\nTopics:\n${JSON.stringify(helpTopics)}\n\nConversation context: ${JSON.stringify(chatHistory)}\n\nUser's question: ${question}\n\nInstructions:\n- Only use the help topics provided above to respond.\n- Be clear and concise.\n- Answer the user's query directly.\n- Use a friendly and supportive tone.\n- Do not make up data or go beyond the help topics."

CHATBOT_PROMPT_TEMPLATE_FAQ="You are a FAQ assistant.\nHere are the frequently asked questions and their answers:\n${JSON.stringify(faqList)}\n\nConversation context: ${JSON.stringify(chatHistory)}\n\nUser's question: ${question}\n\nInstructions:\n- Use only the provided FAQs.\n- Match the user's question as closely as possible.\n- Be brief and to the point.\n- Answer as clearly as possible without adding extra content."
