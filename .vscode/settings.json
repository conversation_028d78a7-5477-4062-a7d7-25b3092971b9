{
    "window.commandCenter": 1,
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.detectIndentation": false,
    "[javascript]": {
        "editor.formatOnSave": true
    },
    "[javascriptreact]": {
        "editor.formatOnSave": true
    },
    "[typescript]": {
        "editor.formatOnSave": true
    },
    "[typescriptreact]": {
        "editor.formatOnSave": true
    },
    "[css]": {
        "editor.defaultFormatter": null
    },
    "[scss]": {
        "editor.defaultFormatter": null
    },
    "workbench.iconTheme": "material-icon-theme",
    "editor.wordWrap": "on",
    "diffEditor.ignoreTrimWhitespace": true,
    "liveServer.settings.donotShowInfoMsg": true,
    "eslint.codeActionsOnSave.rules": null,
    // "eslint.format.enable": false,
    // "vs-code-prettier-eslint.prettierLast": true,
    "eslint.enable": true
}