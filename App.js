import 'react-native-gesture-handler';
import { enableScreens } from 'react-native-screens';
enableScreens();
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { AuthProvider } from './context/AuthContext';
import { AppContent } from './navigations/AppContent';
import { FontLoad } from 'constants/fontLoad';
import { Colors } from 'constants/theme/colors';
import { useKeepAwake } from 'expo-keep-awake';
import { CustomLoader } from 'components/CustomAction';
import NetInfo from '@react-native-community/netinfo';
import * as Notifications from "expo-notifications";
import { StatusBar } from 'expo-status-bar';
import { DefaultTheme, NavigationContainer } from '@react-navigation/native';
import { linkingConfig } from 'navigations/LinkingConfig';
import * as Linking from 'expo-linking';
import Toast, { BaseToast, ErrorToast } from 'react-native-toast-message';
import { navigationRef } from './navigations/RootNavigation';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;

import { KeyboardProvider } from 'react-native-keyboard-controller';
import AppModalWrapper from 'navigations/components/Layouts/AppModalWrapper';
import { ThemeFonts } from 'constants/theme/fonts';
import { performance } from '@shopify/react-native-performance';

const toastConfig = {
  success: (props) => (
    <BaseToast
      {...props}
      style={{ height: 'auto', minHeight: 60, paddingVertical: 8, borderLeftColor: Colors.primaryGreen, }}
      text1Style={{ fontFamily: ThemeFonts.Exo_700, color: Colors.primaryPurple, fontSize: 16 }}
      text1NumberOfLines={1}
      text2Style={{ fontFamily: ThemeFonts.Exo_500, color: Colors.primaryPurple, fontSize: 14 }}
      text2NumberOfLines={0}
    />
  ),
  error: (props) => (
    <ErrorToast
      {...props}
      style={{ height: 'auto', minHeight: 60, paddingVertical: 8, borderLeftColor: Colors.lightOrange }}
      text1Style={{ fontFamily: ThemeFonts.Exo_700, color: Colors.primaryPurple, fontSize: 16 }}
      text1NumberOfLines={1}
      text2Style={{ fontFamily: ThemeFonts.Exo_500, color: Colors.primaryPurple, fontSize: 14 }}
      text2NumberOfLines={0}
    />
  ),
};


Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

SplashScreen.preventAutoHideAsync();
SplashScreen.setOptions({
  duration: 1000,
  fade: true,
  backgroundColor: Colors.primaryGreen,
  imageWidth: 200,
});
export default function App() {
  // const markStart = performance.mark("app_start");
  // performance.mark("app_start");
  // global.appStartTime = performance.now();

  // Then in your first screen's useEffect
  // useEffect(() => {
  //   const markEnd = performance.mark("home_screen_rendered");
  //   performance.measure("App Startup", "app_start", "home_screen_rendered");
  // }, []);


  const fontsLoaded = FontLoad();
  const [isServerConnected, setIsServerConnected] = useState(false);
  const [isInternetConnected, setIsInternetConnected] = useState(true);
  const [loading, setLoading] = useState(true);

  useKeepAwake();

  // Check for internet connection
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsInternetConnected(state.isConnected);
    });

    return () => unsubscribe();
  }, []);

  // Check for server connection
  const checkServerConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${apiUrl}/health`);
      setIsServerConnected(response.ok);
    } catch (error) {
      setIsServerConnected(false);
    }
    setLoading(false);
  };


  useEffect(() => {
    // Set up notification listeners
    const notificationListener = Notifications.addNotificationReceivedListener(() => {
      // We're not using the notification data, just setting up the listener
    });

    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      const redirectURL = response.notification.request.content.data.url;
      Linking.openURL(redirectURL);
    });

    return () => {
      Notifications.removeNotificationSubscription(notificationListener);
      Notifications.removeNotificationSubscription(responseListener);
    };
  }, []);

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
      checkServerConnection();
    }
  }, [fontsLoaded]);

  if (!fontsLoaded || loading) {
    return (
      <View style={styles.container}>
        <CustomLoader />
      </View>
    );
  }

  if (!isInternetConnected) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No Internet Connection</Text>
        <TouchableOpacity onPress={() => checkServerConnection()} style={styles.retryButton}>
          <Text style={styles.retryText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!isServerConnected) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Server Not Responding</Text>
        <TouchableOpacity onPress={() => checkServerConnection()} style={styles.retryButton}>
          <Text style={styles.retryText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }
  const MyTheme = {
    ...DefaultTheme,
    colors: {
      ...DefaultTheme.colors,
      background: Colors.darkGreen, // or your app's main background color
    },
  };

  return (
    <NavigationContainer
      ref={navigationRef}
      linking={linkingConfig}
      theme={MyTheme}
      fallback={
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <CustomLoader />
        </View>
      }>
      <KeyboardProvider>
        <SafeAreaProvider>
          <AuthProvider>
            <SafeAreaView
              style={{
                flex: 1,
                backgroundColor: Platform.OS === 'ios' ? 'transparent' : Colors.white,
              }}
              edges={["bottom"]}
            >
              <StatusBar translucent backgroundColor='transparent' />
              <AppContent />
              {/* <AppModalWrapper /> */}
              <Toast config={toastConfig} />
            </SafeAreaView>
          </AuthProvider>
        </SafeAreaProvider>
      </KeyboardProvider>
    </NavigationContainer>

  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontFamily: 'Exo_700Bold',
    fontSize: 20,
    color: Colors.primaryGreen,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.primaryGreen,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  retryText: {
    fontSize: 16,
    color: '#fff',
    fontFamily: 'Exo_600SemiBold',
  },
});
