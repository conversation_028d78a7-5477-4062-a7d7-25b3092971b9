import getDayName from "utils/dateandtimeformatters/getDayName";
import apiClient from "./axiosInstance";
import { getTimePeriod } from "utils/userrecords/getTimePeriod";
import weightLogResponseSchema from "schemas/weightSchema/weightLogResponseSchema";
import weightLastLoggedResponseSchema from "schemas/weightSchema/weightLastLoggedResponseSchema";
import getWeightRecordResponseSchema from "schemas/weightSchema/getWeightRecordResponseSchema";
import weightLogUpdatedResponseSchema from "schemas/weightSchema/weightLogUpdatedResponseSchema";
import weightGraphResponseSchemas from "schemas/weightSchema/getWeightGraphRecordsSchema";

export const weightLossService = {
    logWeightData: async ({ date, weight }) => {
        try {
            const res = await apiClient.post("/weight_records", {
                "weight": weight,
                "date": date
            });

            const weightLoggedData = weightLogResponseSchema.safeParse(res?.data);

            if (!weightLoggedData.success) {
                throw new Error("Unable to get logged weight. Please try again later.");
            }

            return {
                success: true,
                message: "Weight logged successfully!!",
                data: weightLoggedData?.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error recording weight."
            };
        }
    },
    getLastLoggedData: async () => {
        try {
            const res = await apiClient.get("/weight_records");

            const lastLoggedWeightData = weightLastLoggedResponseSchema.safeParse(res?.data);

            if (!lastLoggedWeightData.success) {
                throw new Error("Unable to get logged weight. Please try again later.");
            }

            return {
                success: true,
                data: lastLoggedWeightData?.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recent weight logged data."
            };
        }
    },
    getWeightRecordForDate: async (date) => {
        try {
            const res = await apiClient.get(`/weight_records?date=${date}`);

            const weightRecordData = getWeightRecordResponseSchema.safeParse(res?.data);

            if (!weightRecordData.success) {
                throw new Error("Unable to get weight record. Please try again later.");
            }

            return {
                success: true,
                data: weightRecordData?.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching weight record."
            };
        }
    },
    updateWeightData: async ({ id, weight, date }) => {
        try {
            const res = await apiClient.put(`/weight_records/${id}`, {
                "weight": weight,
            });

            const weightLogUpdatedData = weightLogUpdatedResponseSchema.safeParse(res?.data);

            if (!weightLogUpdatedData.success) {
                throw new Error("Something went wrong please try again later. Please try again later.");
            }

            return {
                success: true,
                message: res?.msg || "Weight data updated successfully!!"
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error updating weight."
            };
        }
    },
    getWeightGraphData: async ({ filter }) => {
        try {
            const res = await apiClient.get(`/weight_analytics?filter=${filter}`);

            let responseData = [];
            let timeRange = null;

            if (filter === "weekly") {
                const weeklyWeightData = weightGraphResponseSchemas.weekly.safeParse(res?.data);

                if (!weeklyWeightData.success) {
                    throw new Error("Unable to get weight graph data. Please try again later.");
                }

                responseData = weeklyWeightData.data.map((item) => {
                    return {
                        value: item?.averageWeight || 0,
                        label: getDayName(item.period).substring(0, 1)
                    };
                });

                timeRange = getTimePeriod(
                    weeklyWeightData.data[0].period,
                    weeklyWeightData.data[weeklyWeightData.data.length - 1].period
                );
            } else if (filter === "monthly") {
                const monthlyWeightData = weightGraphResponseSchemas.monthly.safeParse(res?.data);

                if (!monthlyWeightData.success) {
                    throw new Error("Unable to get weight graph data. Please try again later.");
                }

                responseData = monthlyWeightData.data.map((item) => {
                    const weekNumber = item.period.match(/week_(\d+)/);
                    return {
                        value: item?.averageWeight || 0,
                        label: `W${weekNumber[1]}`
                    };
                });

                timeRange = getTimePeriod(
                    monthlyWeightData.data[0].startDate,
                    monthlyWeightData.data[monthlyWeightData.data.length - 1].endDate
                );
            } else if (filter === "half_yearly") {
                const halfYearlyWeightData = weightGraphResponseSchemas.half_yearly.safeParse(res?.data);

                if (!halfYearlyWeightData.success) {
                    throw new Error("Unable to get weight graph data. Please try again later.");
                }

                responseData = halfYearlyWeightData.data.map((item) => {
                    return {
                        value: item?.averageWeight || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange =
                    halfYearlyWeightData.data[0].year !== halfYearlyWeightData.data[halfYearlyWeightData.data.length - 1].year
                        ? `${halfYearlyWeightData.data[0].period} ${halfYearlyWeightData.data[0].year} - ${halfYearlyWeightData.data[halfYearlyWeightData.data.length - 1].period} ${halfYearlyWeightData.data[halfYearlyWeightData.data.length - 1].year}`
                        : `${halfYearlyWeightData.data[0].period} - ${halfYearlyWeightData.data[halfYearlyWeightData.data.length - 1].period} ${halfYearlyWeightData.data[0].year}`;
            } else if (filter === "yearly") {

                const yearlyWeightData = weightGraphResponseSchemas.yearly.safeParse(res?.data);

                if (!yearlyWeightData.success) {
                    throw new Error("Unable to get weight graph data. Please try again later.");
                }

                responseData = yearlyWeightData.data.map((item) => {
                    return {
                        value: item?.averageWeight || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = yearlyWeightData.data[0].year !== yearlyWeightData.data[yearlyWeightData.data.length - 1].year
                    ? `${yearlyWeightData.data[0].period} ${yearlyWeightData.data[0].year} - ${yearlyWeightData.data[yearlyWeightData.data.length - 1].period} ${yearlyWeightData.data[yearlyWeightData.data.length - 1].year}`
                    : `${yearlyWeightData.data[0].period} - ${yearlyWeightData.data[yearlyWeightData.data.length - 1].period} ${yearlyWeightData.data[0].year}`;
            }

            return {
                success: true,
                data: {
                    weightData: responseData,
                    timeRange: timeRange
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching weight graph data."
            };
        }
    }
};
