import getDayName from 'utils/dateandtimeformatters/getDayName';
import apiClient from './axiosInstance';
import { getTimePeriod } from 'utils/userrecords/getTimePeriod';
import { mealRecordsResponseSchema } from 'schemas/nutritionSchema/mealRecordsResponseSchema';
import { mealRecordResponseSchema } from 'schemas/nutritionSchema/mealRecordResponseSchema';
import mealGraphRecordResponseSchema from 'schemas/nutritionSchema/mealGraphRecordResponseSchema';
import { mealIngredientsResponseSchema } from 'schemas/nutritionSchema/mealIngredientsResponseSchema';
import mealIngredientResponseSchema from 'schemas/nutritionSchema/mealIngredientResponseSchema';

export const mealRecordsService = {
    getMealRecords: async (date) => {
        try {
            const res = await apiClient.get('/meal_records?date=' + (date ? `${date}` : new Date().toISOString().split('T')[0]));

            const mealRecords = mealRecordsResponseSchema.safeParse(res?.data);

            if (!mealRecords.success) {
                throw new Error("Unable to load meal records. Please try again later.");
            }

            return {
                success: true,
                data: mealRecords.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching meal records'
            };
        }
    },

    getMealRecord: async (id) => {
        try {
            const res = await apiClient.get(`/meal_records/${id}`);

            const mealRecord = mealRecordResponseSchema.safeParse(res?.data);

            if (!mealRecord.success) {
                throw new Error("Unable to load meal record. Please try again later.");
            }

            return {
                success: true,
                data: mealRecord.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching meal record'
            };
        }
    },

    addMealRecord: async (mealData) => {
        try {
            await apiClient.post('/meal_records', mealData);

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error adding meal record'
            };
        }
    },

    updateMealRecord: async (id, mealData) => {
        try {
            await apiClient.put(`/meal_records/${id}`, mealData);

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error updating meal record'
            };
        }
    },

    getMealGraphRecords: async (filter) => {
        try {
            const res = await apiClient.get(`/meal_analytics?filter=${filter}`);

            const mealGraphRecordsData = mealGraphRecordResponseSchema.safeParse({
                filter: filter,
                data: res?.data,
            });

            if (!mealGraphRecordsData.success) {
                throw new Error("Unable to get meal graph data. Please try again later.");
            }

            let mealGraphRecords = [];
            let timePeriod = null;


            if (filter === "weekly") {
                mealGraphRecords = res.data.map((record) => ({
                    value: record?.data || {
                        protein: 0,
                        calories: 0,
                        fats: 0,
                        fiber: 0,
                        carbs: 0,
                    },
                    label: getDayName(record.period).substring(0, 1),
                }));

                timePeriod = getTimePeriod(
                    res.data[0].period,
                    res.data[res.data.length - 1].period
                );
            }
            else if (filter === "monthly") {
                mealGraphRecords = res.data.map((record) => {
                    const weekNumber = record.period.match(/week_(\d+)/);

                    return {
                        value: record?.data || {
                            protein: 0,
                            calories: 0,
                            fats: 0,
                            fiber: 0,
                            carbs: 0,
                        },
                        label: `W${weekNumber[1]}`,
                    }
                });

                timePeriod = getTimePeriod(
                    res.data[0].startDate,
                    res.data[res.data.length - 1].endDate
                );
            }
            else if (filter === "half_yearly") {
                mealGraphRecords = res.data.map((record) => {
                    return {
                        value: record?.data || {
                            protein: 0,
                            calories: 0,
                            fats: 0,
                            fiber: 0,
                            carbs: 0,
                        },
                        label: record.period.substring(0, 1),
                    }
                });

                timePeriod = res.data[0].year !== res.data[res.data.length - 1].year
                    ? `${res.data[0].period.substring(0, 3)} ${res.data[0].year} - ${res.data[res.data.length - 1].period.substring(0, 3)} ${res.data[res.data.length - 1].year}`
                    : `${res.data[0].period.substring(0, 3)} - ${res.data[res.data.length - 1].period.substring(0, 3)} ${res.data[0].year}`;
            }
            else if (filter === "yearly") {
                mealGraphRecords = res.data.map((record) => ({
                    value: record?.data || {
                        protein: 0,
                        calories: 0,
                        fats: 0,
                        fiber: 0,
                        carbs: 0,
                    },
                    label: record.period.substring(0, 1),
                }));

                timePeriod = res.data[0].year !== res.data[res.data.length - 1].year
                    ? `${res.data[0].period} ${res.data[0].year} - ${res.data[res.data.length - 1].period} ${res.data[res.data.length - 1].year}`
                    : `${res.data[0].period} - ${res.data[res.data.length - 1].period} ${res.data[0].year}`;
            }

            return {
                success: true,
                data: {
                    graphData: mealGraphRecords,
                    timePeriod: timePeriod,
                },
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recent meal logged data.",
            };
        }
    },

    getIngredients: async (query = "", page = 1) => {
        try {
            const res = await apiClient.get(`/ingredient?name=${query}&page=${page}`);

            const ingredientsData = mealIngredientsResponseSchema.safeParse(res?.data);

            if (!ingredientsData.success) {
                throw new Error("Unable to get ingredients. Please try again later.");
            }

            return {
                success: true,
                data: ingredientsData.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching ingredients"
            };
        }
    },

    getIngredient: async (id) => {
        try {
            const res = await apiClient.get(`/ingredient/${id}`);

            const ingredientData = mealIngredientResponseSchema.safeParse(res?.data);

            if (!ingredientData.success) {
                throw new Error("Unable to get ingredient. Please try again later.");
            }

            return {
                success: true,
                data: ingredientData.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching ingredient"
            };
        }
    }
};