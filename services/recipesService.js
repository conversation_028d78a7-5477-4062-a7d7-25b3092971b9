import { recipeDetailsSchema } from "schemas/recipeSchema/recipeDetailsResponse";
import { recipesResponseSchema } from "schemas/recipeSchema/recipesResponse";
import apiClient from "./axiosInstance";
import createRecipeResponseSchema from "schemas/recipeSchema/createRecipeResponseSchema";

export const recipesService = {
    getAllRecipes: async ({ query, page = 1, mealType }) => {

        let userOnlyRecipes = mealType === "My recipes";

        if (mealType === "All" || mealType == "My recipes") {
            mealType = "";
        }

        try {
            const res = await apiClient.get(
                `/recipes?page=${page}${query ? `&title=${query}` : ""
                }${mealType ? `&mealType=${mealType}` : ""
                }${userOnlyRecipes ? `&myrecipe=true` : ""}`
            )

            const recipes = recipesResponseSchema.safeParse(res?.data);

            if (!recipes.success) {
                throw new Error("Unable to load recipes. Please try again later.")
            }

            return {
                success: true,
                data: recipes.data
            }
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recipes"
            }
        }
    },
    getSingleRecipe: async ({ id }) => {
        try {
            const res = await apiClient.get(`/recipes/${id}`);

            const recipe = recipeDetailsSchema.safeParse(res?.data);

            if (!recipe.success) {
                throw new Error("Unable to load recipe. Please try again later.");
            }

            return {
                success: true,
                data: recipe?.data
            }
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recipe"
            }
        }
    },
    createUserRecipe: async ({ data }) => {
        try {
            const res = await apiClient.post(`/recipes`, data, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            const recipe = createRecipeResponseSchema.safeParse(res?.data);

            if (!recipe.success) {
                throw new Error("Unable to create recipe. Please try again later.");
            }

            return {
                success: true,
                data: recipe.data
            }
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error creating recipe"
            }
        }
    },
}