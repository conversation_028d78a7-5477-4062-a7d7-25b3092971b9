import getDevicePermissionsResponseSchema from "schemas/deviceSchema/getDevicePermissionsResponse";
import apiClient from "./axiosInstance";
import { getDeviceResponseSchema } from "schemas/deviceSchema/DeviceResponse";
const apiUrl = process.env.EXPO_PUBLIC_API_URL;

const deviceService = {
  getDevices: async () => {
    try {
      const res = await apiClient.get(`${apiUrl}/allDeviceToUser`);

      const devices = res?.devices?.map((device) => {
        const deviceRes = getDeviceResponseSchema.safeParse(device)

        if (!deviceRes.success) {
          throw new Error("Unable to load devices. Please try again later.");
        }

        return deviceRes.data;
      })


      return {
        success: true,
        data: devices,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching devices",
      };
    }
  },

  getPermission: async () => {
    try {
      const res = await apiClient.get(`${apiUrl}/app_permissions`);

      const appPermissions = getDevicePermissionsResponseSchema.safeParse(res?.appPermissions);

      if (!appPermissions.success) {
        throw new Error("Unable to load app permissions. Please try again later.");
      }

      return {
        success: true,
        data: appPermissions.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error updating goals",
      };
    }
  },
  updatePermission: async (permission) => {
    try {
      await apiClient.put(
        `${apiUrl}/app_permissions/update`,
        permission
      );
      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error updating goals",
      };
    }
  },
};

export default deviceService;
