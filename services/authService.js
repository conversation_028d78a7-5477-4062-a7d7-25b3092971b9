import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import apiClient from './axiosInstance';
import DeviceInfo, { getDeviceType } from 'react-native-device-info';
import { Platform } from 'react-native';
import * as Device from "expo-device";
import loginResponseSchema from 'schemas/authSchema/loginUserResponseSchema';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;

const authService = {

    login: async ({ email, password }) => {
        try {
            const response = await apiClient.post(`${apiUrl}/login`, { email, password });

            const loginResponse = loginResponseSchema.safeParse(response);

            if (!loginResponse.success) {
                throw new Error("Unable to login. Please try again later.");
            }

            return {
                success: true,
                data: loginResponse.data
            }

        } catch (error) {
            return {
                success: false,
                error: error
            }
        }
    },

    register: async (userData) => {
        try {
            await axios.post(`${apiUrl}/register`, userData);

            return {
                success: true,
            }
        } catch (error) {
            return {
                success: false,
                error: error?.response?.data?.message || error?.message || "Error registering user"
            }
        }
    },

    forgotPassword: async (email) => {
        try {
            await axios.put(`${apiUrl}/forget_password`, { email });
            return { success: true, message: "Password reset email sent!" };
        } catch (error) {
            return { success: false, message: error?.response?.data?.message || 'Password reset failed' };
        }
    },

    logout: async ({ deviceId }) => {
        try {
            await apiClient.get(`/logout/${deviceId}`);

            return {
                success: true
            }
        } catch (error) {
            return {
                success: false
            }
        }
    },

    resendVerificationLink: async (email) => {
        try {
            await axios.put(`${apiUrl}/resend_verification_email`, { email });
            return { success: true };
        } catch (error) {
            return { success: false, error: error?.response?.data?.message || error?.message || 'Password reset failed' };
        }
    },

    updateUserTimeZone: async () => {
        try {
            await apiClient.put(`${apiUrl}/timezone_update`, { timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone });
            return {
                success: true
            };
        }
        catch (error) {
            return {
                success: false,
                error: error?.message || 'Error updating timezone'
            };
        }
    }
};

export default authService;
