import * as Notifications from "expo-notifications";
import * as Device from "expo-device";
import Constants from "expo-constants";
import { Platform } from "react-native";
import { Colors } from "constants/theme/colors";

export const getDeviceType = (type) => {
    switch (type) {
        case Device.DeviceType.PHONE:
            return "Mobile";
        case Device.DeviceType.TABLET:
            return "Tablet";
        case Device.DeviceType.DESKTOP:
            return "Desktop";
        case Device.DeviceType.TV:
            return "TV";
        default:
            return "Unknown";
    }
};

export async function registerForPushNotificationsAsync(throwOnMissingPermissions = false) {
    let token = null;

    if (!Device.isDevice) {
        return {
            success: false,
            error: "Must use physical device for push notifications",
        };
    }

    try {
        // Configure notification settings based on platform
        if (Platform.OS === "android") {
            await Notifications.setNotificationChannelAsync("default", {
                name: "default",
                importance: Notifications.AndroidImportance.MAX,
                vibrationPattern: [0, 250, 250, 250],
                lightColor: Colors.lightGreen,
            });

            await Notifications.setNotificationChannelAsync("sound1", {
                name: "sound1",
                importance: Notifications.AndroidImportance.MAX,
                vibrationPattern: [0, 250, 250, 250],
                lightColor: Colors.lightGreen,
                sound: 'sound1.wav',
            });

            await Notifications.setNotificationChannelAsync("sound2", {
                name: "sound2",
                importance: Notifications.AndroidImportance.MAX,
                vibrationPattern: [0, 250, 250, 250],
                lightColor: Colors.lightGreen,
                sound: 'sound2.wav',
            });

            await Notifications.setNotificationChannelAsync("sound3", {
                name: "sound3",
                importance: Notifications.AndroidImportance.MAX,
                vibrationPattern: [0, 250, 250, 250],
                lightColor: Colors.lightGreen,
                sound: 'sound3.wav',
            });

            await Notifications.setNotificationChannelAsync("sound4", {
                name: "sound4",
                importance: Notifications.AndroidImportance.MAX,
                vibrationPattern: [0, 250, 250, 250],
                lightColor: Colors.lightGreen,
                sound: 'sound4.wav',
            });
        } else if (Platform.OS === 'ios') {
            // iOS-specific settings
            await Notifications.setNotificationHandler({
                handleNotification: async () => ({
                    shouldShowAlert: true,
                    shouldPlaySound: true,
                    shouldSetBadge: true,
                }),
            });
        }

        // Check and request permissions
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== "granted") {
            const { status } = await Notifications.requestPermissionsAsync({
                ios: {
                    allowAlert: true,
                    allowBadge: true,
                    allowSound: true,
                    allowAnnouncements: true,
                },
            });
            finalStatus = status;
        }

        if (finalStatus !== "granted") {
            if (throwOnMissingPermissions) {
                throw new Error("Grant push notification to never miss an update.");
            }
            return {
                success: true,
                pushTokenString: null
            };
        }

        // Get project ID for Expo push tokens
        const projectId =
            Constants?.expoConfig?.extra?.eas?.projectId ??
            Constants?.easConfig?.projectId;

        if (!projectId) {
            throw new Error("Project ID not found");
        }

        // Get the token
        const pushToken = await Notifications.getExpoPushTokenAsync({
            projectId,
        });

        token = pushToken.data;
        // console.log("Push notification token:", token);

        if (Platform.OS === 'ios') {
            // Register for remote notifications on iOS
            Notifications.setBadgeCountAsync(0);
        }

        return {
            success: true,
            pushTokenString: token
        };
    } catch (e) {
        return {
            success: false,
            error: `${e?.message || "Error setting up notification"}`
        };
    }
}

export async function togglePushNotificationActivation() {
    return registerForPushNotificationsAsync(true);
}

// Add this function to set up notification handlers
export function setupNotificationListeners(onNotificationReceived, onNotificationResponseReceived) {
    // When the app is in the foreground
    const foregroundSubscription = Notifications.addNotificationReceivedListener(notification => {
        if (onNotificationReceived) {
            onNotificationReceived(notification);
        }
    });

    // When the user taps on a notification
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
        if (onNotificationResponseReceived) {
            onNotificationResponseReceived(response);
        }
    });

    return () => {
        // Clean up listeners when they're no longer needed
        foregroundSubscription.remove();
        responseSubscription.remove();
    };
}

// Helper function to send a local notification (for testing purposes)
export async function sendLocalNotification(title, body, data = {}, sound = true, channelId = "default") {
    await Notifications.scheduleNotificationAsync({
        content: {
            title,
            body,
            data,
            sound: sound ? true : null,
            ...(Platform.OS === 'android' && { channelId }),
        },
        trigger: null, // null means show immediately
    });
}