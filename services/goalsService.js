import getGoalsResponseSchema from 'schemas/userSchema/goalsSchema/getGoalsResponseSchema';
import apiClient from './axiosInstance';

const goalsService = {
    getGoals: async () => {
        try {
            const res = await apiClient.get(`/goals`);
            const goals = getGoalsResponseSchema.safeParse(res?.data);

            if (!goals.success) {
                throw new Error("Unable to load goals. Please try again later.");
            }

            return {
                success: true,
                data: goals.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || 'Error fetching goals'
            };
        }
    },

    updateGoal: async (goalsData) => {
        try {
            await apiClient.put(`/goals`, goalsData);

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || 'Error updating goals'
            };
        }
    },
};

export default goalsService;