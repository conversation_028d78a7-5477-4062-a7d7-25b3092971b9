
import apiClient from './axiosInstance';
import carousalSchemaResponseSchema from 'schemas/carousalSchema/carousalSchemaResponseSchema';

export const carouselService = {
    getCarousalData: async () => {
        try {
            const res = await apiClient.get(`/carousel`);
            const parseResult = carousalSchemaResponseSchema.safeParse(res?.data)

            if (!parseResult.success) {
                return {
                    success: false,
                    error: "Unable to load carousel content. Please try again later."
                };
            }

            return {
                success: true,
                data: parseResult.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching carousal data'
            };
        }
    }
}