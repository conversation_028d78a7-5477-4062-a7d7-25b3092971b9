import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { Alert, Platform } from 'react-native';
import { navigate } from '../navigations/RootNavigation';

// Configuration constants
const CONFIG = {
    API_TIMEOUT: 15000, // 15 seconds
    NAVIGATION_DELAY: 100, // Reduced from 500ms
    MAX_RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // 1 second
};

// Authentication endpoints that should not trigger token refresh
const AUTH_ENDPOINTS = ['/login', '/logout', '/refresh-token', '/register', '/forgot-password'];

const apiUrl = process.env.EXPO_PUBLIC_API_URL;

// Enhanced logging utility
// const logger = {
//     info: (message, data) => {
//         if (__DEV__) {
//             console.log(`[API Client] ${message}`, data || '');
//         }
//     },
//     error: (message, error) => {
//         if (__DEV__) {
//             console.error(`[API Client] ${message}`, error);
//         }
//         // In production, you might want to send this to a logging service
//         // crashlytics().recordError(error);
//     },
//     warn: (message, data) => {
//         if (__DEV__) {
//             console.warn(`[API Client] ${message}`, data || '');
//         }
//     }
// };

// Token management state
class TokenManager {
    constructor () {
        this.refreshSubscribers = [];
        this.refreshPromise = null;
        this.retryCount = 0;
    }

    addSubscriber(callback) {
        this.refreshSubscribers.push(callback);
    }

    notifySubscribers(token, error = null) {
        this.refreshSubscribers.forEach(callback => callback(token, error));
        this.refreshSubscribers = [];
    }

    async clearTokens() {
        try {
            await Promise.all([
                SecureStore.deleteItemAsync('accessToken'),
                SecureStore.deleteItemAsync('refreshToken'),
                SecureStore.deleteItemAsync('userData'),
            ]);
            // logger.info('Tokens cleared successfully');
        } catch (error) {
            // logger.error('Error clearing tokens:', error);
        }
    }

    async getStoredTokens() {
        try {
            const [accessToken, refreshToken] = await Promise.all([
                SecureStore.getItemAsync('accessToken'),
                SecureStore.getItemAsync('refreshToken'),
            ]);
            return { accessToken, refreshToken };
        } catch (error) {
            // logger.error('Error retrieving tokens:', error);
            return { accessToken: null, refreshToken: null };
        }
    }

    async storeTokens(accessToken, refreshToken = null) {
        try {
            const promises = [SecureStore.setItemAsync('accessToken', accessToken)];
            if (refreshToken) {
                promises.push(SecureStore.setItemAsync('refreshToken', refreshToken));
            }
            await Promise.all(promises);
            // logger.info('Tokens stored successfully');
        } catch (error) {
            // logger.error('Error storing tokens:', error);
            throw error;
        }
    }

    redirectToLogin(message = 'Your session has expired. Please log in again.') {
        // logger.info('Redirecting to login page');

        // Clear tokens first
        this.clearTokens();

        // Show alert on mobile platforms
        if (Platform.OS !== 'web') {
            // Alert.alert(
            //     "Session Expired",
            //     message,
            //     [
            //         {
            //             text: "OK",
            //             onPress: () => {
            //                 setTimeout(() => {
            //                     navigate('Login');
            //                 }, CONFIG.NAVIGATION_DELAY);
            //             }
            //         }
            //     ],
            //     { cancelable: false }
            // );
            navigate('Login');
        } else {
            // For web, navigate immediately
            navigate('Login');
        }
    }

    async refreshAccessToken() {
        // Prevent multiple simultaneous refresh attempts
        if (this.refreshPromise) {
            return this.refreshPromise;
        }

        this.refreshPromise = this._performTokenRefresh();

        try {
            const result = await this.refreshPromise;
            this.retryCount = 0; // Reset retry count on success
            return result;
        } catch (error) {
            throw error;
        } finally {
            this.refreshPromise = null;
        }
    }

    async _performTokenRefresh() {
        try {
            const { refreshToken } = await this.getStoredTokens();

            if (!refreshToken) {
                // logger.warn('No refresh token available');
                throw new AuthError('NO_REFRESH_TOKEN', 'No refresh token available');
            }

            // logger.info('Attempting to refresh access token');

            // Use a new axios instance to avoid interceptor loops
            const refreshClient = axios.create({
                baseURL: apiUrl,
                timeout: CONFIG.API_TIMEOUT,
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const response = await refreshClient.post('/refresh-token', {
                refreshToken
            });

            if (!response.data?.accessToken) {
                throw new AuthError('INVALID_RESPONSE', 'Invalid refresh token response');
            }

            const { accessToken, refreshToken: newRefreshToken } = response.data;

            // Store new tokens
            await this.storeTokens(accessToken, newRefreshToken);

            // logger.info('Token refresh successful');
            return accessToken;

        } catch (error) {
            // logger.error('Token refresh failed:', error);

            // Handle different types of refresh token errors
            if (error.response) {
                const status = error.response.status;
                const errorData = error.response.data;

                if (status === 401 || status === 403) {
                    // Refresh token is expired or invalid
                    throw new AuthError('REFRESH_TOKEN_EXPIRED', 'Refresh token has expired');
                } else if (status >= 500) {
                    // Server error - retry might help
                    throw new AuthError('SERVER_ERROR', 'Server error during token refresh');
                }
            }

            // Network or other errors
            if (error.code === 'ECONNABORTED') {
                throw new AuthError('TIMEOUT', 'Token refresh request timed out');
            }

            throw new AuthError('UNKNOWN_ERROR', error.message || 'Unknown error during token refresh');
        }
    }

    reset() {
        this.refreshSubscribers = [];
        this.refreshPromise = null;
        this.retryCount = 0;
    }
}

// Custom error class for authentication errors
class AuthError extends Error {
    constructor (code, message) {
        super(message);
        this.name = 'AuthError';
        this.code = code;
    }
}

// Create token manager instance
const tokenManager = new TokenManager();

// Create axios instance
const apiClient = axios.create({
    baseURL: apiUrl,
    timeout: CONFIG.API_TIMEOUT,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Request Interceptor
apiClient.interceptors.request.use(
    async (config) => {
        try {
            const { accessToken } = await tokenManager.getStoredTokens();
            if (accessToken) {
                config.headers.Authorization = `Bearer ${accessToken}`;
            }

            // logger.info(`Making ${config.method?.toUpperCase()} request to: ${config.url}`);
        } catch (error) {
            // logger.error('Error setting auth header:', error);
        }
        return config;
    },
    (error) => {
        // logger.error('Request interceptor error:', error);
        return Promise.reject(error);
    }
);

// Response Interceptor
apiClient.interceptors.response.use(
    (response) => {
        // logger.info(`Response received from: ${response.config.url}`, {
        //     status: response.status,
        //     statusText: response.statusText
        // });
        return response.data;
    },
    async (error) => {
        const originalRequest = error.config;

        // logger.error('Response error:', {
        //     url: originalRequest?.url,
        //     status: error.response?.status,
        //     statusText: error.response?.statusText
        // });

        // Check if this is an auth endpoint
        const isAuthEndpoint = AUTH_ENDPOINTS.some(endpoint =>
            originalRequest?.url?.includes(endpoint)
        );

        // Handle 401 Unauthorized errors for non-auth endpoints
        if (error.response?.status === 401 && !originalRequest._retry && !isAuthEndpoint) {
            originalRequest._retry = true;

            // Check if we already have a refresh in progress
            if (!tokenManager.refreshPromise) {
                // Start new refresh process
                try {
                    const newToken = await tokenManager.refreshAccessToken();

                    // Update the Authorization header for the original request
                    originalRequest.headers.Authorization = `Bearer ${newToken}`;

                    // Notify all queued requests
                    tokenManager.notifySubscribers(newToken);

                    // logger.info('Retrying original request with new token');
                    return apiClient(originalRequest);

                } catch (refreshError) {
                    // logger.error('Token refresh failed, handling logout:', refreshError);

                    // Notify all subscribers about the failure
                    tokenManager.notifySubscribers(null, refreshError);

                    // Handle different types of refresh errors
                    if (refreshError instanceof AuthError) {
                        switch (refreshError.code) {
                            case 'NO_REFRESH_TOKEN':
                            case 'REFRESH_TOKEN_EXPIRED':
                                tokenManager.redirectToLogin('Your session has expired. Please log in again.');
                                break;
                            case 'SERVER_ERROR':
                                tokenManager.redirectToLogin('Server error occurred. Please try logging in again.');
                                break;
                            case 'TIMEOUT':
                                tokenManager.redirectToLogin('Connection timeout. Please check your internet and try again.');
                                break;
                            default:
                                tokenManager.redirectToLogin('Authentication failed. Please log in again.');
                        }
                    } else {
                        tokenManager.redirectToLogin('An error occurred. Please log in again.');
                    }

                    return Promise.reject(createErrorResponse(error, 'AUTHENTICATION_FAILED'));
                }
            } else {
                // Queue this request to be retried after ongoing token refresh
                return new Promise((resolve, reject) => {
                    tokenManager.addSubscriber((token, refreshError) => {
                        if (token) {
                            originalRequest.headers.Authorization = `Bearer ${token}`;
                            resolve(apiClient(originalRequest));
                        } else {
                            reject(createErrorResponse(error, 'AUTHENTICATION_FAILED'));
                        }
                    });
                });
            }
        }

        // Handle other HTTP errors
        return Promise.reject(createErrorResponse(error));
    }
);

// Helper function to create standardized error responses
function createErrorResponse(error, customCode = null) {
    if (error.response) {
        // Server responded with error status
        const errorData = error.response.data || {};
        return {
            message: errorData.message || `HTTP Error ${error.response.status}`,
            status: error.response.status,
            code: customCode || errorData.code || 'HTTP_ERROR',
            data: errorData,
        };
    } else if (error.request) {
        // Network error
        return {
            message: 'Network error. Please check your internet connection.',
            code: customCode || 'NETWORK_ERROR',
            status: 0,
        };
    } else {
        // Other errors
        return {
            message: error.message || 'An unexpected error occurred.',
            code: customCode || 'UNKNOWN_ERROR',
            status: 0,
        };
    }
}

// Utility functions for manual token management
export const authUtils = {
    // Clear all authentication data
    logout: async () => {
        // logger.info('Manual logout initiated');
        await tokenManager.clearTokens();
        tokenManager.reset();
    },

    // Check if user is authenticated
    isAuthenticated: async () => {
        const { accessToken } = await tokenManager.getStoredTokens();
        return !!accessToken;
    },

    // Get current access token
    getAccessToken: async () => {
        const { accessToken } = await tokenManager.getStoredTokens();
        return accessToken;
    },

    // Manually refresh token
    refreshToken: async () => {
        return tokenManager.refreshAccessToken();
    },

    // Store tokens after login
    storeTokens: async (accessToken, refreshToken) => {
        await tokenManager.storeTokens(accessToken, refreshToken);
    }
};

export default apiClient;