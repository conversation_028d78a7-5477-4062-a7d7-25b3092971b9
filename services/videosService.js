import { getAllVideosResponseSchema } from "schemas/videosSchema/getAllVideosSchema";
import apiClient from "./axiosInstance";

const videosService = {
    getAllVideos: async ({ page = 1, tag, filter = "" }) => {
        try {
            const response = await apiClient.get(`/video_library?${tag && `tag=${tag}`}&page=${page}&filter=${filter}`);

            const videos = getAllVideosResponseSchema.safeParse(response?.videos);

            if (!videos.success) {
                throw new Error("Unable to load videos. Please try again later.");
            }

            return {
                success: true,
                data: videos.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching videos"
            };
        }
    }
}

export default videosService;