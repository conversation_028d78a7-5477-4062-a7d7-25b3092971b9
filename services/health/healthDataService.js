import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from '../axiosInstance';
import { getGrantedPermissionsList } from 'utils/GoogleHealth';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { formatHealthData } from './utils/formatHealthData';

export const setIsFirstCall = async () => {
    const expirationTime = new Date().getTime() + 24 * 60 * 60 * 1000;
    await AsyncStorage.setItem('isfirstcall', JSON.stringify({ expiresAt: expirationTime }));
};

export const getIsFirstCall = async () => {
    const tokenData = await AsyncStorage.getItem('isfirstcall');
    if (!tokenData) return true;
    const { expiresAt } = JSON.parse(tokenData);
    const currentTime = new Date().getTime();
    if (currentTime > expiresAt) {
        await AsyncStorage.removeItem('isfirstcall');
        return true;
    }
    return false;
};

export const insertSampleData = async () => {
    if (Platform.OS !== 'android') return;
    try {
        const { initialize, insertRecords } = await import('react-native-health-connect');
        const isInitialized = await initialize();
        if (!isInitialized) return;
        const now = new Date();
        const nDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        await insertRecords([
            {
                recordType: 'Distance',
                distance: { unit: 'kilometers', value: 1 },
                startTime: nDaysAgo.toISOString(),
                endTime: now.toISOString(),
            },
        ]);
    } catch (err) {
        console.log(err);
    }
};

export const readSampleData = async (days, filterRecord = [], callDumpAPi = false, formatData = false) => {
    if (Platform.OS !== 'android') return { success: false, error: 'Health Connect only available on Android' };
    try {
        const { initialize, readRecords } = await import('react-native-health-connect');
        const isInitialized = await initialize();
        if (!isInitialized) {
            throw Error("Health Connect not initialized");
        }
        const { permissionsGranted } = await getGrantedPermissionsList();
        const recordNames = permissionsGranted
            .filter(record => record.accessType === "read")
            .map(record => record.recordType);

        const now = new Date();
        const nDaysAgo = new Date();
        nDaysAgo.setDate(nDaysAgo.getDate() - days);
        nDaysAgo.setHours(0, 0, 0, 0);

        const result = await Promise.all(recordNames.filter(record => {
            if (filterRecord.length == 0 || filterRecord.includes(record)) return record;
        }).map(async (record) => {
            let allData = [];
            let pageToken = null;
            do {
                const response = await readRecords(record, {
                    timeRangeFilter: {
                        operator: 'between',
                        startTime: nDaysAgo.toISOString(),
                        endTime: now.toISOString(),
                    },
                    pageSize: 1000,
                    pageToken: pageToken
                });
                allData = [...allData, ...response.records];
                pageToken = response.pageToken;
            } while (pageToken);
            return { name: record, data: allData };
        }));
        await sendHealthDataToAggregateBackend(formatData ? result.map(formatHealthData) : result, callDumpAPi);
        return {
            success: true,
            error: null
        };
    } catch (error) {
        if (error?.message?.includes('rate limit') || error?.message?.includes('quota')) {
            return {
                success: false,
                error: "Rate limit exceeded. Please wait before making more requests."
            };
        } else {
            console.log("Error fetching health data:", error);
            return {
                success: false,
                error: error.message || "Unknown error"
            };
        }
    }
};

const sendHealthDataToAggregateBackend = async (result, callDumpAPi) => {
    try {
        const deviceId = await DeviceInfo.getUniqueId();
        if (callDumpAPi) {
            await apiClient.post("/user-health/system-activity", {
                deviceId: deviceId,
                os: Platform.OS,
                source: Platform.OS == "android" ? "google" : "apple",
                dumps: result.filter((item) => {
                    if (item.data.length > 0) return item;
                }),
            });
        } else {
            await apiClient.post("/health-dump", {
                deviceId: deviceId,
                os: Platform.OS,
                source: Platform.OS == "android" ? "google" : "apple",
                data: result.filter((item) => {
                    if (item.data.length > 0) return item;
                }),
            });
        }
    } catch (error) {
        console.log("Send Health data error : ", error);
    }
};
