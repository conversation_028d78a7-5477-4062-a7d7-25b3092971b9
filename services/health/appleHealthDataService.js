import { Platform, NativeModules } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import apiClient from '../axiosInstance';
import { formatHealthData } from './utils/formatHealthData';

// Only try to load the health modules on iOS
let AppleHealthKit = null;
let NativeAppleHealthKit = null;
let Permissions = {};

// Make sure we're on iOS before trying to load the modules
if (Platform.OS === 'ios') {
  try {
    // Try to get the native module directly
    NativeAppleHealthKit = NativeModules.AppleHealthKit;

    // Also import the JS wrapper
    AppleHealthKit = require('react-native-health');

    // Get constants from the module
    if (AppleHealthKit && AppleHealthKit.Constants) {
      Permissions = AppleHealthKit.Constants.Permissions || {};
    }
  } catch (error) {
    console.error('Error loading AppleHealthKit modules:', error);
  }
}

/**
 * Check if Apple HealthKit is available on the device
 * @returns {boolean} Whether HealthKit is available
 */
export const isHealthKitAvailable = () => {
  return Platform.OS === 'ios' && AppleHealthKit !== null;
};

/**
 * Read health data from Apple HealthKit
 * @param {number} days - Number of days to fetch data for
 * @param {Array} filterRecord - Array of record types to filter by
 * @param {boolean} callDumpApi - Whether to call the dump API
 * @param {boolean} formatData - Whether to format the data
 * @returns {Promise<Object>} Result of the operation
 */
export const readAppleHealthData = async (days = 7, filterRecord = [], callDumpApi = false, formatData = false) => {
  if (Platform.OS !== 'ios') {
    return { success: false, error: 'Apple HealthKit only available on iOS' };
  }


  if (!AppleHealthKit && !NativeAppleHealthKit) {
    return { success: false, error: 'Apple HealthKit modules not available' };
  }

  try {
    // Set up date range for queries
    const now = new Date();
    const nDaysAgo = new Date();
    nDaysAgo.setDate(nDaysAgo.getDate() - days);
    nDaysAgo.setHours(0, 0, 0, 0);

    console.log(`Fetching Apple Health data for ${days} days: ${nDaysAgo.toISOString()} to ${now.toISOString()}`);

    // Define the record types we want to fetch (matching Android implementation)
    const recordTypes = [
      'Steps',
      'Distance',
      'HeartRate',
      'TotalCaloriesBurned',
      'FlightsClimbed',
      'ActiveEnergyBurned',
      'SleepAnalysis'
    ];

    // Filter record types if needed
    const recordsToFetch = filterRecord.length > 0
      ? recordTypes.filter(record => filterRecord.includes(record))
      : recordTypes;

    console.log('Records to fetch:', recordsToFetch);

    // Fetch data for each record type with aggregate collection (same pattern as Android)
    const result = await Promise.all(recordsToFetch.map(async (recordType) => {
      console.log(`Fetching data for record type: ${recordType}`);
      let allData = [];

      try {
        // Fetch aggregate data for the entire date range
        const data = await fetchAggregateHealthData(recordType, nDaysAgo, now);
        allData = data;

        console.log(`Fetched ${allData.length} records for ${recordType}`);
      } catch (error) {
        console.log(`Error fetching ${recordType}:`, error);
        allData = [];
      }

      return { name: recordType, data: allData };
    }));
    // Send data to backend using the same function as Android
    const sendResult = await sendHealthDataToAggregateBackend(formatData ? result.map(formatHealthData) : result, callDumpApi);
    console.log("Send result:", sendResult);

    return {
      success: true,
      error: null
    };
  } catch (error) {
    // Handle rate limiting and quota errors (same as Android)
    if (error?.message?.includes('rate limit') || error?.message?.includes('quota')) {
      return {
        success: false,
        error: "Rate limit exceeded. Please wait before making more requests."
      };
    } else {
      console.log("Error fetching Apple Health data:", error);
      return {
        success: false,
        error: error.message || "Unknown error"
      };
    }
  }
};

/**
 * Fetch aggregate health data for a specific record type (similar to Android pagination logic)
 * @param {string} recordType - The type of health record to fetch
 * @param {Date} startDate - Start date for the query
 * @param {Date} endDate - End date for the query
 * @returns {Promise<Array>} Array of health data records
 */
const fetchAggregateHealthData = async (recordType, startDate, endDate) => {
  const options = {
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
  };

  // For Apple HealthKit, we don't have pagination like Android Health Connect,
  // but we can fetch data in chunks by breaking down the date range
  let allData = [];

  try {
    switch (recordType) {
      case 'Steps':
        allData = await fetchStepsData(options);
        break;
      case 'Distance':
        allData = await fetchDistanceData(options);
        break;
      case 'HeartRate':
        allData = await fetchHeartRateData(options);
        break;
      case 'TotalCaloriesBurned':
      case 'ActiveEnergyBurned':
        allData = await fetchCaloriesData(options);
        break;
      case 'FlightsClimbed':
        allData = await fetchFlightsData(options);
        break;
      case 'SleepAnalysis':
        allData = await fetchSleepData(options);
        break;
      default:
        console.log(`Unknown record type: ${recordType}`);
        allData = [];
    }
  } catch (error) {
    console.log(`Error fetching aggregate data for ${recordType}:`, error);
    allData = [];
  }

  return allData;
};

/**
 * Fetch steps data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Steps data
 */
const fetchStepsData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getStepCount === 'function') {
      console.log('Fetching steps data with JS wrapper');
      AppleHealthKit.getStepCount(options, (err, results) => {
        if (err) {
          console.log('Error getting step count with JS wrapper:', err);
          // Try native module as fallback
          tryNativeSteps();
        } else {
          console.log('Step count from JS wrapper:', results);
          resolve([{
            count: results.value || 0,
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      // Try native module directly
      tryNativeSteps();
    }

    function tryNativeSteps() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getStepCount === 'function') {
        console.log('Fetching steps data with native module');
        NativeAppleHealthKit.getStepCount(options, (err, results) => {
          if (err) {
            console.log('Error getting step count with native module:', err);
            resolve([]);
          } else {
            console.log('Step count from native module:', results);
            resolve([{
              count: results.value || 0,
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch steps data');
        resolve([]);
      }
    }
  });
};

/**
 * Fetch distance data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Distance data
 */
const fetchDistanceData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getDistanceWalkingRunning === 'function') {
      console.log('Fetching distance data with JS wrapper');
      AppleHealthKit.getDistanceWalkingRunning(options, (err, results) => {
        if (err) {
          console.log('Error getting distance with JS wrapper:', err);
          tryNativeDistance();
        } else {
          console.log('Distance from JS wrapper:', results);
          const meters = results.value || 0;
          resolve([{
            distance: {
              inMeters: meters,
              inKilometers: meters / 1000,
              inMiles: meters / 1609.34,
              inFeet: meters * 3.28084,
              inInches: meters * 39.3701
            },
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      tryNativeDistance();
    }

    function tryNativeDistance() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getDistanceWalkingRunning === 'function') {
        console.log('Fetching distance data with native module');
        NativeAppleHealthKit.getDistanceWalkingRunning(options, (err, results) => {
          if (err) {
            console.log('Error getting distance with native module:', err);
            resolve([]);
          } else {
            console.log('Distance from native module:', results);
            const meters = results.value || 0;
            resolve([{
              distance: {
                inMeters: meters,
                inKilometers: meters / 1000,
                inMiles: meters / 1609.34,
                inFeet: meters * 3.28084,
                inInches: meters * 39.3701
              },
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch distance data');
        resolve([]);
      }
    }
  });
};

/**
 * Fetch heart rate data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Heart rate data
 */
const fetchHeartRateData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getHeartRateSamples === 'function') {
      console.log('Fetching heart rate data with JS wrapper');
      AppleHealthKit.getHeartRateSamples(options, (err, results) => {
        if (err) {
          console.log('Error getting heart rate with JS wrapper:', err);
          tryNativeHeartRate();
        } else {
          console.log('Heart rate from JS wrapper:', results);
          resolve([{
            samples: (results || []).map(sample => ({
              beatsPerMinute: sample.value,
              time: sample.startDate
            })),
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      tryNativeHeartRate();
    }

    function tryNativeHeartRate() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getHeartRateSamples === 'function') {
        console.log('Fetching heart rate data with native module');
        NativeAppleHealthKit.getHeartRateSamples(options, (err, results) => {
          if (err) {
            console.log('Error getting heart rate with native module:', err);
            resolve([]);
          } else {
            console.log('Heart rate from native module:', results);
            resolve([{
              samples: (results || []).map(sample => ({
                beatsPerMinute: sample.value,
                time: sample.startDate
              })),
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch heart rate data');
        resolve([]);
      }
    }
  });
};

/**
 * Fetch calories data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Calories data
 */
const fetchCaloriesData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getActiveEnergyBurned === 'function') {
      console.log('Fetching calories data with JS wrapper');
      AppleHealthKit.getActiveEnergyBurned(options, (err, results) => {
        if (err) {
          console.log('Error getting calories with JS wrapper:', err);
          tryNativeCalories();
        } else {
          console.log('Calories from JS wrapper:', results);
          const calories = results.value || 0;
          resolve([{
            energy: {
              inCalories: calories,
              inKilocalories: calories / 1000,
              inJoules: calories * 4.184,
              inKilojoules: calories * 0.004184
            },
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      tryNativeCalories();
    }

    function tryNativeCalories() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getActiveEnergyBurned === 'function') {
        console.log('Fetching calories data with native module');
        NativeAppleHealthKit.getActiveEnergyBurned(options, (err, results) => {
          if (err) {
            console.log('Error getting calories with native module:', err);
            resolve([]);
          } else {
            console.log('Calories from native module:', results);
            const calories = results.value || 0;
            resolve([{
              energy: {
                inCalories: calories,
                inKilocalories: calories / 1000,
                inJoules: calories * 4.184,
                inKilojoules: calories * 0.004184
              },
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch calories data');
        resolve([]);
      }
    }
  });
};

/**
 * Fetch flights climbed data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Flights data
 */
const fetchFlightsData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getFlightsClimbed === 'function') {
      console.log('Fetching flights data with JS wrapper');
      AppleHealthKit.getFlightsClimbed(options, (err, results) => {
        if (err) {
          console.log('Error getting flights with JS wrapper:', err);
          tryNativeFlights();
        } else {
          console.log('Flights from JS wrapper:', results);
          resolve([{
            count: results.value || 0,
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      tryNativeFlights();
    }

    function tryNativeFlights() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getFlightsClimbed === 'function') {
        console.log('Fetching flights data with native module');
        NativeAppleHealthKit.getFlightsClimbed(options, (err, results) => {
          if (err) {
            console.log('Error getting flights with native module:', err);
            resolve([]);
          } else {
            console.log('Flights from native module:', results);
            resolve([{
              count: results.value || 0,
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch flights data');
        resolve([]);
      }
    }
  });
};

/**
 * Fetch sleep data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Sleep data
 */
const fetchSleepData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getSleepSamples === 'function') {
      console.log('Fetching sleep data with JS wrapper');
      AppleHealthKit.getSleepSamples(options, (err, results) => {
        if (err) {
          console.log('Error getting sleep with JS wrapper:', err);
          tryNativeSleep();
        } else {
          console.log('Sleep from JS wrapper:', results);
          resolve([{
            samples: (results || []).map(sample => ({
              value: sample.value,
              startDate: sample.startDate,
              endDate: sample.endDate
            })),
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      tryNativeSleep();
    }

    function tryNativeSleep() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getSleepSamples === 'function') {
        console.log('Fetching sleep data with native module');
        NativeAppleHealthKit.getSleepSamples(options, (err, results) => {
          if (err) {
            console.log('Error getting sleep with native module:', err);
            resolve([]);
          } else {
            console.log('Sleep from native module:', results);
            resolve([{
              samples: (results || []).map(sample => ({
                value: sample.value,
                startDate: sample.startDate,
                endDate: sample.endDate
              })),
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch sleep data');
        resolve([]);
      }
    }
  });
};

/**
 * Send health data to the backend using the same API endpoints as Android (aggregate backend)
 * @param {Array} result - Health data to send
 * @param {boolean} callDumpApi - Whether to call the dump API
 * @returns {Promise<void>}
 */
const sendHealthDataToAggregateBackend = async (result, callDumpApi) => {
  try {
    const deviceId = await DeviceInfo.getUniqueId();
    console.log('Apple Health data fetched:', JSON.stringify(result));

    if (!callDumpApi) {
      // Using the same API endpoint as Android for system activity
      const res = await apiClient.post("/user-health/system-activity", {
        deviceId: deviceId,
        os: Platform.OS,
        source: "apple",
        dumps: result.filter((item) => {
          if (item.data.length > 0) return item;
        }),
      });
      console.log("Send result /user-health/system-activity:", res);
    } else {
      // Using the same API endpoint as Android for health dump
      const res = await apiClient.post("/health-dump", {
        deviceId: deviceId,
        os: Platform.OS,
        source: "apple",
        data: result.filter((item) => {
          if (item.data.length > 0) return item;
        }),
      });
      console.log("Send result /health-dump:", res);
    }
  } catch (error) {
    console.log("Send Apple Health data error:", error);
  }
};
