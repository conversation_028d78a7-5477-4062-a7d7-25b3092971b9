import getAllFAQResponseSchema from 'schemas/help&faqSchema/getAllFAQResponseSchema';
import apiClient from './axiosInstance';
import getAllHelpResponseSchema from 'schemas/help&faqSchema/getAllHelpResponseSchema';
import helpDetailsSchema from 'schemas/help&faqSchema/getHelpDetailsResponseSchema';

const FAQ_HelpService = {
    getAllFAQs: async ({ page, searchQuery }) => {
        try {
            const res = await apiClient.get(`/faq?page=${page}&title=${searchQuery}`);

            const faqs = getAllFAQResponseSchema.safeParse(res?.faqs);

            if (!faqs.success) {
                throw new Error("Unable to load FAQs. Please try again later.");
            }

            return {
                success: true,
                data: faqs.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching FAQs'
            };
        }
    },

    getAllHelps: async (page, search = "") => {
        try {
            const res = await apiClient.get(`/help?search=${search}${page ? "&page=" + page : ""}`);

            const helps = getAllHelpResponseSchema.safeParse(res?.data);

            if (!helps.success) {
                throw new Error("Unable to load Helps. Please try again later.");
            }

            return {
                success: true,
                data: helps.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching Helps'
            };
        }
    },

    getHelp: async (id) => {
        try {
            const res = await apiClient.get(`/help/${id}`);

            const help = helpDetailsSchema.safeParse(res?.help);

            if (!help.success) {
                throw new Error("Unable to load Help. Please try again later.");
            }

            return {
                success: true,
                data: help.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching Helps'
            };
        }
    },
};

export default FAQ_HelpService;