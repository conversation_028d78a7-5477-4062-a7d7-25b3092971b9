import * as SecureStore from 'expo-secure-store';
import apiClient from './axiosInstance';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;

const contactUsService = {
    sendQuery: async (queryData) => {
        try {
            const token = await SecureStore.getItemAsync('accessToken');

            await apiClient.post(`/contact`, queryData, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'multipart/form-data',
                },
            });

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || 'Error while sending query',
            };
        }
    },
};

export default contactUsService;
