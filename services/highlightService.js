import { z } from "zod";
import apiClient from "./axiosInstance";

const highlightResponseSchema = z.array(z.string());

export const highlightsService = {
  getHighlights: async (filter = "mood") => {
    try {
      const response = await apiClient.get(`/user_highlights/${filter}`);

      const highlights = highlightResponseSchema.safeParse(response?.data);

      if (!highlights.success) {
        throw new Error("Unable to get highlights. Please try again later.");
      }

      return {
        success: true,
        data: highlights.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || `Error fetching ${filter} highlights`,
      };
    }
  },
};
