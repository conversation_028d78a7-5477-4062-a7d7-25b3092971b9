import getChatbotHistoryResponseSchema from 'schemas/chatbotSchema/getChatbotHistoryResponseSchema';
import apiClient from './axiosInstance';
import * as SecureStore from 'expo-secure-store';
import sendChatBogMessageResponseSchema from 'schemas/chatbotSchema/sendChatBogMessageResponseSchema';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;
const CHATBOT_SESSION_ID = 'chatbot_session_id';

const chatbotService = {

    getSessionId: async () => {
        try {
            const sessionId = await SecureStore.getItemAsync(CHATBOT_SESSION_ID);
            return sessionId;
        } catch (error) {
            return null;
        }
    },

    setSessionId: async (sessionId) => {
        try {
            await SecureStore.setItemAsync(CHATBOT_SESSION_ID, sessionId);
            return true;
        } catch (error) {
            return false;
        }
    },

    // Get chat session history
    getChatHistory: async (sessionId) => {
        try {
            if (!sessionId) return { success: false, error: 'No session ID provided' };

            const response = await apiClient.get(`${apiUrl}/chatbot/${sessionId}`);

            const getChatbotHistoryResponse = getChatbotHistoryResponseSchema.safeParse(response?.data);

            if (!getChatbotHistoryResponse.success) {
                throw new Error("Unable to get chat history. Please try again later.");
            }

            // Format messages to match our app's structure
            const formattedMessages = getChatbotHistoryResponse?.data?.messages.map(msg => ({
                id: msg?._id,
                text: msg?.content,
                sender: msg?.role === 'assistant' ? 'bot' : 'user',
                timestamp: msg?.timestamp
            }));

            return {
                success: true,
                data: {
                    messages: formattedMessages,
                    sessionId: getChatbotHistoryResponse?.data?.sessionId,
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching chat history'
            };
        }
    },

    // Send a message to the chatbot
    sendMessage: async (message, sessionId = null) => {
        try {

            const requestBody = { question: message };
            if (sessionId) {
                requestBody.sessionId = sessionId;
            }

            const response = await apiClient.post(`${apiUrl}/chatbot`, requestBody);
            console.log("chatbot response ===", response);
            const sendChatBogMessageResponse = sendChatBogMessageResponseSchema.safeParse(response?.data);

            if (!sendChatBogMessageResponse.success) {
                throw new Error("Unable to send message. Please try again later.");
            }

            return {
                success: true,
                data: sendChatBogMessageResponse.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || 'Error sending message to chatbot'
            };
        }
    },

    // Send a message with attachments to the chatbot
    // sendMessageWithAttachments: async (message, attachments) => {
    //     try {
    //         const token = await SecureStore.getItemAsync('accessToken');
    //         const sessionId = await chatbotService.getSessionId();
    //         const formData = new FormData();

    //         formData.append('message', message);
    //         if (sessionId) {
    //             formData.append('sessionId', sessionId);
    //         }

    //         // Add attachments to form data
    //         attachments.forEach((file, index) => {
    //             formData.append('attachments', {
    //                 uri: file.uri,
    //                 name: file.name || `file_${index}.${file.uri.split('.').pop()}`,
    //                 type: file.type || file.mimeType || 'application/octet-stream',
    //             });
    //         });

    //         const response = await apiClient.post(`${apiUrl}/chatbot/message/attachment`, formData, {
    //             headers: {
    //                 'Authorization': `Bearer ${token}`,
    //                 'Content-Type': 'multipart/form-data',
    //             },
    //         });

    //         if (response.data?.data?.sessionId) {
    //             await chatbotService.setSessionId(response.data.data.sessionId);
    //         }

    //         return {
    //             success: true,
    //             data: response.data,
    //         };
    //     } catch (error) {
    //         return {
    //             success: false,
    //             message: error.message || 'Error sending message with attachments to chatbot',
    //             error: error.response?.data,
    //         };
    //     }
    // },
};

export default chatbotService;
