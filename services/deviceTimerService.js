import getDayName from "utils/dateandtimeformatters/getDayName";
import apiClient from "./axiosInstance";
import { getTimePeriod } from "utils/userrecords/getTimePeriod";
import createTimerResponseSchema from "schemas/deviceTimerSchema/createTimerResponseSchema";
import updateTimerResponseSchema from "schemas/deviceTimerSchema/updateTimerResponseSchema";
import getTimerHistoryResponseSchema from "schemas/deviceTimerSchema/getTimerHistoryResponseSchema";
import { deviceUsageGraphResponseSchema } from "schemas/deviceTimerSchema/deviceUsageGraphResponseSchema";

export const deviceTimerService = {
  // Start a new timer
  createTimer: async ({ startTime, durationSet }) => {
    try {
      const res = await apiClient.post("/device_timer", {
        startTime: startTime,
        durationSet: durationSet,
      });

      const createTimerResponse = createTimerResponseSchema.safeParse(res?.data);

      if (!createTimerResponse.success) {
        throw new Error("Unable to create timer. Please try again later.");
      }

      return {
        success: true,
        message: "Device usage record created successfully",
        data: createTimerResponse.data || null,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error creating timer.",
      };
    }
  },

  // Update a timer session
  updateTimerSession: async ({ sessionId, durationConsumed }) => {
    try {

      const res = await apiClient.put(`/device_timer/${sessionId}`, {
        durationConsumed: durationConsumed,
      });

      const updateTimerResponse = updateTimerResponseSchema.safeParse(res?.data);

      if (!updateTimerResponse.success) {
        throw new Error("Unable to update timer. Please try again later.");
      }

      return {
        success: true,
        message: "Device usage session updated successfully",
        data: updateTimerResponse?.data || null,
      };

    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error updating timer session.",
      };
    }
  },

  // End a timer session
  endTimerSession: async ({ sessionId, durationConsumed, endTime }) => {
    try {
      await apiClient.put(`/device_timer/end/${sessionId}`, {
        durationConsumed: durationConsumed,
        endTime: endTime,
      });

      return {
        success: true,
        message: "Device usage session ended and summary updated.",
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error ending timer session.",
      };
    }
  },

  // Get today's timer history
  getAllTimerHistory: async () => {
    try {
      const today = new Date().toISOString().split("T")[0];;
      const res = await apiClient.get(`/device_timer?date=${today}`);

      const getTimerHistoryResponse = getTimerHistoryResponseSchema.safeParse(res?.data);

      if (!getTimerHistoryResponse.success) {
        throw new Error("Unable to get timer history. Please try again later.");
      }

      return {
        success: true,
        data: getTimerHistoryResponse.data || [],
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching timer history.",
        data: [],
      };
    }
  },

  // Add a past timer entry
  addMoreTimer: async ({ startTime, durationConsumed }) => {
    try {
      await apiClient.post("/device_timer/add_more", {
        startTime: startTime,
        durationConsumed: durationConsumed
      });

      return {
        success: true,
        message: "Device usage session created and summary updated.",
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error adding past timer entry.",
      };
    }
  },

  // Update a past timer entry
  updateAddMoreTimer: async ({ id, startTime, durationConsumed }) => {
    try {
      await apiClient.put(`/device_timer/add_more/${id}`, {
        startTime: startTime,
        durationConsumed: durationConsumed,
      });

      return {
        success: true,
        message: "Device usage session updated successfully",
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error updating timer entry.",
      };
    }
  },

  getTimerGraphRecords: async (filter) => {
    try {
      const res = await apiClient.get(
        `/device_timer_analytics?filter=${filter}`
      );

      const timerGraphDataSanitized = deviceUsageGraphResponseSchema.safeParse({
        filter: filter,
        data: res?.data,
      });

      if (!timerGraphDataSanitized.success) {
        throw new Error("Unable to get timer graph data. Please try again later.");
      }

      const timerGraphData = timerGraphDataSanitized.data.data;

      let timerGraphRecords = [];
      let timePeriod = null;

      if (filter === "weekly") {
        timerGraphRecords = timerGraphData.map((item) => {
          return {
            value: item?.averageDeviceUsage
              ? Number(item?.averageDeviceUsage)
              : 0,
            label: getDayName(item.period).substring(0, 1),
          };
        });

        timePeriod = getTimePeriod(
          timerGraphData[0].period,
          timerGraphData[timerGraphData.length - 1].period
        );
      } else if (filter === "monthly") {
        timerGraphRecords = timerGraphData.map((item) => {
          const weekNumber = item.period.match(/week_(\d+)/);
          return {
            value: item?.averageDeviceUsage
              ? Number(item?.averageDeviceUsage)
              : 0,
            label: `W${weekNumber[1]}`,
          };
        });

        timePeriod = getTimePeriod(
          timerGraphData[0].startDate,
          timerGraphData[timerGraphData.length - 1].endDate
        );
      } else if (filter === "half_yearly") {
        timerGraphRecords = timerGraphData.map((item) => {
          return {
            value: item?.averageDeviceUsage
              ? Number(item?.averageDeviceUsage)
              : 0,
            label: item.period.substring(0, 1),
          };
        });
        timePeriod =
          timerGraphData[0].year !== timerGraphData[timerGraphData.length - 1].year
            ? `${timerGraphData[0].period.substring(0, 3)} ${timerGraphData[0].year
            } - ${timerGraphData[timerGraphData.length - 1].period.substring(0, 3)} ${timerGraphData[timerGraphData.length - 1].year
            }`
            : `${timerGraphData[0].period.substring(0, 3)} - ${timerGraphData[
              timerGraphData.length - 1
            ].period.substring(0, 3)} ${timerGraphData[0].year}`;
      } else if (filter === "yearly") {
        timerGraphRecords = timerGraphData.map((item) => {
          return {
            value: item?.averageDeviceUsage
              ? Number(item?.averageDeviceUsage)
              : 0,
            label: item.period.substring(0, 1),
          };
        });
        timePeriod =
          timerGraphData[0].year !== timerGraphData[timerGraphData.length - 1].year
            ? `${timerGraphData[0].period.substring(0, 3)} ${timerGraphData[0].year
            } - ${timerGraphData[timerGraphData.length - 1].period.substring(0, 3)} ${timerGraphData[timerGraphData.length - 1].year
            }`
            : `${timerGraphData[0].period.substring(0, 3)} - ${timerGraphData[
              timerGraphData.length - 1
            ].period.substring(0, 3)} ${timerGraphData[0].year}`;
      }

      return {
        success: true,
        data: {
          graphData: timerGraphRecords,
          timePeriod: timePeriod,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching recent sleep logged data.",
      };
    }
  },
};
