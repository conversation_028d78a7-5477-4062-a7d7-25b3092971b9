import getUserProfileDetailsSchema from 'schemas/userSchema/getUserProfileDetailsReponseSchema';
import apiClient from './axiosInstance';
import updatedUserProfileDetailsResponseSchema from 'schemas/userSchema/updatedUserProfileDetailsResponseSchema';
import completeUserProfileResponseSchema from 'schemas/userSchema/completeUserProfileResponseSchema';
const apiUrl = process.env.EXPO_PUBLIC_API_URL;

const userService = {
    getCurrentUser: async () => {
        try {
            const res = await apiClient.get(`${apiUrl}/profile`);

            const user = getUserProfileDetailsSchema.safeParse(res?.user);

            if (!user.success) {
                throw new Error("Unable to load user profile. Please try again later.");
            }

            return {
                success: true,
                data: user.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || 'Error fetching user profile'
            };
        }
    },

    updateUser: async (userData) => {
        try {
            const res = await apiClient.put(`${apiUrl}/profile`, userData);

            const updatedUser = updatedUserProfileDetailsResponseSchema.safeParse(res?.user);

            if (!updatedUser.success) {
                throw new Error("Unable to update user profile. Please try again later.");
            }

            return {
                success: true,
                data: updatedUser.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || 'Error updating user profile'
            };
        }
    },

    completeUserProfile: async (userProfileData) => {
        try {
            const res = await apiClient.put(`${apiUrl}/profile/setup_profile`, userProfileData);

            const completeUserProfileResponse = completeUserProfileResponseSchema.safeParse(res?.user);

            if (!completeUserProfileResponse.success) {
                throw new Error("Unable to complete user profile. Please try again later.");
            }

            return {
                success: true,
                data: completeUserProfileResponse.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || 'Something went wrong. Please try again.'
            };
        }
    },
};

export default userService;