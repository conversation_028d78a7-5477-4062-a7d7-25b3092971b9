import getDayName from "utils/dateandtimeformatters/getDayName";
import apiClient from "./axiosInstance";
import { getTimePeriod } from "utils/userrecords/getTimePeriod";
import { getAllActivitiesResponseSchema } from "schemas/activitySchema/getAllActivitiesResponseSchema";
import getActivityResponseSchema from "schemas/activitySchema/getActivityResponseSchema";
import activityLedgerResponseSchema from "schemas/activitySchema/activityLedgerResponseSchema";
import recentActivityResponseSchema from "schemas/activitySchema/recentActivityResponseSchema";
import activityHighlightsResponseSchema from "schemas/activitySchema/activityHighlightsResponseSchema";
import activityRecommendedVideoResponseSchema from "schemas/activitySchema/activityRecommendedVideoResponseSchema";
import activityGraphResponseSchema from "schemas/activitySchema/activityGraphResponseSchema";

export const activityService = {

    createActivity: async ({ activityType, durationInMinutes }) => {
        try {
            await apiClient.post("/user-health/activity-manual", {
                activityType,
                durationInMinutes,
            });

            return {
                success: true,
                message: "Activity created successfully",
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error creating activity"
            };
        }
    },

    getAllActivities: async () => {
        try {
            const res = await apiClient.get("/user-health/activity-manual");

            const activities = getAllActivitiesResponseSchema.safeParse(res?.data);

            if (!activities.success) {
                throw new Error("Unable to get activities. Please try again later.");
            }

            return {
                success: true,
                data: activities.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activities"
            };
        }
    },

    getActivityById: async (id) => {
        try {
            const res = await apiClient.get(`/user-health/single-activity-manual/${id}`);

            const activity = getActivityResponseSchema.safeParse(res.data);

            if (!activity.success) {
                throw new Error("Unable to get activity. Please try again later.");
            }

            return {
                success: true,
                data: activity.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activity"
            };
        }
    },

    updateActivity: async ({ id, durationInMinutes }) => {
        try {
            await apiClient.put(`/user-health/edit-activity-manual/${id}`, {
                durationInMinutes,
            });

            return {
                success: true,
                message: "Activity updated successfully",
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error updating activity"
            };
        }
    },

    deleteActivity: async (id) => {
        try {
            await apiClient.put(`/user-health/delete-activity-manual/${id}`);

            return {
                success: true,
                message: "Activity deleted successfully",
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error deleting activity"
            };
        }
    },

    getTodaysLedger: async () => {
        try {
            const res = await apiClient.get("/user-health/health-ledger");

            const ledgerData = activityLedgerResponseSchema.safeParse(res?.data);

            if (!ledgerData.success) {
                throw new Error("Unable to get ledger. Please try again later.");
            }

            return {
                success: true,
                data: ledgerData.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching ledger"
            };
        }
    },

    getRecentActivity: async () => {
        try {
            const res = await apiClient.get("/user-health/last-log-activity");

            const recentActivity = recentActivityResponseSchema.safeParse(res?.data);

            if (!recentActivity.success) {
                throw new Error("Unable to get recent activity. Please try again later.");
            }

            return {
                success: true,
                data: recentActivity.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recent activity"
            };
        }
    },

    getHighlights: async () => {
        try {
            const response = await apiClient.get(`/health/highlight`);

            const highlights = activityHighlightsResponseSchema.safeParse(response?.data);

            if (!highlights.success) {
                throw new Error("Unable to get highlights. Please try again later.");
            }

            return {
                success: true,
                data: highlights.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || `Error fetching activity highlights`,
            };
        }
    },

    getRecommendedVideo: async () => {
        try {
            const response = await apiClient.get(`/activity_recommendation`);

            const recommendedVideoData = activityRecommendedVideoResponseSchema.safeParse(response?.video);

            if (!recommendedVideoData.success) {
                throw new Error("Unable to get recommended video. Please try again later.");
            }

            return {
                success: true,
                data: recommendedVideoData?.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || `Error fetching recommended video`,
            };
        }
    },

    getActivityGraph: async ({ filter }) => {
        try {
            const response = await apiClient.get(`/health-analytics/activity-analytics?period=${filter}`);

            const activityGraphDataSanitized = activityGraphResponseSchema.safeParse({
                filter: filter,
                data: response?.data,
            });

            if (!activityGraphDataSanitized.success) {
                throw new Error("Unable to get activity graph data. Please try again later.");
            }

            const activityGraphData = activityGraphDataSanitized.data.data;

            let responseData = [];
            let timeRange = null;

            if (filter === "weekly") {
                responseData = activityGraphData?.map(item => {
                    return {
                        value: item?.averageBurnedCalories || 0,
                        label: getDayName(item.period).substring(0, 1)
                    };
                });

                timeRange = getTimePeriod(activityGraphData[0]?.period, activityGraphData[activityGraphData.length - 1]?.period);
            }
            else if (filter === "monthly") {
                responseData = activityGraphData?.map(item => {
                    const weekNumber = item?.period?.match(/week_(\d+)/);
                    return {
                        value: item?.averageBurnedCalories || 0,
                        label: `W${weekNumber[1]}`
                    };
                });
                timeRange = getTimePeriod(activityGraphData[0].startDate, activityGraphData[activityGraphData.length - 1].endDate);
            }
            else if (filter == "half_yearly") {
                responseData = activityGraphData?.map(item => {
                    return {
                        value: item?.averageBurnedCalories || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = activityGraphData?.[0].year !== activityGraphData[activityGraphData?.length - 1].year
                    ? `${activityGraphData?.[0].period.substring(0, 3)} ${activityGraphData?.[0].year} - ${activityGraphData[activityGraphData.length - 1].period.substring(0, 3)} ${activityGraphData[activityGraphData.length - 1].year}`
                    : `${activityGraphData?.[0].period.substring(0, 3)} - ${activityGraphData[activityGraphData.length - 1].period.substring(0, 3)} ${activityGraphData?.[0].year}`;
            }
            else if (filter === "yearly") {
                responseData = activityGraphData?.map(item => {
                    return {
                        value: item?.averageBurnedCalories || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = activityGraphData[0].year !== activityGraphData[activityGraphData.length - 1].year
                    ? `${activityGraphData[0].period} ${activityGraphData[0].year} - ${activityGraphData[activityGraphData.length - 1].period} ${activityGraphData[activityGraphData.length - 1].year}`
                    : `${activityGraphData[0].period} - ${activityGraphData[activityGraphData.length - 1].period} ${activityGraphData[0].year}`;
            }

            return {
                success: true,
                data: {
                    activityData: responseData,
                    timeRange: timeRange
                }
            };

        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activity graph",
            };
        }
    },
};