import getDayName from "utils/dateandtimeformatters/getDayName";
import apiClient from "./axiosInstance";
import { getTimePeriod } from "utils/userrecords/getTimePeriod";
import sleepLastLoggedResponseSchema from "schemas/sleepSchema/getLastLoggedSleepResponse";
import getSleepRecordResponseSchema from "schemas/sleepSchema/getSleepRecordResponse";
import sleepGraphRecordsResponseSchema from "schemas/sleepSchema/sleepGraphRecordsResponseSchema";

export const recordSleepService = {
  logSleepData: async ({ date, duration }) => {
    try {
      await apiClient.post("/sleep_records", {
        numOfHours: duration,
        date: date,
      });

      return {
        success: true,
        message: "Sleep logged successfully!!",
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error recoding sleep.",
      };
    }
  },
  getLastLoggedData: async () => {
    try {
      const res = await apiClient.get("/sleep_records");

      const sleepData = sleepLastLoggedResponseSchema.safeParse(res?.data);

      if (!sleepData.success) {
        throw new Error("Unable to get logged sleep. Please try again later.");
      }

      return {
        success: true,
        data: sleepData.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching recent sleep logged data.",
      };
    }
  },
  getSleepRecord: async (date) => {
    try {
      const res = await apiClient.get(`/sleep_records?date=${date}`);

      const sleepRecordData = getSleepRecordResponseSchema.safeParse(res?.data);

      if (!sleepRecordData.success) {
        throw new Error("Unable to get sleep record. Please try again later.");
      }

      return {
        success: true,
        data: sleepRecordData.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching sleep records.",
      };
    }
  },
  getSleepGraphRecords: async (filter) => {
    try {
      const res = await apiClient.get(`/sleep_analytics?filter=${filter}`);

      let sleepGraphRecords = [];
      let timePeriod = null;

      const graphDataSanitized = sleepGraphRecordsResponseSchema.safeParse({
        filter: filter,
        data: res.data,
      });

      if (!graphDataSanitized.success) {
        throw new Error("Unable to get sleep graph data. Please try again later.");
      }

      const graphData = graphDataSanitized.data;

      if (filter === "weekly") {
        sleepGraphRecords = graphData.data.map((item) => {
          return {
            value: item?.averageSleep || 0,
            label: getDayName(item.period).substring(0, 1),
          };
        });

        timePeriod = getTimePeriod(
          graphData.data[0].period,
          graphData.data[graphData.data.length - 1].period
        );
      } else if (filter === "monthly") {
        sleepGraphRecords = graphData.data.map((item) => {
          const weekNumber = item.period.match(/week_(\d+)/);
          return {
            value: item?.averageSleep || 0,
            label: `W${weekNumber[1]}`,
          };
        });

        timePeriod = getTimePeriod(
          graphData.data[0].startDate,
          graphData.data[graphData.data.length - 1].endDate
        );
      } else if (filter === "half_yearly") {
        sleepGraphRecords = graphData.data.map((item) => {
          return {
            value: item?.averageSleep || 0,
            label: item.period.substring(0, 1),
          };
        });
        timePeriod =
          graphData.data[0].year !== graphData.data[graphData.data.length - 1].year
            ? `${graphData.data[0].period.substring(0, 3)} ${graphData.data[0].year
            } - ${graphData.data[graphData.data.length - 1].period.substring(0, 3)} ${graphData.data[graphData.data.length - 1].year
            }`
            : `${graphData.data[0].period.substring(0, 3)} - ${graphData.data[
              graphData.data.length - 1
            ].period.substring(0, 3)} ${graphData.data[0].year}`;
      } else if (filter === "yearly") {
        sleepGraphRecords = graphData.data.map((item) => {
          return {
            value: item?.averageSleep || 0,
            label: item.period.substring(0, 1),
          };
        });
        timePeriod =
          graphData.data[0].year !== graphData.data[graphData.data.length - 1].year
            ? `${graphData.data[0].period.substring(0, 3)} ${graphData.data[0].year
            } - ${graphData.data[graphData.data.length - 1].period.substring(0, 3)} ${graphData.data[graphData.data.length - 1].year
            }`
            : `${graphData.data[0].period.substring(0, 3)} - ${graphData.data[
              graphData.data.length - 1
            ].period.substring(0, 3)} ${graphData.data[0].year}`;
      }

      return {
        success: true,
        data: {
          graphData: sleepGraphRecords,
          timePeriod: timePeriod,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching recent sleep logged data.",
      };
    }
  },
};
