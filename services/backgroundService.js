import * as BackgroundFetch from 'expo-background-fetch';

const registerBackgroundTask = async () => {
    const status = await BackgroundFetch.getStatusAsync();
    if (status === BackgroundFetch.BackgroundFetchStatus.Available) {
        await BackgroundFetch.registerTaskAsync('background-fetch', {
            minimumInterval: 60,
            stopOnTerminate: false,
            startOnBoot: true,
        });
    }
};

const unregisterBackgroundTask = async () => {
    await BackgroundFetch.unregisterTaskAsync('background-fetch');
}

export { registerBackgroundTask, unregisterBackgroundTask };
