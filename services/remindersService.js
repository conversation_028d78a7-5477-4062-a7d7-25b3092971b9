import reminderDetailsSchema from "schemas/reminderSchema/getReminderDetailsResponseSchema";
import getRemindersResponseSchema from "schemas/reminderSchema/getRemindersResponseSchema";
import apiClient from "./axiosInstance";

export const reminderService = {
    getReminders: async (category, page) => {
        try {
            const res = await apiClient.get(`/reminders?category=${category}&page=${page}`);
            const reminders = getRemindersResponseSchema.safeParse(res?.data);

            if (!reminders.success) {
                throw new Error("Unable to load reminders. Please try again later.");
            }

            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error fetching remainders"
            };
        }
    },
    getSingleReminder: async (id) => {
        try {
            const res = await apiClient.get(`/reminders/${id}`);

            const reminder = reminderDetailsSchema.safeParse(res?.data);

            if (!reminder.success) {
                throw new Error("Unable to load reminder. Please try again later.");
            }

            return {
                success: true,
                data: reminder.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error fetching remainder,"
            };
        }
    },
    createReminder: async ({ category, label, timeStamps, sound, redirectURL }) => {
        try {
            const date = new Date(timeStamps);

            await apiClient.post("/reminders", {
                category: category,
                label: label,
                frontend_screen_url: redirectURL,
                time: date,
                sound: sound,
            });

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error creating remainder"
            };
        }
    },
    updateReminder: async ({ id, label, timeStamps, sound }) => {
        try {
            const date = new Date(timeStamps);

            await apiClient.put(`/reminders/${id}`, {
                label: label,
                time:date,
                sound: sound,
            });

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error updating remainder"
            };
        }
    },
    deleteRemainder: async (id) => {
        try {
            await apiClient.put(`/reminders/delete/${id}`);

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error deleting remainder"
            };
        }
    },
};