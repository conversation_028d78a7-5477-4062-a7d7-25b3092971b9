import getDayName from 'utils/dateandtimeformatters/getDayName';
import apiClient from './axiosInstance';
import { getTimePeriod } from 'utils/userrecords/getTimePeriod';
import moodLastLoggedResponseSchema from 'schemas/moodSchema/moodLastLoggedResponseSchema';
import moodRecommendedVideoResponseSchema from 'schemas/moodSchema/moodRecommendedVideoResponseSchema';
import moodGraphResponseSchema from 'schemas/moodSchema/moodGraphResponseDataSchema';

export const moodService = {
    getMoodOptions: async () => {
        try {
            const MoodOptions = [
                {
                    label: "Happy",
                    value: "Happy",
                },
                {
                    label: "Moderately Happy",
                    value: "Moderately Happy",
                },
                {
                    label: "Irritated",
                    value: "Irritated",
                },
                {
                    label: "Anxious",
                    value: "Anxious",
                },
                {
                    label: "Sad",
                    value: "Sad",
                }
            ];
            return {
                success: true,
                data: MoodOptions
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching mood options"
            };
        }
    },
    getHungerOptions: async () => {
        try {
            const HungerOptions = [
                {
                    label: "High",
                    value: "High",
                },
                {
                    label: "Moderate",
                    value: "Moderate",
                },
                {
                    label: "Barely",
                    value: "Barely",
                },
                {
                    label: "Mild",
                    value: "Mild",
                }
            ];
            return {
                success: true,
                data: HungerOptions
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching hunger options"
            };
        }
    },
    getLastMood: async () => {
        try {
            const response = await apiClient.get("/mood_records");

            const lastLoggedMoodData = moodLastLoggedResponseSchema.safeParse(response?.data);

            if (!lastLoggedMoodData.success) {
                throw new Error("Unable to get logged mood. Please try again later.");
            }

            return {
                success: true,
                data: lastLoggedMoodData?.data || null
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching last mood"
            };
        }
    },
    createMood: async ({ mood, hungerLevel }) => {
        try {
            await apiClient.post("/mood_records", {
                moodType: mood,
                hungerLevel: hungerLevel
            });

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error saving mood."
            };
        }
    },

    editMood: async ({ mood, hungerLevel, id }) => {
        try {
            await apiClient.put(`/mood_records/${id}`, {
                moodType: mood,
                hungerLevel: hungerLevel
            });
            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error editing mood."
            };
        }
    },

    getRecommendedVideo: async () => {
        try {
            const response = await apiClient.get('/mood_recommendation');

            const recommendedVideoData = moodRecommendedVideoResponseSchema.safeParse(response?.data);

            if (!recommendedVideoData.success) {
                throw new Error("Unable to get recommended video. Please try again later.");
            }

            return {
                success: true,
                data: recommendedVideoData?.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recommended video"
            };
        }
    },

    getMoodGraphData: async ({ filter }) => {
        try {
            const response = await apiClient.get(`/mood_analytics?filter=${filter}`,);

            const moodGraphResponseSantized = moodGraphResponseSchema.safeParse({
                filter: filter,
                data: response?.data,
            });

            if (!moodGraphResponseSantized.success) {
                throw new Error("Unable to get mood graph data. Please try again later.");
            }

            const moodGraphData = moodGraphResponseSantized.data.data;

            let responseData = [];
            let timeRange = null;

            if (filter === "weekly") {
                responseData = moodGraphData.map(item => {
                    return {
                        value: item.averageMoodType,
                        label: getDayName(item.period).substring(0, 1)
                    };
                });

                timeRange = getTimePeriod(moodGraphData[0].period, moodGraphData[moodGraphData.length - 1].period);
            }
            else if (filter === "monthly") {
                responseData = moodGraphData.map(item => {
                    const weekNumber = item.period.match(/week_(\d+)/);
                    return {
                        value: item?.averageMoodType,
                        label: `W${weekNumber[1]}`
                    };
                });
                timeRange = getTimePeriod(moodGraphData[0].startDate, moodGraphData[moodGraphData.length - 1].endDate);
            }
            else if (filter == "half_yearly") {
                responseData = moodGraphData.map(item => {
                    return {
                        value: item?.averageMoodType,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = moodGraphData[0].year !== moodGraphData[moodGraphData.length - 1].year
                    ? `${moodGraphData[0].period.substring(0, 3)} ${moodGraphData[0].year} - ${moodGraphData[moodGraphData.length - 1].period.substring(0, 3)} ${moodGraphData[moodGraphData.length - 1].year}`
                    : `${moodGraphData[0].period.substring(0, 3)} - ${moodGraphData[moodGraphData.length - 1].period.substring(0, 3)} ${moodGraphData[0].year}`;
            }
            else if (filter === "yearly") {
                responseData = moodGraphData.map(item => {
                    return {
                        value: item?.averageMoodType,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = moodGraphData[0].year !== moodGraphData[moodGraphData.length - 1].year
                    ? `${moodGraphData[0].period} ${moodGraphData[0].year} - ${moodGraphData[moodGraphData.length - 1].period} ${moodGraphData[moodGraphData.length - 1].year}`
                    : `${moodGraphData[0].period} - ${moodGraphData[moodGraphData.length - 1].period} ${moodGraphData[0].year}`;
            }

            return {
                success: true,
                data: {
                    moodData: responseData,
                    timeRange: timeRange
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recommended video"
            };
        }
    }
};

export default moodService;
