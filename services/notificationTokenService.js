import DeviceInfo from 'react-native-device-info';
import * as Device from "expo-device";
import apiClient from './axiosInstance';

const notificationTokenService = {
    createUserNotificationToken: async (notificationTokenData) => {
        try {
            const response = await apiClient.post("/notification_token", notificationTokenData);
            return response.data;
        } catch (error) {
            throw error.response?.data || error.message;
        }
    },
    toggleNotificationStatus: async (isNotificationActive) => {
        try {
            const deviceId = await DeviceInfo.getUniqueId()

            await apiClient.put("/notification_token", {
                deviceId: deviceId,
                isNotificationActive: isNotificationActive
            });
            return {
                success: true,
                data: "Notification status updated successfully"
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error changing notification active status"
            };
        }
    }
}

export default notificationTokenService