import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import chatbotService from 'services/chatbotService';

const WELCOME_MESSAGE = "Hi! I'm your health assistant. I can help you with nutrition tracking, device pairing, reminders, mood tracking and more. How can I help you today?";

const useChatbotStore = create(
    persist(
        (set, get) => ({
            messages: [],
            sessionId: null,
            isLoading: false,
            isBotTyping: false,
            error: null,
            showSessionPrompt: false,
            predefinedOptions: [
                { id: '1', text: 'How to track my nutrition?' },
                { id: '2', text: 'Help with device pairing' },
                { id: '3', text: 'How to set up reminders?' },
                { id: '4', text: 'Track my mood and sleep' },
                { id: '5', text: 'Contact support team' }
            ],

            // Initialize chatbot
            initializeChatbot: async () => {
                const { sessionId } = get();
                if (sessionId) {
                    set({ showSessionPrompt: true });
                } else {
                    set({
                        messages: [{
                            id: 'welcome',
                            text: WELCOME_MESSAGE,
                            sender: 'bot',
                            timestamp: new Date().toISOString()
                        }]
                    });
                }
            },

            // Load previous chat
            loadPreviousChat: async () => {
                set({ isLoading: true, showSessionPrompt: false });

                const { sessionId } = get();
                const response = await chatbotService.getChatHistory(sessionId);

                if (response.success) {
                    set({
                        messages: [
                            {
                                id: 'welcome',
                                text: WELCOME_MESSAGE,
                                sender: 'bot',
                                timestamp: new Date().toISOString()
                            },
                            ...response.data.messages,
                        ]
                    });
                } else {
                    set({ error: response.error || 'Failed to load chat history' });
                }

                set({ isLoading: false });
            },

            // Reset chat
            resetChat: () => {
                set({
                    messages: [{
                        id: 'welcome',
                        text: WELCOME_MESSAGE,
                        sender: 'bot',
                        timestamp: new Date().toISOString()
                    }],
                    sessionId: null,
                    error: null,
                    showSessionPrompt: false
                });
            },

            // Send message
            sendMessage: async (message) => {
                if (!message.trim()) return;

                const { sessionId } = get();

                // Add user message immediately
                set((state) => ({
                    messages: [...state.messages, {
                        id: `user_${Date.now()}`,
                        text: message,
                        sender: 'user',
                        timestamp: new Date().toISOString()
                    }],
                    isBotTyping: true
                }));

                try {
                    const response = await chatbotService.sendMessage(message, sessionId);

                    if (!response.success) {
                        throw new Error(response.error || 'Failed to send message');
                    }

                    const { sessionId: newSessionId, reply } = response.data;

                    // Update session ID if it's new
                    if (newSessionId && newSessionId !== sessionId) {
                        set({ sessionId: newSessionId });
                    }

                    // Add bot's reply
                    set((state) => ({
                        messages: [...state.messages, {
                            id: `bot_${Date.now()}`,
                            text: reply,
                            sender: 'bot',
                            timestamp: new Date().toISOString()
                        }],
                        isBotTyping: false
                    }));

                } catch (error) {
                    set((state) => ({
                        error: error.message,
                        isBotTyping: false
                    }));
                }
            }
        }),
        {
            name: 'chatbot-storage',
            storage: createJSONStorage(() => AsyncStorage),
            partialize: (state) => ({
                sessionId: state.sessionId,
                messages: state.messages
            })
        }
    )
);

export default useChatbotStore;
