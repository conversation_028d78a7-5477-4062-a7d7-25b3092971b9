
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import * as SecureStore from 'expo-secure-store';
import apiClient from 'services/axiosInstance';
import userService from 'services/userService';
import { getDeviceType } from 'services/pushNotificationRegistrationService';
import DeviceInfo from 'react-native-device-info';
import * as Device from "expo-device";
import { Platform } from 'react-native';

const useAuthStore = create(
  persist(
    (set, get) => ({
      isLoading: false,
      userToken: null,
      user: null,
      isSignout: false,

      setLoading: (isLoading) => set({ isLoading }),

      bootstrapAsync: async () => {
        try {
          set({ isLoading: true });
          const accessToken = await SecureStore.getItemAsync('accessToken');
          const userData = await SecureStore.getItemAsync('userData');
          
          if (accessToken && userData) {
            const user = JSON.parse(userData);
            apiClient.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
            set({ userToken: accessToken, user, isLoading: false });
          } else {
            set({ userToken: null, user: null, isLoading: false });
          }
        } catch (error) {
          console.error('Error restoring token:', error);
          set({ userToken: null, user: null, isLoading: false });
        }
      },

      signIn: async (email, password, pushTokenString) => {
        try {
          const response = await apiClient.post('/login', { email, password });
          const { error, msg, accessToken, user, expiry } = response;
          
          if (error || !accessToken || !user) {
            throw new Error(msg || 'Invalid response from server');
          }

          await SecureStore.setItemAsync('accessToken', accessToken);
          apiClient.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;

          let res = { data: { isNotificationActive: false } };

          if (pushTokenString) {
            const deviceId = await DeviceInfo.getUniqueId();
            const deviceData = {
              notificationToken: pushTokenString,
              deviceId,
              deviceType: getDeviceType(Device.deviceType),
              osType: Platform.OS,
            };

            res = await apiClient.post(`/notification_token`, deviceData);
          }

          await SecureStore.setItemAsync('userData', JSON.stringify(user));
          set({ userToken: accessToken, user, isSignout: false });

          return { success: true, isNotificationActive: res?.data?.isNotificationActive };
        } catch (error) {
          console.error('Login failed:', error);
          return { success: false, error };
        }
      },

      signOut: async () => {
        try {
          set({ isLoading: true });
          const deviceId = await DeviceInfo.getUniqueId();
          await apiClient.get(`/logout/${deviceId}`);
          
          await SecureStore.deleteItemAsync('accessToken');
          await SecureStore.deleteItemAsync('userData');
          delete apiClient.defaults.headers.common['Authorization'];
          
          set({ userToken: null, user: null, isSignout: true, isLoading: false });
        } catch (error) {
          await SecureStore.deleteItemAsync('accessToken');
          await SecureStore.deleteItemAsync('userData');
          delete apiClient.defaults.headers.common['Authorization'];
          set({ userToken: null, user: null, isSignout: true, isLoading: false });
        }
      },

      updateUserProfile: async (userData) => {
        try {
          const response = await userService.updateUser({
            ...userData,
            weight: Number(userData.weight),
          });

          if (!response.success) {
            throw new Error(response.error);
          }

          await SecureStore.setItemAsync('userData', JSON.stringify(response.data));
          set({ user: response.data });

          return { success: true, data: response.data };
        } catch (error) {
          return {
            success: false,
            error: error?.message || "Error updating user profile"
          };
        }
      },

      updateUserProfileLocal: async (userData) => {
        try {
          await SecureStore.setItemAsync('userData', JSON.stringify(userData));
          set({ user: userData });
          return true;
        } catch (error) {
          console.error('Error updating user profile:', error);
          return false;
        }
      }
    }),
    {
      name: 'auth-storage',
      storage: {
        getItem: async (name) => {
          const value = await SecureStore.getItemAsync(name);
          return value ? JSON.parse(value) : null;
        },
        setItem: async (name, value) => {
          await SecureStore.setItemAsync(name, JSON.stringify(value));
        },
        removeItem: async (name) => {
          await SecureStore.deleteItemAsync(name);
        },
      },
    }
  )
);

export default useAuthStore;

