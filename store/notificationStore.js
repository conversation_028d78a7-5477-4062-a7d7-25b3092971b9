
import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { persist, createJSONStorage } from 'zustand/middleware';
import notificationTokenService from 'services/notificationTokenService';

const defaultNotificationState = {
    isNotificationEnabled: false,
    notificationError: null,
    isLoadingNotifications: false
};

const useNotificationStore = create(
    persist(
        (set, get) => ({
            ...defaultNotificationState,

            setNotificationStatus: ({ isNotificationActive }) => set((state) => ({
                ...state,
                isNotificationEnabled: isNotificationActive
            })),

            setIsLoadingNotifications: (loading) => set((state) => ({
                ...state,
                isLoadingNotifications: loading
            })),

            toggleNotifications: async () => {
                set((state) => ({ ...state, isLoadingNotifications: true }));
                
                const currentState = get();
                const res = await notificationTokenService.toggleNotificationStatus(!currentState.isNotificationEnabled);

                if (res.success) {
                    set((state) => ({
                        ...state,
                        isLoadingNotifications: false,
                        isNotificationEnabled: !state.isNotificationEnabled,
                        notificationError: null
                    }));
                } else {
                    set((state) => ({
                        ...state,
                        isLoadingNotifications: false,
                        notificationError: res.error
                    }));
                }
            },

            clearError: () => set((state) => ({ 
                ...state, 
                notificationError: null 
            }))
        }),
        {
            name: 'notification-status-store',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

export default useNotificationStore;

