import { create } from 'zustand';

const useNutritionMealStore = create((set) => ({
    meals: [],
    isChanged: false,
    prevMealsData : [],
    initializeMealsData: (data) => set((state) => ({ meals: [...data], isChanged: false ,prevMealsData:[...data] })),
    addMealData: (data) => set((state) => {
        return { meals: [...state.meals, data], isChanged: true }
    }),
    editMealData: ({ data, index }) => set((state) => ({
        meals: state.meals.map((meal, i) => i === index ? data : meal),
        isChanged: true
    })),
    deleteMealData: (id) => set((state) => ({ meals: state.meals.filter((meal, index) => index != id), isChanged: true })),
    resetMeals: () => set((state) => ({ meals: [], isChanged: false,prevMealsData:[] })),
}));

export default useNutritionMealStore;