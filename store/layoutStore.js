import { create } from 'zustand';

const useLayoutStore = create((set) => ({
  isMenuOpen: false,
  isSettingOpen: false,
  isChatbotOpen: false,

  toggleMenu: (value) => set((state) => ({
    isMenuOpen: value != undefined ? value : !state.isMenuOpen
  })),

  toggleSettings: (value) => set((state) => ({
    isSettingOpen: value != undefined ? value : !state.isSettingOpen
  })),

  toggleChatbot: (value) => set((state) => ({
    isChatbotOpen: value != undefined ? value : !state.isChatbotOpen
  })),
}));

export default useLayoutStore;