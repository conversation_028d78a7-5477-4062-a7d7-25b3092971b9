import { highlightsService } from "services/highlightService";
import { weightLossService } from "services/weightLossService";
import { create } from "zustand";

const initialState = {
  isLoadingUserWeightStore: false,

  isLoadingLastLoggedWeightData: false,
  lastLoggedWeight: null,
  lastLoggedWeightError: null,

  isLoadingWeightGraphData: false,
  weightGraphFilter: "weekly",
  weightGraphData: [],
  weightGraphTimeRange: null,
  weightGraphError: null,

  isLoadingWeightHighlights: false,
  weightHighlights: [],
  weightHighlightsError: null,
};

const useUserWeightStore = create((set, get) => ({
  ...initialState,

  setLoadingUserWeightStore: (loading) =>
    set((state) => ({ ...state, isLoadingUserWeightStore: loading })),

  getLastLoggedWeightData: async () => {
    set((state) => ({ ...state, isLoadingLastLoggedWeightData: true }));

    const res = await weightLossService.getLastLoggedData();

    if (res.success)
      set((state) => ({
        ...state,
        lastLoggedWeight: res.data,
        isLoadingLastLoggedWeightData: false,
      }));
    else
      set((state) => ({
        ...state,
        lastLoggedWeightError: res.error,
        isLoadingLastLoggedWeightData: false,
      }));
  },

  setWeightGraphFilter: (filter) =>
    set((state) => ({ ...state, weightGraphFilter: filter })),

  getWeightGraphData: async () => {
    const filter = get().weightGraphFilter;

    set((state) => ({ ...state, isLoadingWeightGraphData: true }));

    const res = await weightLossService.getWeightGraphData({ filter });

    if (res.success) {
      set((state) => ({
        ...state,
        isLoadingWeightGraphData: false,
        weightGraphData: res.data.weightData,
        weightGraphTimeRange: res.data.timeRange,
      }));
      return true;
    } else {
      set((state) => ({
        ...state,
        isLoadingWeightGraphData: false,
        weightGraphError: res.error,
      }));
      return false;
    }
  },

  getWeightHighlights: async () => {
    set((state) => ({ ...state, isLoadingWeightHighlights: true }));

    const res = await highlightsService.getHighlights("weight");

    if (res.success) {
      set((state) => ({
        ...state,
        isLoadingWeightHighlights: false,
        weightHighlights: res.data,
      }));
    } else {
      set((state) => ({
        ...state,
        isLoadingWeightHighlights: false,
        weightHighlightsError: res.error,
      }));
    }
  },

  clearWeightStoreErrors: () => {
    set((state) => ({
      lastLoggedWeightError: null,
      weightGraphError: null,
      weightHighlightsError: null,
    }));
  },

  resetWeightStore: () => set({ ...initialState }),
}));

export default useUserWeightStore;
