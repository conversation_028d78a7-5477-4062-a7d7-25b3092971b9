import { highlightsService } from "services/highlightService";
import { recordSleepService } from "services/recordSleepService";
import { create } from "zustand";

const initialState = {
  isLoadingSleepData: false,

  isLoadingLastSleepLogged: false,
  lastLoggedSleep: null,
  sleepError: null,

  isLoadingSleepGraphRecords: false,
  sleepRecordTimeFilter: "weekly",
  sleepGraphRecords: [],
  sleepGraphRecordTime: null,
  sleepGraphRecordError: null,

  isLoadingSleepHighLights: false,
  sleepHighlights: [],
  sleepHightlightsError: null,
};

const useSleepStore = create((set, get) => ({
  ...initialState,

  setLoadingSleepStore: (loading) =>
    set((state) => ({ ...state, isLoadingSleepData: loading })),

  getLastLoggedSleep: async () => {
    set((state) => ({ ...state, isLoadingLastSleepLogged: true }));

    const res = await recordSleepService.getLastLoggedData();

    if (res.success)
      set((state) => ({
        ...state,
        lastLoggedSleep: res.data,
        isLoadingLastSleepLogged: false,
      }));
    else
      set((state) => ({
        ...state,
        sleepError: res.error,
        isLoadingLastSleepLogged: false,
      }));
  },

  setSleepRecordTimeFilter: (filter) =>
    set((state) => ({ ...state, sleepRecordTimeFilter: filter })),

  getSleepGraphRecords: async () => {
    const filter = get().sleepRecordTimeFilter;

    set((state) => ({ ...state, isLoadingSleepGraphRecords: true }));

    const res = await recordSleepService.getSleepGraphRecords(filter);

    if (res.success) {
      set((state) => ({
        ...state,
        sleepGraphRecords: res.data.graphData,
        sleepGraphRecordTime: res.data.timePeriod,
        isLoadingSleepGraphRecords: false,
      }));
      return true;
    } else {
      set((state) => ({
        ...state,
        sleepGraphRecordError: res.error,
        isLoadingSleepGraphRecords: false,
      }));
      return false;
    }
  },

  getSleepHighLights: async () => {
    set((state) => ({ ...state, isLoadingSleepHighLights: true }));

    const res = await highlightsService.getHighlights("sleep");

    if (res.success) {
      set((state) => ({
        ...state,
        isLoadingSleepHighLights: false,
        sleepHighlights: res.data,
      }));
    } else {
      set((state) => ({
        ...state,
        isLoadingSleepHighLights: false,
        sleepHightlightsError: res.error,
      }));
    }
  },

  clearSleepStoreErrors: () => {
    set((state) => ({
      sleepError: null,
      sleepGraphRecordError: null,
      sleepHightlightsError: null,
    }));
  },

  resetSleepStore: () => set({ ...initialState }),
}));

export default useSleepStore;
