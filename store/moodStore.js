import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { moodService } from "services/moodService";
import { highlightsService } from "services/highlightService";

const initialState = {
  isLoadingMoodData: false,

  isSavingMoodData: false,
  savingMoodError: null,

  isLoadingLastLoggedMood: false,
  lastLoggedMood: null,
  lastLoggedMoodError: null,

  isLoadingRecommendedMoodVideo: false,
  recommendedMoodVideo: null,
  recommendedMoodVideoError: null,

  isLoadingMoodGraphData: false,
  moodGraphFilter: "weekly",
  moodGraphData: [],
  moodGraphTimeRange: null,
  moodGraphError: null,

  isLoadingMoodHighlights: false,
  moodHighlights: [],
  moodHighlightsError: null,
};

const useMoodStore = create(
  persist(
    (set, get) => ({
      ...initialState,

      setLoadingMoodStore: (loading) =>
        set((state) => ({ ...state, isLoadingMoodData: loading })),

      saveMoodData: async ({ mood, hungerLevel }) => {
        set((state) => ({ ...state, isSavingMoodData: true }));

        const isWithinLastHour = () => {
          if (!get()?.lastLoggedMood || !get()?.lastLoggedMood.updatedAt)
            return false;

          const createdAtTime = new Date(
            get()?.lastLoggedMood.updatedAt
          ).getTime();

          const now = Date.now();

          return now - createdAtTime <= 60 * 60 * 1000;
        };

        let successfullyResolved = false;

        if (isWithinLastHour()) {
          const res = await moodService.editMood({
            mood: mood.toLowerCase(),
            hungerLevel: hungerLevel.toLowerCase(),
            id: get().lastLoggedMood.id,
          });

          successfullyResolved = res.success;

          if (!successfullyResolved) {
            set((state) => ({ ...state, savingMoodError: res?.error }));
            return false;
          }
        } else {
          const res = await moodService.createMood({
            mood: mood.toLowerCase(),
            hungerLevel: hungerLevel.toLowerCase(),
          });

          successfullyResolved = res.success;

          if (!successfullyResolved) {
            set((state) => ({ ...state, savingMoodError: res?.error }));
            return false;
          }
        }

        set((state) => ({ ...state, isSavingMoodData: false }));
        return true;
      },

      getMoodLastLogged: async () => {
        set((state) => ({ ...state, isLoadingLastLoggedMood: true }));

        const res = await moodService.getLastMood();

        if (res.success) {
          set((state) => ({
            ...state,
            isLoadingLastLoggedMood: false,
            lastLoggedMood: res.data,
          }));
        } else
          set((state) => ({
            ...state,
            isLoadingLastLoggedMood: false,
            lastLoggedMoodError: res.error,
          }));
      },

      getMoodRecommendedVideo: async () => {
        set((state) => ({ ...state, isLoadingRecommendedMoodVideo: true }));

        const res = await moodService.getRecommendedVideo();

        if (res.success)
          set((state) => ({
            ...state,
            isLoadingRecommendedMoodVideo: false,
            recommendedMoodVideo: res.data,
          }));
        else
          set((state) => ({
            ...state,
            isLoadingRecommendedMoodVideo: false,
            recommendedMoodVideoError: res.error,
          }));
      },

      setMoodGraphFilter: (filter) =>
        set((state) => ({ ...state, moodGraphFilter: filter })),

      getMoodGraphData: async () => {
        const filter = get().moodGraphFilter;

        set((state) => ({ ...state, isLoadingMoodGraphData: true }));

        const res = await moodService.getMoodGraphData({ filter });

        if (res.success) {
          set((state) => ({
            ...state,
            isLoadingMoodGraphData: false,
            moodGraphData: res.data.moodData,
            moodGraphTimeRange: res.data.timeRange,
          }));
          return true;
        } else {
          set((state) => ({
            ...state,
            isLoadingMoodGraphData: false,
            moodGraphError: res.error,
          }));
          return false;
        }
      },

      getMoodHighlights: async () => {
        set((state) => ({ ...state, isLoadingMoodHighlights: true }));

        const res = await highlightsService.getHighlights("mood");

        if (res.success) {
          set((state) => ({
            ...state,
            isLoadingMoodHighlights: false,
            moodHighlights: res.data,
          }));
        } else
          set((state) => ({
            ...state,
            isLoadingMoodHighlights: false,
            moodHighlightsError: res.error,
          }));
      },

      clearMoodStoreErrors: () => {
        set((state) => ({
          savingMoodError: null,
          lastLoggedMoodError: null,
          recommendedMoodVideoError: null,
          moodGraphError: null,
          moodHighlightsError: null,
        }));
      },

      resetMoodStore: () => set({ ...initialState }),
    }),
    {
      name: "mood-store",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

export default useMoodStore;
