import { create } from 'zustand';

const useIngredientsStore = create((set) => ({
    ingredients: [],
    selectedMealQuantity: "small",
    setSelectedMealQuantity: (quantity) => set((state) => ({ selectedMealQuantity: quantity })),
    addIngredient: (data) => set((state) => {
        return { ingredients: [...state.ingredients, data] }
    }),
    editIngredient: ({ data, index }) => set((state) => ({
        ingredients: state.ingredients.map((meal, i) => i === index ? data : meal)
    })),
    deleteIngredient: (id) => set((state) => ({ ingredients: state.ingredients.filter((meal, index) => index != id) })),
    resetIngredients: () => set((state) => ({ ingredients: [], previngredientsData: [] })),
}));

export default useIngredientsStore;