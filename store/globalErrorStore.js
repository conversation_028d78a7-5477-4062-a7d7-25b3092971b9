import { create } from 'zustand'

const useGlobalErrorStore = create ((set)=>({
    healthDataError:null,

    setHealthDataError: (error) => {
        set((state)=>({
            ...state,
            healthDataError:error,
        }))
    }
}))

export default  useGlobalErrorStore

// import { create } from 'zustand';

// const useGlobalErrorStore = create((set) => ({
//   healthDataError: {
//     error: null,
//     lastShowTime: null,
//   },

//   setHealthDataError: (error) => {
//     set((state) => ({
//         healthDataError: {
//           ...state,
//           healthDataError : {
//               error: error,
//               lastShowTime: new Date(),
//           }
//         },
//       }))
//   },
//   clearHealthDataError: () => {
//     set((state) => ({
//       healthDataError: {
//         ...state,
//         healthDataError : {
//           error: null,
//           ...state.healthDataError,
//         }
//       },
//     }))
//   },
// }));

// export default useGlobalErrorStore;
