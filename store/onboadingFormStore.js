
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

const initialState = {
    isLoadingOnboarding: false,
    currentOnboardingStep: 1,
    profile: {
        age: "",
        dob: "",
        sex: "",
        height: "",
        weight: "",
        dietPreference: "",
    },
    goals: {
        physical: "",
        movement: "",
        mindfulness: "",
        sleep: "",
        deviceUsageLimit: "",
    },
    deviceDetails: {
        id: "",
        referenceId: "",
        type: "",
        name: "",
        version: "",
        image: ""
    },
    app_permissions: [],
    onboardingError: null,
};

const useOnboardingFormStore = create(
    persist(
        (set) => ({
            ...initialState,

            setLoadingOnboarding: (isLoadingOnboarding) => set((state) => ({
                ...state,
                isLoadingOnboarding,
            })),

            setOnboardingStep: (step) => set((state) => ({
                ...state,
                currentOnboardingStep: step,
            })),
            
            setProfileOnboardingData : (data) => set((state) => ({
                ...state,
                profile: {
                    ...state.profile,
                    ...data,
                },
            })),

            setGoalsOnboardingData : (data) => set((state) => ({
                ...state,
                goals: {
                    ...state.goals,
                    ...data,
                },
            })),

            setAppOnboardingPermissions : (data) => set((state) => ({
                ...state,
                app_permissions: data,
            })),

            setDeviceOnboardingDetails : (data) => set((state) => ({
                ...state,
                deviceDetails: {
                    ...state.deviceDetails,
                    ...data,
                },
            })),

            setOnboardingErrors: (step, errors) => set((state) => ({
                ...state,
                errors: {
                    ...state.errors,
                    [step]: errors,
                },
            })),

            resetOnboardingForm: () => set(initialState),
        }),
        {
            name: 'user-onboarding-store',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

export default useOnboardingFormStore;