
import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { persist, createJSONStorage } from "zustand/middleware";

const initialDeviceManagementState = {
    devices: [],
};

const useDeviceManagementStore = create(
    persist(
        (set, get) => ({
            ...initialDeviceManagementState,
            addDevice: async (device) => {
                set((state) => {
                    const deviceExists = state.devices.some(d =>
                        d.id === device.id && d.serialId === device.serialId
                    );

                    if (!deviceExists) {
                        return { devices: [...state.devices, device] };
                    }

                    return state;
                })
            },
            removeDevice: async (id) => {
                set((state) => ({ devices: state.devices.filter((d) => d.id !== id) }))
            },
        }),
        {
            name: "connected-devices-store",
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

export default useDeviceManagementStore;
