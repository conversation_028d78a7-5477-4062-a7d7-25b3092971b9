import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const useAppleHealthPermissionStore = create(
    persist(
        (set, get) => ({
            isLoadingHealthPermissions: false,
            hasAppleHealthPermission: false,
            hasAppleAllHealthPermissions: false,
            applePermissionRequestCalled: 0,
            showPermissionAlert: false,
            healthPermissionError: null,

            setAppleHealthPermission: async () => {
                set((state) => ({ ...state, isLoadingHealthPermissions: true }));
                const currentState = get();

                if (currentState.hasAppleHealthPermission) {
                    set((state) => ({
                        ...state,
                        hasAppleHealthPermission: false,
                        hasAppleAllHealthPermissions: false,
                        isLoadingHealthPermissions: false,
                        showPermissionAlert: false,
                        healthPermissionError: null,
                    }));
                    return;
                }

                try {
                    const { hasApplePermissions, requestPermission } = await import('../hooks/useHealthDataIOS');
                    const permission = await requestPermission();

                    set((state) => ({
                        ...state,
                        isLoadingHealthPermissions: false,
                        hasAppleHealthPermission: permission.granted,
                        hasAppleAllHealthPermissions: permission.granted,
                        applePermissionRequestCalled: permission.granted ? 0 : state.applePermissionRequestCalled + 1,
                        showPermissionAlert: state.applePermissionRequestCalled >= 2,
                        healthPermissionError: (!permission.granted && state.applePermissionRequestCalled < 2)
                            ? "Something went wrong with Apple Health permissions. Please try again."
                            : null,
                    }));
                } catch (error) {
                    set((state) => ({
                        ...state,
                        isLoadingHealthPermissions: false,
                        healthPermissionError: "Failed to request Apple Health permissions",
                    }));
                }
            },

            resetAppleHealthPermissionStore: () => {
                set((state) => ({
                    ...state,
                    isLoadingHealthPermissions: false,
                    hasAppleHealthPermission: false,
                    hasAppleAllHealthPermissions: false,
                    applePermissionRequestCalled: 0,
                    showPermissionAlert: false,
                    healthPermissionError: null,
                }));
            }
        }),
        {
            name: 'apple-health-permission-store',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

export default useAppleHealthPermissionStore;