import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

let getGrantedPermissionsList = null;
let hasGoogleHealthInstalled = null;
let removeGoogleHealthPermission = null;
let requestGoogleHealthPermissions = null;

// Only load Android dependencies if we're on Android
if (Platform.OS === 'android') {
    const GoogleHealth = require('../utils/GoogleHealth');
    getGrantedPermissionsList = GoogleHealth.getGrantedPermissionsList;
    hasGoogleHealthInstalled = GoogleHealth.hasGoogleHealthInstalled;
    removeGoogleHealthPermission = GoogleHealth.removeGoogleHealthPermission;
    requestGoogleHealthPermissions = GoogleHealth.requestGoogleHealthPermissions;
}

const useGoogleHealthPermissionStore = create(
    persist(
        (set, get) => ({
            isLoadingHealthPermissions: false,
            isGoogleHealthAppInstalled: false,
            showHealthAppInstallationPopUp: false,
            hasGoogleHealthPermission: false,
            hasGoogleAllHealthPermissions: false,
            googlePermissionRequestCalled: 0,
            showPermissionAlert: false,
            hasBackgroundPermission: false,
            healthPermissionError: null,

            setGoogleHealthPermission: async () => {
                set((state) => ({ ...state, isLoadingHealthPermissions: true }));
                const currentState = get();

                try {
                    const isInitialized = await hasGoogleHealthInstalled();

                    if (!isInitialized) {
                        set((state) => ({
                            ...state,
                            isLoadingHealthPermissions: false,
                            isGoogleHealthAppInstalled: false,
                            showHealthAppInstallationPopUp: true,
                            hasGoogleHealthPermission: false,
                            hasGoogleAllHealthPermissions: false,
                            showPermissionAlert: false,
                            googlePermissionRequestCalled: 0,
                            healthPermissionError: null,
                        }));
                        return;
                    }

                    const healthDataBackgroundPermissionGranted = await requestGoogleHealthPermissions();
                    const { permissionsGranted, areAllPermissionsGranted } = await getGrantedPermissionsList();

                    set((state) => ({
                        ...state,
                        isLoadingHealthPermissions: false,
                        isGoogleHealthAppInstalled: true,
                        showHealthAppInstallationPopUp: false,
                        hasGoogleHealthPermission: permissionsGranted.length > 0,
                        hasGoogleAllHealthPermissions: areAllPermissionsGranted,
                        googlePermissionRequestCalled: permissionsGranted.length > 0 ? 0 :
                            (state.googlePermissionRequestCalled > 2 ? 3 : state.googlePermissionRequestCalled + 1),
                        showPermissionAlert: (state.googlePermissionRequestCalled >= 2 || !healthDataBackgroundPermissionGranted),
                        hasBackgroundPermission: healthDataBackgroundPermissionGranted,
                        healthPermissionError: (state.googlePermissionRequestCalled < 2 && permissionsGranted.length === 0)
                            ? "Something went wrong with Google Health permissions. Please try again."
                            : null,
                    }));
                } catch (error) {
                    set((state) => ({
                        ...state,
                        isLoadingHealthPermissions: false,
                        healthPermissionError: "Failed to request Google Health permissions",
                    }));
                }
            },

            setShowHealthAppInstallationAlert: () => {
                set((state) => ({ ...state, showHealthAppInstallationPopUp: false }));
            },

            resetGoogleHealthPermissionStore: () => {
                set((state) => ({
                    ...state,
                    isLoadingHealthPermissions: false,
                    isGoogleHealthAppInstalled: false,
                    showHealthAppInstallationPopUp: false,
                    hasGoogleHealthPermission: false,
                    hasGoogleAllHealthPermissions: false,
                    googlePermissionRequestCalled: 0,
                    showPermissionAlert: false,
                    hasBackgroundPermission: false,
                    healthPermissionError: null,
                }));
            }
        }),
        {
            name: 'google-health-permission-store',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

export default useGoogleHealthPermissionStore;