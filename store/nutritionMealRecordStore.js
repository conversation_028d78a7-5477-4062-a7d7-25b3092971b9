import { create } from "zustand";
import { persist, createJSONStorage, devtools } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { mealRecordsService } from "services/mealRecordsService";
import { highlightsService } from "services/highlightService";

export const DEFAULT_MEAL_TYPES = [
  "Breakfast",
  "Mid-Morning Snack",
  "Lunch",
  "Afternoon Snack",
  "Dinner",
  "Late Night Snack",
];

export const AVAILABLE_MEAL_TYPES = [
  "Breakfast",
  "Mid-Morning Snack",
  "Lunch",
  "Afternoon Snack",
  "Dinner",
  "Late Night Snack",
];

const initialState = {
  isLoadingMealStore: false,

  isLoadingMealRecords: false,
  mealRecords: [],
  lastLoggedMealRecord: null,
  todayTotalCalories: 0,
  mealRecordsError: null,
  extraAddedCards: {
    createdAt: null,
    records: [],
  },
  historyRecords: AVAILABLE_MEAL_TYPES.map((mealType) => ({
    mealName: mealType,
    meals: [],
  })),

  isLoadingMealGraphRecords: false,
  mealGraphRecordsFilter: "weekly",
  mealGraphRecords: [],
  mealGraphRecordTimePeriod: null,
  mealGraphRecordError: null,

  isLoadingNutritionHighlights: false,
  nutritionHighlights: [],
  nutritionHighlightsError: null,
};

const log = (config) => (set, get, api) =>
  config(
    (...args) => {
      //   console.log("Previous meal record state:", get())
      set(...args);
      //   console.log("Current meal record state:", get())
    },
    get,
    api
  );

const useNutritionMealRecordStore = create(
  log(
    persist(
      (set, get) => ({
        ...initialState,
        setIsLoadingMealStore: (loading) =>
          set((state) => ({ isLoadingMealStore: loading })),
        setIsLoadingMealRecords: (loading) =>
          set((state) => ({ isLoadingMealRecords: loading })),
        getMealRecords: async () => {
          set((state) => ({ isLoadingMealRecords: true }));

          const mealRecordsRes = await mealRecordsService.getMealRecords();

          if (mealRecordsRes.success) {
            const mealRecords = [...mealRecordsRes.data];

            DEFAULT_MEAL_TYPES.forEach((mealType) => {
              if (!mealRecords.some((record) => record.mealName === mealType)) {
                mealRecords.push({
                  mealName: mealType,
                  meals: [],
                });
              }
            });

            // const extraAddedCards = get().extraAddedCards;

            // const remainingCards = (extraAddedCards?.createdAt && extraAddedCards.createdAt===new Date().toISOString().split('T')[0])? extraAddedCards.records.filter(extraCard => {
            //     return !mealRecords.some(record => record.mealName === extraCard.mealName);
            // }):[];

            let latestMealLog = null;
            mealRecords.forEach((record) => {
              if (!record.createdAt) return;

              const recordTime = new Date(record.createdAt);
              if (
                !latestMealLog ||
                recordTime > new Date(latestMealLog.createdAt)
              ) {
                latestMealLog = record;
              }
            });

            set((state) => ({
              isLoadingMealRecords: false,
              mealRecords: [
                ...mealRecords,
                // ...remainingCards,
              ],
              lastLoggedMealRecord: latestMealLog,
              todayTotalCalories: mealRecords.reduce((total, record) => {
                return total + record.meals.reduce((sum, meal) => sum + meal.calories, 0);
              }, 0),
            }));
            return;
          } else
            set((state) => ({
              isLoadingMealRecords: false,
              mealRecords: [],
              mealRecordsError: mealRecordsRes.error,
            }));
        },
        getHistoryMealRecords: async (date) => {
          set((state) => ({ isLoadingMealRecords: true }));

          const mealRecordsRes = await mealRecordsService.getMealRecords(date);

          if (mealRecordsRes.success) {
            const mealRecords = [...mealRecordsRes.data];

            AVAILABLE_MEAL_TYPES.forEach((mealType) => {
              if (!mealRecords.some((record) => record.mealName === mealType)) {
                mealRecords.push({
                  mealName: mealType,
                  meals: [],
                });
              }
            });

            set((state) => ({
              isLoadingMealRecords: false,
              historyRecords: mealRecords,
            }));
            return;
          } else
            set((state) => ({
              isLoadingMealRecords: false,
              historyRecords: AVAILABLE_MEAL_TYPES.map((mealType) => ({
                mealName: mealType,
                meals: [],
              })),
              mealRecordsError: mealRecordsRes.error,
            }));
        },
        addMealRecord: (mealName) => {
          const previousAddedCards = get().extraAddedCards;
          const date = previousAddedCards?.createdAt;

          if (!date || date !== new Date().toISOString().split("T")[0]) {
            set((state) => ({
              extraAddedCards: {
                createdAt: new Date().toISOString().split("T")[0],
                records: [
                  {
                    mealName: mealName,
                    meals: [],
                  },
                ],
              },
            }));
            return;
          } else {
            set((state) => ({
              extraAddedCards: {
                ...state.extraAddedCards,
                records: [
                  ...state.extraAddedCards.records,
                  {
                    mealName: mealName,
                    meals: [],
                  },
                ],
              },
            }));
            return;
          }
        },
        setMealGraphFilter: (filter) => set((state) => ({ mealGraphRecordsFilter: filter })),
        getMealGraphRecords: async () => {
          set((state) => ({ isLoadingMealGraphRecords: true }));

          const res = await mealRecordsService.getMealGraphRecords(
            get().mealGraphRecordsFilter
          );

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingMealGraphRecords: false,
              mealGraphRecords: res.data.graphData,
              mealGraphRecordTimePeriod: res.data.timePeriod,
            }));
            return true;
          } else {
            set((state) => ({
              ...state,
              isLoadingMealGraphRecords: false,
              mealGraphRecordError: res.error,
            }));
            return false;
          }
        },
        getNutritionHighlights: async () => {
          set((state) => ({ isLoadingNutritionHighlights: true }));

          const res = await highlightsService.getHighlights("nutrition");

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingNutritionHighlights: false,
              nutritionHighlights: res.data,
            }));
          } else {
            set((state) => ({
              ...state,
              isLoadingNutritionHighlights: false,
              nutritionHighlightsError: res.error,
            }));
          }
        },
        resetMealRecords: () => {
          set((state) => ({
            ...initialState,
            extraAddedCards: {
              ...state.extraAddedCards,
            },
          }))
        },
        clearMealRecords: () => set((state) => ({ ...initialState })),
        clearMealRecordsError: () => set((state) => ({ ...state, mealRecordsError: null, mealGraphRecordError: null, mealGraphRecordsError: null, nutritionHighlightsError: null })),
      }),
      {
        name: "nutrition-meal-records",
        storage: createJSONStorage(() => AsyncStorage),
      }
    )
  )
);

export default useNutritionMealRecordStore;
