import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Conditionally import Android-specific functions
let getGrantedPermissionsList = null;
let hasGoogleHealthInstalled = null;
let removeGoogleHealthPermission = null;
let requestGoogleHealthPermissions = null;

// Only import Android health utilities on Android platform
if (Platform.OS === 'android') {
    const GoogleHealth = require('utils/GoogleHealth');
    getGrantedPermissionsList = GoogleHealth.getGrantedPermissionsList;
    hasGoogleHealthInstalled = GoogleHealth.hasGoogleHealthInstalled;
    removeGoogleHealthPermission = GoogleHealth.removeGoogleHealthPermission;
    requestGoogleHealthPermissions = GoogleHealth.requestGoogleHealthPermissions;
}

// For iOS, we'll use the useHealthDataIOS hook directly in the components

import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const useHealthPermissionStore = create(
    persist(
        (set, get) => ({
            isLoadingHealthPermissions: false,
            isHealthAppInstalled: false,
            showHealthAppInstallationPopUp: false,
            hasHealthPermission: false,
            hasAllHealthPermissions: false,
            permissionRequestCalled: 0,
            showPermissionAlert: false,
            hasHealthPermissionLastStatus: false,
            healthPermissionError: null,
            hasBackgroundPermission: false,

            setHealthPermission: async () => {
                set((state) => ({ ...state, isLoadingHealthPermissions: true }));

                const currentState = get();

                // Platform-specific logic
                if (Platform.OS === 'android') {
                    // ANDROID IMPLEMENTATION - Keep existing code

                    // if the user has previously given health connect permission
                    if (currentState.hasHealthPermission) {
                        // await removeGoogleHealthPermission();
                        set((state) => ({
                            ...state,
                            hasHealthPermission: false,
                            hasAllHealthPermissions: false,
                            isLoadingHealthPermissions: false,
                            hasHealthPermissionLastStatus: true,
                            showPermissionAlert: false,
                            healthPermissionError: null,
                        }));
                        return;
                    }

                    // if health connect is not installed - for android 13 and below.
                    const isInitialized = await hasGoogleHealthInstalled();

                    if (!isInitialized) {
                        set((state) => ({
                            ...state,
                            isLoadingHealthPermissions: false,
                            isHealthAppInstalled: false,
                            showHealthAppInstallationPopUp: true,
                            hasHealthPermission: false,
                            hasAllHealthPermissions: false,
                            showPermissionAlert: false,
                            permissionRequestCalled: 0,
                            healthPermissionError: null,
                        }));
                        return;
                    }

                    const healthDataBackgroundPermissionGranted = await requestGoogleHealthPermissions();

                    const { permissionsGranted, areAllPermissionsGranted } = await getGrantedPermissionsList();

                    if (currentState.hasHealthPermissionLastStatus && !currentState.hasAllHealthPermission && !areAllPermissionsGranted) {
                        set((state) => ({
                            ...state,
                            isLoadingHealthPermissions: false,
                            isHealthAppInstalled: true,
                            showHealthAppInstallationPopUp: false,
                            hasHealthPermission: permissionsGranted.length != 0,
                            hasAllHealthPermissions: areAllPermissionsGranted,
                            permissionRequestCalled: state.permissionRequestCalled > 2 ? 3 : state.permissionRequestCalled + permissionsGranted.length == 0,
                            showPermissionAlert: true,
                            healthPermissionError: null,
                            hasBackgroundPermission: healthDataBackgroundPermissionGranted,
                        }));
                        return;
                    }

                    set((state) => ({
                        ...state,
                        isLoadingHealthPermissions: false,
                        isHealthAppInstalled: true,
                        showHealthAppInstallationPopUp: false,
                        hasHealthPermission: permissionsGranted.length != 0,
                        hasAllHealthPermissions: areAllPermissionsGranted,
                        permissionRequestCalled: permissionsGranted.length != 0 ? 0 : (state.permissionRequestCalled > 2 ? 3 : state.permissionRequestCalled + 1),
                        showPermissionAlert: (state.permissionRequestCalled >= 2 || !healthDataBackgroundPermissionGranted),
                        healthPermissionError: (state.permissionRequestCalled < 2 && permissionsGranted.length == 0) ? "Something went wrong.Please try again." : null,
                        hasBackgroundPermission: healthDataBackgroundPermissionGranted,
                    }));
                } else {
                    // iOS IMPLEMENTATION
                    // For iOS, we'll just toggle the permission state in the store
                    // The actual permission handling is done in the IntegrationScreen component
                    // using the useHealthDataIOS hook

                    // Simply toggle the current state
                    set((state) => ({
                        ...state,
                        hasHealthPermission: !currentState.hasHealthPermission,
                        isLoadingHealthPermissions: false,
                    }));
                }
            },
            setShowPermissionAlert: () => {
                set((state) => ({ ...state, showPermissionAlert: false }));
            },
            setShowHealthAppInstallationAlert: () => {
                set((state) => ({ ...state, showHealthAppInstallationPopUp: false }));
            },
            resetHealthPermissionStore: () => {
                set((state) => ({
                    ...state,
                    isLoadingHealthPermissions: false,
                    isHealthAppInstalled: false,
                    showHealthAppInstallationPopUp: false,
                    hasHealthPermission: false,
                    hasAllHealthPermissions: false,
                    showPermissionAlert: false,
                    hasHealthPermissionLastStatus: false,
                    healthPermissionError: null,
                    hasBackgroundPermission: false,
                }));
            }
        }),
        {
            name: 'user-health-permission-store',
            storage: createJSONStorage(() => AsyncStorage),
            onRehydrateStorage: () => (state) => {
                if (state) {
                    state.isLoadingHealthPermissions = false;
                    state.hasHealthPermissionLastStatus = false;
                    state.showPermissionAlert = false;
                    state.showHealthAppInstallationPopUp = false;
                    state.healthPermissionError = null;
                }
            }
        }
    )
);

export default useHealthPermissionStore;