import { StyleSheet, Text, View } from 'react-native'
import React, { useCallback } from 'react'
import { Calendar } from 'react-native-calendars';
import { format, subDays } from 'date-fns';
import { Colors } from 'constants/theme/colors';
import CustomSelect from 'components/CustomAction/CustomSelect';
import { ThemeFonts } from 'constants/theme/fonts';
import { screenHeight } from 'constants/sizes';


const CustomCalendarHeader = ({ currentMonth, setCurrentMonth, currentOpenDropdown, setCurrentOpenDropdown, totalDays = 7 }) => {
    const monthNames = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ];

    const currentDate = new Date();
    const currMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const currDay = currentDate.getDate();

    const nthDaysAgo = new Date(currentDate);
    nthDaysAgo.setDate(currDay - totalDays);
    const nthDaysAgoMonth = nthDaysAgo.getMonth();
    const nthDaysAgoYear = nthDaysAgo.getFullYear();

    const showPreviousMonth = nthDaysAgoMonth !== currMonth || nthDaysAgoYear !== currentYear;
    const monthsToShow = showPreviousMonth ? 2 : 1;


    const monthsOptions = Array.from({ length: monthsToShow }, (_, i) => {
        const month = currMonth - i;
        const year = month < 0 ? currentYear - 1 : currentYear;
        const adjustedMonth = month < 0 ? month + 12 : month;

        return {
            label: `${monthNames[adjustedMonth]} ${year}`,
            value: `${year}-${(adjustedMonth + 1).toString().padStart(2, '0')}`
        };
    });

    const getEndDays = (value) => {
        const currentDate = new Date();
        const currMonth = currentDate.getMonth() + 1;
        const currDay = currentDate.getDate();

        if (value === currMonth.toString().padStart(2, '0')) {
            return currDay.toString().padStart(2, '0');
        }

        const nthDaysAgo = new Date(currentDate);
        nthDaysAgo.setDate(currDay - totalDays + 1)

        if (parseInt(value) === nthDaysAgo.getMonth() + 1) {
            return nthDaysAgo.getDate().toString().padStart(2, '0');
        }

        return '01';
    };

    return (
        <View style={styles.customHeader}>
            <CustomSelect
                label="Select Month"
                options={monthsOptions.reverse()}
                selectedValue={String(currentMonth).substring(0, 7)}
                onValueChange={(value) => setCurrentMonth(`${value}-${getEndDays(String(value).substring(5, 7))}`)}
                backgroundColor={Colors.lightGreen}
                textColor={Colors.black}
                triggerStyle={{ paddingVertical: 3 }}
                triggerZ={20}
                listZ={19}
                currentOpenDropdown={currentOpenDropdown}
                dropdownId={"calender_month_dropdown"}
                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                disabled={monthsOptions.length <= 1}
                showDropdownIcon={monthsOptions.length > 1}
                alignDropdown="flex-start"
                changeBG={true}
                disabledBgColor={Colors.lightGreen}
            />
            <View style={styles.customWeekDaysContainer}>
                <Text style={styles.weekDayName}>S</Text>
                <Text style={styles.weekDayName}>M</Text>
                <Text style={styles.weekDayName}>T</Text>
                <Text style={styles.weekDayName}>W</Text>
                <Text style={styles.weekDayName}>T</Text>
                <Text style={styles.weekDayName}>F</Text>
                <Text style={styles.weekDayName}>S</Text>
            </View>
        </View>
    );
};

const CustomCalender = ({ heading, totalDays, currentMonth, setCurrentMonth, currentOpenDropdown, setCurrentOpenDropdown }) => {

    const todayDate = format(new Date(), 'yyyy-MM-dd');
    const nDaysAgo = format(subDays(new Date(), totalDays - 1), 'yyyy-MM-dd');

    const onDayPress = useCallback((day) => {
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);

        const sevenDaysAgo = new Date(currentDate);
        sevenDaysAgo.setDate(currentDate.getDate() - totalDays);
        sevenDaysAgo.setHours(0, 0, 0, 0);

        const selectedDate = new Date(day.year, day.month - 1, day.day);
        selectedDate.setHours(0, 0, 0, 0);

        if (selectedDate > currentDate || selectedDate < sevenDaysAgo) {
            return;
        }

        setCurrentMonth(day.dateString);
    }, []);

    return (
        <View style={{ paddingHorizontal: 16 }}>
            <Text style={styles.heading}>{heading}</Text>
            <CustomCalendarHeader
                currentMonth={currentMonth}
                setCurrentMonth={(value) => setCurrentMonth(value)}
                currentOpenDropdown={currentOpenDropdown} setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                totalDays={totalDays}
            />
            <Calendar
                key={currentMonth}
                current={currentMonth}
                style={styles.calendar}
                onDayPress={onDayPress}
                minDate={nDaysAgo}
                maxDate={todayDate}
                markedDates={{
                    [currentMonth]: { selected: true, selectedColor: Colors.lightGreen },
                }}
                firstDay={0}
                hideExtraDays
                disableAllTouchEventsForDisabledDays
                customHeader={() => <></>}
                theme={{
                    calendarBackground: Colors.primaryGreen,
                    textSectionTitleColor: Colors.white,
                    textSectionTitleDisabledColor: Colors.white,
                    dayTextColor: Colors.white,
                    todayTextColor: Colors.white,
                    selectedDayTextColor: Colors.black,
                    selectedDayBackgroundColor: Colors.lightGreen,
                    textDisabledColor: Colors.gray,

                }}
            />
        </View>
    )
}

export default CustomCalender

const styles = StyleSheet.create({
    calendar: {
        backgroundColor: Colors.primaryGreen,
        paddingHorizontal: 16
    },
    heading: {
        fontSize: 35,
        color: Colors.white,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700,
        marginHorizontal: 24,
        // marginBottom: 10,
    },
    text: {
        textAlign: 'center',
        padding: 10,
        backgroundColor: 'lightgrey',
        fontSize: 16
    },
    customCalendar: {
        height: screenHeight * .3,
        borderBottomWidth: 1,
        borderBottomColor: 'lightgrey'
    },
    customHeader: {
        backgroundColor: Colors.primaryGreen,
        flexDirection: 'col',
        // marginHorizontal: -4,
        padding: 8
    },
    customWeekDaysContainer: {
        flexDirection: 'row',
        justifyContent: "space-between",
        paddingHorizontal: 16,
        marginTop: 4
    },
    weekDayName: {
        color: Colors.white,
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_500,
    }
})