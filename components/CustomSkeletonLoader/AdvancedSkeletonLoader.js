import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  Easing,
  interpolate,
  Extrapolation,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors } from 'constants/theme/colors';

const { width } = Dimensions.get('window');

/**
 * AdvancedSkeletonLoader - A customizable skeleton loader with shimmer effect
 * @param {Object} props - Component props
 * @param {number} props.width - Width of the skeleton item
 * @param {number} props.height - Height of the skeleton item
 * @param {number} props.borderRadius - Border radius of the skeleton item
 * @param {Object} props.style - Additional styles for the skeleton container
 * @param {boolean} props.isCircle - Whether the skeleton should be a circle
 * @param {boolean} props.isLoading - Whether the skeleton is in loading state
 * @param {React.ReactNode} props.children - Content to show when not loading
 * @returns {React.ReactNode}
 */
const SkeletonItem = ({
  width: itemWidth = '100%',
  height = 20,
  borderRadius = 4,
  style = {},
  isCircle = false,
  isLoading = true,
  children = null,
}) => {
  // Animation progress value
  const shimmerValue = useSharedValue(0);

  // Start the animation when component mounts
  useEffect(() => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: 1500, easing: Easing.bezier(0.25, 0.1, 0.25, 1) }),
      -1, // Infinite repeat
      false // Don't reverse
    );
  }, []);

  // Animated style for the shimmer effect
  const shimmerStyle = useAnimatedStyle(() => {
    const translateX = interpolate(
      shimmerValue.value,
      [0, 1],
      [-width, width],
      Extrapolation.CLAMP
    );

    return {
      transform: [{ translateX }],
    };
  });

  // If not in loading state, render children
  if (!isLoading) {
    return children;
  }

  // Calculate dimensions for circle if isCircle is true
  const dimensions = isCircle
    ? {
      width: height,
      height: height,
      borderRadius: height / 2,
    }
    : {
      width: itemWidth,
      height,
      borderRadius,
    };

  return (
    <View
      style={[
        styles.skeletonContainer,
        dimensions,
        style,
      ]}
    >
      <Animated.View style={[styles.shimmer, shimmerStyle]}>
        <LinearGradient
          colors={[
            'rgba(197, 251, 199, 0.01)', // Almost transparent lightGreen
            'rgba(197, 251, 199, 0.15)', // Semi-transparent lightGreen
            'rgba(197, 251, 199, 0.3)', // More visible lightGreen
            'rgba(197, 251, 199, 0.15)', // Semi-transparent lightGreen
            'rgba(197, 251, 199, 0.01)', // Almost transparent lightGreen
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}
        />
      </Animated.View>
    </View>
  );
};

/**
 * SkeletonCard - A skeleton loader for card-like UI elements
 */
export const SkeletonCard = ({
  width = '100%',
  height = 120,
  borderRadius = 25,
  style = {},
  isLoading = true,
  children = null,
}) => {
  return (
    <SkeletonItem
      width={width}
      height={height}
      borderRadius={borderRadius}
      style={style}
      isLoading={isLoading}
    >
      {children}
    </SkeletonItem>
  );
};

/**
 * SkeletonCircle - A circular skeleton loader
 */
export const SkeletonCircle = ({
  size = 40,
  style = {},
  isLoading = true,
  children = null,
}) => {
  return (
    <SkeletonItem
      height={size}
      borderRadius={size / 2}
      style={style}
      isCircle={true}
      isLoading={isLoading}
    >
      {children}
    </SkeletonItem>
  );
};

/**
 * SkeletonText - A skeleton loader for text elements
 */
export const SkeletonText = ({
  width = '80%',
  height = 15,
  style = {},
  isLoading = true,
  children = null,
}) => {
  return (
    <SkeletonItem
      width={width}
      height={height}
      style={style}
      isLoading={isLoading}
    >
      {children}
    </SkeletonItem>
  );
};

/**
 * SkeletonRow - A row of skeleton items with spacing
 */
export const SkeletonRow = ({
  items = 3,
  height = 20,
  gap = 8,
  style = {},
  isLoading = true,
  children = null,
}) => {
  if (!isLoading) {
    return children;
  }

  return (
    <View style={[styles.row, { gap }, style]}>
      {Array(items)
        .fill(0)
        .map((_, index) => (
          <SkeletonItem
            key={index}
            width={`${100 / items - 2}%`}
            height={height}
          />
        ))}
    </View>
  );
};

/**
 * SkeletonList - A list of skeleton items
 */
export const SkeletonList = ({
  items = 3,
  height = 80,
  gap = 16,
  style = {},
  isLoading = true,
  children = null,
}) => {
  if (!isLoading) {
    return children;
  }

  return (
    <View style={[styles.list, { gap }, style]}>
      {Array(items)
        .fill(0)
        .map((_, index) => (
          <SkeletonItem key={index} height={height} />
        ))}
    </View>
  );
};

/**
 * SkeletonProfile - A skeleton loader for profile-like UI elements
 */
export const SkeletonProfile = ({
  avatarSize = 60,
  gap = 16,
  style = {},
  isLoading = true,
  children = null,
}) => {
  if (!isLoading) {
    return children;
  }

  return (
    <View style={[styles.profile, { gap }, style]}>
      <SkeletonCircle size={avatarSize} />
      <View style={styles.profileInfo}>
        <SkeletonText width="70%" height={16} style={{ marginBottom: 8 }} />
        <SkeletonText width="90%" height={12} />
      </View>
    </View>
  );
};

/**
 * SkeletonCard with content - A skeleton loader for card with content
 */
export const SkeletonCardWithContent = ({
  height = 200,
  borderRadius = 25,
  style = {},
  isLoading = true,
  children = null,
}) => {
  if (!isLoading) {
    return children;
  }

  return (
    <View style={[styles.cardWithContent, style]}>
      <SkeletonCard height={height} borderRadius={borderRadius} />
      <View style={styles.cardContent}>
        <SkeletonText width="60%" height={18} style={{ marginBottom: 8 }} />
        <SkeletonText width="90%" height={14} style={{ marginBottom: 4 }} />
        <SkeletonText width="40%" height={14} />
      </View>
    </View>
  );
};

/**
 * SkeletonDashboard - A skeleton loader for dashboard-like UI elements
 */
export const SkeletonDashboard = ({
  style = {},
  isLoading = true,
  children = null,
}) => {
  if (!isLoading) {
    return children;
  }

  return (
    <View style={[styles.dashboard, style]}>
      <SkeletonCard height={180} style={{ marginBottom: 24 }} />
      <SkeletonRow items={3} height={80} style={{ marginBottom: 24 }} />
      <SkeletonText width="50%" height={20} style={{ marginBottom: 16 }} />
      <SkeletonList items={3} />
    </View>
  );
};

const styles = StyleSheet.create({
  skeletonContainer: {
    backgroundColor: Colors.lightGray,
    overflow: 'hidden',
    position: 'relative',
  },
  shimmer: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  gradient: {
    width: '200%',
    height: '100%',
  },
  row: {
    flexDirection: 'row',
    width: '100%',
  },
  list: {
    width: '100%',
  },
  profile: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  profileInfo: {
    flex: 1,
  },
  cardWithContent: {
    width: '100%',
    marginBottom: 16,
  },
  cardContent: {
    padding: 12,
  },
  dashboard: {
    width: '100%',
    padding: 16,
  },
});

export default SkeletonItem;
