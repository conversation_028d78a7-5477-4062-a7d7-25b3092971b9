import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import { Colors } from 'constants/theme/colors';

/**
 * SimpleSkeletonLoader - A simple skeleton loader with pulse animation
 * @param {Object} props - Component props
 * @param {number} props.width - Width of the skeleton item
 * @param {number} props.height - Height of the skeleton item
 * @param {number} props.borderRadius - Border radius of the skeleton item
 * @param {Object} props.style - Additional styles for the skeleton container
 * @param {boolean} props.isCircle - Whether the skeleton should be a circle
 * @param {boolean} props.isLoading - Whether the skeleton is in loading state
 * @param {React.ReactNode} props.children - Content to show when not loading
 * @returns {React.ReactNode}
 */
const SimpleSkeletonItem = ({
  width: itemWidth = '100%',
  height = 20,
  borderRadius = 4,
  style = {},
  isCircle = false,
  isLoading = true,
  children = null,
}) => {
  // Animation opacity value
  const opacity = useSharedValue(0.5);

  // Start the animation when component mounts
  useEffect(() => {
    opacity.value = withRepeat(
      withTiming(1, { duration: 1000, easing: Easing.bezier(0.25, 0.1, 0.25, 1) }),
      -1, // Infinite repeat
      true // Reverse
    );
  }, []);

  // Animated style for the pulse effect
  const pulseStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  // If not in loading state, render children
  if (!isLoading) {
    return children;
  }

  // Calculate dimensions for circle if isCircle is true
  const dimensions = isCircle
    ? {
      width: height,
      height: height,
      borderRadius: height / 2,
    }
    : {
      width: itemWidth,
      height,
      borderRadius,
    };

  return (
    <Animated.View
      style={[
        styles.skeletonContainer,
        dimensions,
        pulseStyle,
        style,
      ]}
    />
  );
};

/**
 * SkeletonCard - A skeleton loader for card-like UI elements
 */
export const SimpleSkeletonCard = ({
  width = '100%',
  height = 120,
  borderRadius = 25,
  style = {},
  isLoading = true,
  children = null,
}) => {
  return (
    <SimpleSkeletonItem
      width={width}
      height={height}
      borderRadius={borderRadius}
      style={style}
      isLoading={isLoading}
    >
      {children}
    </SimpleSkeletonItem>
  );
};

/**
 * SkeletonCircle - A circular skeleton loader
 */
export const SimpleSkeletonCircle = ({
  size = 40,
  style = {},
  isLoading = true,
  children = null,
}) => {
  return (
    <SimpleSkeletonItem
      height={size}
      borderRadius={size / 2}
      style={style}
      isCircle={true}
      isLoading={isLoading}
    >
      {children}
    </SimpleSkeletonItem>
  );
};

/**
 * SkeletonText - A skeleton loader for text elements
 */
export const SimpleSkeletonText = ({
  width = '80%',
  height = 15,
  style = {},
  isLoading = true,
  children = null,
}) => {
  return (
    <SimpleSkeletonItem
      width={width}
      height={height}
      style={style}
      isLoading={isLoading}
    >
      {children}
    </SimpleSkeletonItem>
  );
};

const styles = StyleSheet.create({
  skeletonContainer: {
    backgroundColor: Colors.lightGray,
    overflow: 'hidden',
  },
});

export default SimpleSkeletonItem;
