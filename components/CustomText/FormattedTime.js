import React, { memo, useEffect } from 'react';
import { StyleSheet, Text } from 'react-native';

const FormattedTime = ({
    timestamp,
    prefix = "Last logged at",
    defaultPrefix = "Today at",
    style
}) => {
    const formatTime = (date) => {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }).replace('am', 'AM').replace('pm', 'PM');
    };

    const formatDate = (date) => {
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    const isToday = (someDate) => {
        const today = new Date();
        return (
            someDate.getDate() === today.getDate() &&
            someDate.getMonth() === today.getMonth() &&
            someDate.getFullYear() === today.getFullYear()
        );
    };

    const renderTime = () => {
        const date = timestamp ? new Date(timestamp) : new Date();
        const timeStr = formatTime(date);

        if (timestamp) {
            return isToday(date)
                ? `${prefix} ${timeStr}`
                : `${formatDate(date)} at ${timeStr}`;
        } else {
            return `${defaultPrefix} ${timeStr}`;
        }
    };

    return <Text style={[style]}>{renderTime()}</Text>;
};

const styles = StyleSheet.create({});

export default memo(FormattedTime);