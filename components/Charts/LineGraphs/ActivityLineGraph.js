import {
  processColor,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { memo } from "react";
import { CustomLoader, CustomSelect } from "components/CustomAction";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { LineChart } from "react-native-charts-wrapper-microcosmworks";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";
import useActivityStore from "store/activityStore";

const TIME_FILTER_OPTIONS = [
  { label: "Week", value: "weekly" },
  { label: "Month", value: "monthly" },
  { label: "6 mon", value: "half_yearly" },
  { label: "Year", value: "yearly" },
];

const ActivityLineGraph = memo(({ currentOpenDropdown, setCurrentOpenDropdown }) => {

  const isLoadingActivityGraph = useActivityStore(state => state.isLoadingActivityGraph);
  const activityGraphData = useActivityStore(state => state.activityGraphData);
  const activityGraphTimeRange = useActivityStore(state => state.activityGraphTimeRange);
  const getActivityGraph = useActivityStore(state => state.getActivityGraph);
  const activityGraphFilter = useActivityStore(state => state.activityGraphFilter);
  const setActivityGraphFilter = useActivityStore(state => state.setActivityGraphFilter);

  const handleSelectFilter = async (value) => {
    if (value == activityGraphFilter) return;

    const prevFilter = activityGraphFilter;

    setActivityGraphFilter(value);

    const getActivityGraphDataSuccess = await getActivityGraph({ filter: value });

    if (!getActivityGraphDataSuccess) {
      setActivityGraphFilter(prevFilter);
    }
  };

  if (!activityGraphData || activityGraphData.length == 0) return (
    <View style={[styles.graphContainer, { gap: 16 }]}>
      <SkeletonItem height={28} width={"30%"} borderRadius={25} />
      <SkeletonItem height={32} width={"50%"} borderRadius={10} />

      <SkeletonItem height={180} borderRadius={25} colors={[
        'rgba(255, 159, 193, 0.01)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.55)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.01)',
      ]} />
    </View>
  );

  return (
    <TouchableWithoutFeedback
      onPress={() => setCurrentOpenDropdown(null)}
      style={{ flex: 1 }}
    >
      <View style={styles.graphContainer}>
        {isLoadingActivityGraph && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
              borderRadius: 25,
              backgroundColor: "rgba(0,0,0,0.5)",
            }}
            pointerEvents="box-only"
          >
            <CustomLoader small />
          </View>
        )}
        <View style={{ marginHorizontal: 4 }}>
          <CustomSelect
            options={TIME_FILTER_OPTIONS}
            selectedValue={activityGraphFilter}
            onValueChange={handleSelectFilter}
            currentOpenDropdown={currentOpenDropdown}
            dropdownId={1}
            setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
            triggerZ={10}
            listZ={9}
            width={140}
            triggerStyle={{
              paddingVertical: 3,
              borderColor: Colors.veryLightGreen,
            }}
            backgroundColor={Colors.lightPurple}
            textColor={Colors.white}
            activeOptionBgColor={Colors.white}
            optionsBgColor={Colors.veryLightGreen}
            optionsBorderWidth={0}
            triggerBorderWidth={1}
            optionsBorderRadius={20}
            alignDropdown="flex-start"
            changeBG={true}
          />
        </View>
        <View style={styles.graphHeader}>
          <Text style={styles.graphHeading}>Activity Graph</Text>
          <Text style={styles.graphTimeRange}>({activityGraphTimeRange || ""}
            )</Text>
        </View>
        <View style={{ backgroundColor: Colors.lightPurple, flexGrow: 1 }}>
          <LineChart
            style={{
              flexGrow: 1,
              height: 180,
            }}
            data={{
              dataSets: [
                {
                  values: activityGraphData.map((data) => {
                    return {
                      y: ((data?.value || 0) == 0) ? activityGraphData.map((item) => item.value).reduce((a, b) => Math.max(a, b)) * .01 : data?.value || 0,
                      marker: `${Number(data?.value || 0).toFixed(0)} kcal`,
                    };
                  }),
                  label: "Recorded average weight",
                  config: {
                    circleColor: processColor("transparent"),
                    circleHoleColor: processColor("transparent"),
                    valueTextColor: processColor("transparent"),
                    lineWidth: 2,
                    color: processColor(Colors.red),
                    mode: "HORIZONTAL_BEZIER",
                    highlightColor: processColor(Colors.red),
                  },
                },
              ],
            }}
            chartDescription={{ text: "" }}
            legend={{
              enabled: false,
            }}
            animation={{ durationX: 250, durationY: 0, easingX: "EaseInQuad" }}
            marker={{
              enabled: true,
              markerColor: processColor(Colors.primaryGreen),
              textSize: 12,
              textColor: processColor(Colors.white),
              textAlign: "center"
            }}
            extraOffsets={{
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
            }}
            xAxis={{
              valueFormatter: activityGraphData.map((item) => item.label),
              granularityEnabled: true,
              granularity: 1,
              drawAxisLine: false,
              drawGridLines: false,
              fontFamily: ThemeFonts.Exo_500,
              textColor: processColor(Colors.black),
              textSize: 12,
              position: "BOTTOM",
              labelRotationAngle: 0,
              labelCount: activityGraphData.length,
              avoidFirstLastClipping: true,
              // labelCountForce:true,
              granularity: 1,
              yOffset: 28,
            }}
            yAxis={{
              left: {
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "OUTSIDE_CHART",
                labelCount: 5,
                avoidFirstLastClipping: true,
                labelCountForce: true,
                // valueFormatter: "##0' kg'",
                axisMinimum: 0,
                axisMaximum: activityGraphData.map((item) => item.value).reduce((a, b) => Math.max(a, b)) + 500
              },
              right: {
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "OUTSIDE_CHART",
                labelCount: 5,
                avoidFirstLastClipping: true,
                labelCountForce: true,
                // valueFormatter: "##0' kg'",
                axisMinimum: 0,
                axisMaximum: activityGraphData.map((item) => item.value).reduce((a, b) => Math.max(a, b)) + 500
              },
            }}
            drawGridBackground={false}
            drawBorders={false}
            touchEnabled={true}
            scaleEnabled={false}
            onSelect={() => { }}
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
});

export default ActivityLineGraph;

const styles = StyleSheet.create({
  graphContainer: {
    backgroundColor: Colors.lightPurple,
    borderRadius: 30,
    padding: 16,
    paddingHorizontal: 24,
  },
  graphHeader: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "baseline",
    gap: 1,
    marginBottom: 10,
    marginTop: 8,
    marginHorizontal: 4,
    flexWrap: "wrap",
  },
  graphHeading: {
    fontSize: 29,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
  },
  graphTimeRange: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.white,
    fontStyle: "italic",
  },
  xAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  yAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  legendWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
    justifyContent: "space-between",
    // backgroundColor:'red',
    marginHorizontal: 12,
    marginTop: 16,
  },
  legendContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
  legendColor: {
    width: 10,
    height: 10,
    borderRadius: 25,
    backgroundColor: Colors.primaryPurple,
  },
  legendText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
});
