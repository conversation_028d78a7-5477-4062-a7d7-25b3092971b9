import {
  processColor,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { memo } from "react";
import { CustomLoader, CustomSelect } from "components/CustomAction";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { LineChart } from "react-native-charts-wrapper-microcosmworks";
import { TIME_FILTER_OPTIONS } from "constants/graphs/filters";
import useUserWeightStore from "store/userWeightStore";
import { useAuth } from "context/AuthContext";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";


const WeightLineGraph = memo(({ currentOpenDropdown, setCurrentOpenDropdown }) => {

  const { state } = useAuth();

  const isLoadingWeightGraphData = useUserWeightStore(state => state.isLoadingWeightGraphData);
  const weightGraphData = useUserWeightStore(state => state.weightGraphData);
  const weightGraphTimeRange = useUserWeightStore(state => state.weightGraphTimeRange);
  const weightGraphFilter = useUserWeightStore(state => state.weightGraphFilter);
  const setWeightGraphFilter = useUserWeightStore(state => state.setWeightGraphFilter);
  const getWeightGraphData = useUserWeightStore(state => state.getWeightGraphData);


  const handleSelectFilter = async (value) => {
    if (value == weightGraphFilter) return;
    const prevFilter = weightGraphFilter;
    setWeightGraphFilter(value);

    const getWeightGraphDataSuccess = await getWeightGraphData({ filter: value });

    if (!getWeightGraphDataSuccess) {
      setWeightGraphFilter(prevFilter);
    }
  };

  if (!weightGraphData || weightGraphData.length == 0) return (
    <View style={[styles.graphContainer, { gap: 16 }]}>
      <SkeletonItem height={28} width={"30%"} borderRadius={25} />
      <SkeletonItem height={32} width={"50%"} borderRadius={10} />

      <SkeletonItem height={180} borderRadius={25} colors={[
        'rgba(255, 159, 193, 0.01)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.55)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.01)',
      ]} />
    </View>
  );

  return (
    <TouchableWithoutFeedback
      onPress={() => setCurrentOpenDropdown(null)}
      style={{ flex: 1 }}
    >
      <View style={styles.graphContainer}>
        {isLoadingWeightGraphData && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
              borderRadius: 25,
              backgroundColor: "rgba(0,0,0,0.5)",
            }}
            pointerEvents="box-only"
          >
            <CustomLoader small />
          </View>
        )}
        <View style={{ marginHorizontal: 4 }}>
          <CustomSelect
            options={TIME_FILTER_OPTIONS}
            selectedValue={weightGraphFilter}
            onValueChange={(value) => handleSelectFilter(value)}
            currentOpenDropdown={currentOpenDropdown}
            dropdownId={"weight_line_graph"}
            setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
            triggerZ={10}
            listZ={9}
            width={140}
            triggerStyle={{
              paddingVertical: 3,
              borderColor: Colors.veryLightGreen,
            }}
            backgroundColor={Colors.lightPurple}
            textColor={Colors.white}
            activeOptionBgColor={Colors.white}
            optionsBgColor={Colors.veryLightGreen}
            optionsBorderWidth={0}
            triggerBorderWidth={1}
            optionsBorderRadius={20}
            alignDropdown="flex-start"
            changeBG={true}
          />
        </View>
        <View style={styles.graphheader}>
          <Text style={styles.graphHeading}>Weight Loss</Text>
          <Text style={styles.graphTimeRange}>{weightGraphTimeRange ? `(${weightGraphTimeRange})` : ""}</Text>
        </View>
        <View style={{ backgroundColor: Colors.lightPurple, flexGrow: 1 }}>
          <LineChart
            style={{
              flexGrow: 1,
              height: 180,
            }}
            data={{
              dataSets: [
                {
                  values: weightGraphData.map((data) => {
                    return {
                      y: data.value == 0 ? 1 : data.value,
                      marker: `${Number(data.value).toString().split('.').length > 1 ? Number(data.value).toFixed(1) : data.value}kg`,
                    };
                  }),
                  label: "Recorded average weight",
                  config: {
                    circleColor: processColor("transparent"),
                    circleHoleColor: processColor("transparent"),
                    valueTextColor: processColor("transparent"),
                    lineWidth: 2,
                    color: processColor(Colors.red),
                    mode: "HORIZONTAL_BEZIER",
                    highlightColor: processColor(Colors.red),
                  },
                },
                {
                  values: weightGraphData.map((data) => ({ y: state.user.weight - Number(state?.user?.goals.filter(goal => goal.goal_type == "physical")[0].selected_goal.substring(4)), marker: `${state.user.weight - Number(state?.user?.goals.filter(goal => goal.goal_type == "physical")[0].selected_goal.substring(4))}kg` })),
                  label: "Goal weight",
                  config: {
                    circleColor: processColor("transparent"),
                    circleHoleColor: processColor("transparent"),
                    valueTextColor: processColor("transparent"),
                    dashedLine: {
                      lineLength: 14,
                      spaceLength: 14,
                    },
                    lineWidth: 1.5,
                    color: processColor(Colors.lightGray),
                    axisDependency: "RIGHT",
                    highlightColor: processColor("transparent"),
                  },
                },
              ],
            }}
            chartDescription={{ text: "" }}
            legend={{
              enabled: false,
            }}
            animation={{ durationX: 250, durationY: 0, easingX: "EaseInQuad" }}
            marker={{
              enabled: true,
              markerColor: processColor(Colors.primaryGreen),
              textSize: 12,
              textColor: processColor(Colors.white),
              textAlign: "center"
            }}
            extraOffsets={{
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
            }}
            xAxis={{
              valueFormatter: weightGraphData.map((item) => item.label),
              granularityEnabled: true,
              granularity: 1,
              drawAxisLine: false,
              drawGridLines: false,
              fontFamily: ThemeFonts.Exo_500,
              textColor: processColor(Colors.black),
              textSize: 12,
              position: "BOTTOM",
              labelRotationAngle: 0,
              labelCount: weightGraphData.length,
              avoidFirstLastClipping: true,
              // labelCountForce:true,
              granularity: 1,
              yOffset: 28,
            }}
            yAxis={{
              left: {
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "OUTSIDE_CHART",
                labelCount: 5,
                avoidFirstLastClipping: true,
                labelCountForce: true,
                // valueFormatter: "##0' kg'",
                axisMinimum: 0,
                axisMaximum:
                  weightGraphData
                    .map((item) => item.value)
                    .reduce((a, b) => Math.max(a, b)) + 10,
              },
              right: {
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "OUTSIDE_CHART",
                labelCount: 5,
                avoidFirstLastClipping: true,
                labelCountForce: true,
                // valueFormatter: "##0' kg'",
                axisMinimum: 0,
                axisMaximum:
                  weightGraphData
                    .map((item) => item.value)
                    .reduce((a, b) => Math.max(a, b)) + 10,
              },
            }}
            drawGridBackground={false}
            drawBorders={false}
            touchEnabled={true}
            scaleEnabled={false}
            onSelect={() => { }}
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
});

export default WeightLineGraph;

const styles = StyleSheet.create({
  graphContainer: {
    backgroundColor: Colors.lightPurple,
    borderRadius: 30,
    padding: 16,
    paddingHorizontal: 24,
  },
  graphheader: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "baseline",
    gap: 1,
    marginBottom: 10,
    marginTop: 8,
    marginHorizontal: 4,
    flexWrap: "wrap",
  },
  graphHeading: {
    fontSize: 29,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
  },
  graphTimeRange: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.white,
    fontStyle: "italic",
  },
  xAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  yAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  legendWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
    justifyContent: "space-between",
    // backgroundColor:'red',
    marginHorizontal: 12,
    marginTop: 16,
  },
  legendContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
  legendColor: {
    width: 10,
    height: 10,
    borderRadius: 25,
    backgroundColor: Colors.primaryPurple,
  },
  legendText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
});
