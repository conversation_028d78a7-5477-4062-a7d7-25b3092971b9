import {
  processColor,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { memo } from "react";
import { CustomLoader, CustomSelect } from "components/CustomAction";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { Bar<PERSON><PERSON> } from "react-native-charts-wrapper-microcosmworks";
import { TIME_FILTER_OPTIONS } from "constants/graphs/filters";
import useNutritionMealRecordStore from "store/nutritionMealRecordStore";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const NutritionStackedBarGraph = memo(({ currentOpenDropdown, setCurrentOpenDropdown, containerStyle = {}, containerHeaderStyle = {}, timeFilterContainerStyle = {} }) => {
  const isLoadingMealGraphRecords = useNutritionMealRecordStore(state => state.isLoadingMealGraphRecords);
  const mealGraphRecordsFilter = useNutritionMealRecordStore(state => state.mealGraphRecordsFilter);
  const mealGraphRecords = useNutritionMealRecordStore(state => state.mealGraphRecords);
  const mealGraphRecordTimePeriod = useNutritionMealRecordStore(state => state.mealGraphRecordTimePeriod);
  const setMealGraphFilter = useNutritionMealRecordStore(state => state.setMealGraphFilter);
  const getMealGraphRecords = useNutritionMealRecordStore(state => state.getMealGraphRecords);

  const handleSelectFilter = async (value) => {
    if (value == mealGraphRecordsFilter) return;

    const prevFilter = mealGraphRecordsFilter;
    setMealGraphFilter(value);

    const mealGraphRecordsSuccess = await getMealGraphRecords(value);

    if (!mealGraphRecordsSuccess) {
      setMealGraphFilter(prevFilter);
    }
  };


  if (!mealGraphRecords || mealGraphRecords.length == 0) return (
    <View style={[styles.graphContainer, { gap: 16 }]}>
      <SkeletonItem height={28} width={"30%"} borderRadius={25} />
      <SkeletonItem height={32} width={"50%"} borderRadius={10} />

      <SkeletonItem height={180} borderRadius={25} colors={[
        'rgba(255, 159, 193, 0.01)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.55)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.01)',
      ]} />
    </View>
  );

  return (
    <TouchableWithoutFeedback
      onPress={() => setCurrentOpenDropdown(null)}
      style={{ flex: 1 }}
    >
      <View style={[styles.graphContainer, containerStyle]}>
        {isLoadingMealGraphRecords && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
              borderRadius: 25,
              backgroundColor: "rgba(0,0,0,0.5)",
            }}
            pointerEvents="box-only"
          >
            <CustomLoader small />
          </View>
        )}
        <View style={[{ marginHorizontal: 4 }, timeFilterContainerStyle]}>
          <CustomSelect
            options={TIME_FILTER_OPTIONS}
            selectedValue={mealGraphRecordsFilter}
            onValueChange={(value) => handleSelectFilter(value)}
            currentOpenDropdown={currentOpenDropdown}
            dropdownId={1}
            setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
            triggerZ={10}
            listZ={9}
            width={140}
            triggerStyle={{
              paddingVertical: 3,
              borderColor: Colors.veryLightGreen,
            }}
            backgroundColor={Colors.lightPurple}
            textColor={Colors.white}
            activeOptionBgColor={Colors.white}
            optionsBgColor={Colors.veryLightGreen}
            optionsBorderWidth={0}
            triggerBorderWidth={1}
            optionsBorderRadius={20}
            alignDropdown="flex-start"
            changeBG={true}
          />
        </View>
        <View style={[styles.graphheader, containerHeaderStyle]}>
          <Text style={styles.graphHeading}>Food Graph</Text>
          <Text style={styles.graphTimeRange}>{mealGraphRecordTimePeriod ? `(${mealGraphRecordTimePeriod})` : ""}</Text>
        </View>
        <View style={{
          backgroundColor: Colors.lightPurple, flexGrow: 1,
          height: 180,
        }}>
          <BarChart
            style={{
              flexGrow: 1,
            }}
            data={{
              dataSets: [
                {
                  values: mealGraphRecords.map((data) => {
                    return {
                      y: [
                        data?.value?.protein || 0,
                        data?.value?.carbs || 0,
                        data?.value?.fats || 0,
                        data?.value?.fiber || 0,
                      ],
                      marker: [`${data?.value?.protein || 0}g`, `${data?.value?.carbs || 0}g`, `${data?.value?.fats || 0}g`, `${data?.value?.fiber || 0}g`],
                    };
                  }),
                  label: "",
                  config: {
                    colors: [
                      processColor(Colors.white),
                      processColor(Colors.lightGreen),
                      processColor(Colors.darkGreen),
                      processColor(Colors.primaryPurple),
                    ],
                    stackLabels: ["Protein", "Carbs", "Fats", "Fiber"],
                    valueTextSize: 10,
                    valueTextColor: processColor("transparent"),
                    // valueFormatter: "##0' cal'",
                  },
                },
              ],
              config: {
                barWidth: 0.5,
              },
            }}
            extraOffsets={{
              top: 10,
              bottom: 10,
              left: 0,
              right: 0,
            }}
            xAxis={{
              valueFormatter: mealGraphRecords.map((item) => item.label),
              granularityEnabled: true,
              granularity: 1,
              drawAxisLine: false,
              drawGridLines: false,
              fontFamily: ThemeFonts.Exo_500,
              textColor: processColor(Colors.black),
              textSize: 12,
              position: "BOTTOM",
              labelRotationAngle: 0,
              labelCount: mealGraphRecords.length,
              avoidFirstLastClipping: false,
              // labelCountForce:true,
              granularity: 1,
              yOffset: 10,
            }}
            animation={{ durationX: 500, durationY: 500 }}
            legend={{
              enabled: false,
              textSize: 8,
              form: "CIRCLE",
              formSize: 10,
              xEntrySpace: 6,
              yEntrySpace: 5,
              wordWrapEnabled: false,
              fontFamily: ThemeFonts.Exo_500,
              verticalAlignment: "BOTTOM",
            }}
            chartDescription={{
              textColor: processColor("transparent"),
            }}
            marker={{
              enabled: true,
              markerColor: processColor(Colors.primaryGreen),
              textSize: 12,
              textColor: processColor(Colors.white),
              textAlign: "center"
            }}
            yAxis={{
              left: {
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "OUTSIDE_CHART",
                labelCount: 5,
                avoidFirstLastClipping: true,
                labelCountForce: true,
                // valueFormatterPattern: "#.0#",
                // valueFormatter: "##0' hr'",
                // valueFormatterPattern:"#.##hr",
                // granularity:1,
                // valueFormatter : (value) => {
                //     return `${value}hr`;
                // },
                axisMinimum: 0,
              },
              right: {
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "OUTSIDE_CHART",
                labelCount: 5,
                avoidFirstLastClipping: true,
                labelCountForce: true,
                // granularity:1,
                // valueFormatter : (value) => {
                //     return value;
                // }
                axisMinimum: 0,
              },
            }}
            gridBackgroundColor={processColor(Colors.primaryPurple)}
            visibleRange={{
              x: { min: mealGraphRecords.length, max: mealGraphRecords.length },
            }}
            drawBarShadow={false}
            drawValueAboveBar={true}
            drawHighlightArrow={true}
            onSelect={() => { }}
            highlights={[]}
            onChange={(event) => { }}
            scaleEnabled={false}
          />
          <View style={styles.legendWrapper}>
            <View style={styles.legendContainer}>
              <View style={[styles.legendColor, { backgroundColor: Colors.primaryPurple }]}></View>
              <Text style={styles.legendText} allowFontScaling={false}>Protein</Text>
            </View>
            <View style={styles.legendContainer}>
              <View style={[styles.legendColor, { backgroundColor: Colors.darkGreen }]}></View>
              <Text style={styles.legendText} allowFontScaling={false}>Carbs</Text>
            </View>
            <View style={styles.legendContainer}>
              <View style={[styles.legendColor, { backgroundColor: Colors.lightGreen }]}></View>
              <Text style={styles.legendText} allowFontScaling={false}>Fats</Text>
            </View>
            <View style={styles.legendContainer}>
              <View style={[styles.legendColor, { backgroundColor: Colors.white }]}></View>
              <Text style={styles.legendText} allowFontScaling={false}>Fiber</Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
});

export default NutritionStackedBarGraph;

const styles = StyleSheet.create({
  graphContainer: {
    backgroundColor: Colors.lightPurple,
    borderRadius: 30,
    padding: 16,
    paddingHorizontal: 24,
  },
  graphheader: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "baseline",
    gap: 1,
    marginBottom: 10,
    marginTop: 8,
    marginHorizontal: 4,
    flexWrap: "wrap",
  },
  graphHeading: {
    fontSize: 29,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
  },
  graphTimeRange: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.white,
    fontStyle: "italic",
  },
  xAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  yAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  legendWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
    justifyContent: "space-between",
    // backgroundColor:'red',
    marginHorizontal: 12,
  },
  legendContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
  legendColor: {
    width: 10,
    height: 10,
    borderRadius: 25,
    backgroundColor: Colors.primaryPurple,
  },
  legendText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
});
