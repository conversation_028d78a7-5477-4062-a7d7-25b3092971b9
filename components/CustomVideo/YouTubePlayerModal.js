import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Dimensions, ScrollView, BackHandler, Modal } from 'react-native';
import YoutubePlayer from 'react-native-youtube-iframe';
import Icon from 'react-native-vector-icons/Ionicons';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import { StatusBar } from 'expo-status-bar';

const { width, height } = Dimensions.get('window');
const videoHeight = Math.floor(height * 0.4);

const YouTubePlayerModal = ({
    videoId,
    title,
    description,
    onClose,
    topInset = 0,
    visible = true,
    videoURL = "youtube.com"
}) => {
    return (
        <Modal onRequestClose={onClose} transparent={true} visible={visible} style={{ zIndex: 1 }}>
            <StatusBar backgroundColor={Colors.darkGray} barStyle="light-content" />
            <View style={[styles.container, { marginTop: -topInset }]}>
                <View style={[styles.headerContainer, { paddingTop: topInset }]}>
                    <TouchableOpacity
                        style={[styles.closeButton, { marginTop: topInset }]}
                        onPress={onClose}
                    >
                        <Icon name="close" size={40} color={Colors.white} />
                    </TouchableOpacity>
                    <View style={{ gap: 4, flex: 1, flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text style={styles.headerTitle}>Appetec</Text>
                        <Text style={styles.youtubeLink} numberOfLines={1} ellipsizeMode="tail">{videoURL || "youtube.com"}</Text>
                    </View>
                </View>
                <ScrollView contentContainerStyle={{ paddingBottom: 56 }}>

                    <View style={styles.contentContainer}>
                        <Text style={styles.title}>
                            Watch Video
                        </Text>

                        <View style={styles.playerWrapper}>
                            <YoutubePlayer
                                height={videoHeight}
                                play={true}
                                videoId={videoId}
                                onError={(error) => { }}
                                onChangeState={(state) => { }}
                                onReady={() => { }}
                                webViewProps={{
                                    androidLayerType: Platform.OS === 'android' ? 'hardware' : undefined,
                                    renderToHardwareTextureAndroid: true,
                                    androidHardwareAccelerationDisabled: false,
                                    style: {
                                        opacity: 0.99,
                                        overflow: 'hidden',
                                    }
                                }}
                                initialPlayerParams={{
                                    preventFullScreen: false,
                                    cc_lang_pref: "us",
                                    showClosedCaptions: false,
                                    controls: true,
                                    modestbranding: true,
                                    rel: 0,
                                    showinfo: 0,
                                    iv_load_policy: 3,
                                    fs: 0,
                                    playsinline: 1
                                }}
                            />
                        </View>

                        <Text style={styles.description}>{description}</Text>
                    </View>
                </ScrollView>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        top: 10,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: Colors.white,
        // justifyContent: 'center',
        zIndex: 1000,
    },
    contentContainer: {
        padding: 20,
        width: '100%',
        alignItems: 'center',
    },
    playerWrapper: {
        width: '100%',
        aspectRatio: 16 / 9,
        backgroundColor: Colors.primaryGreen,
        borderRadius: 16,
        overflow: 'hidden',
        // elevation: Platform.OS === 'android' ? 1 : 0,
    },
    title: {
        fontSize: 26,
        fontFamily: ThemeFonts.Exo_800,
        color: Colors.black,
        textAlign: 'center',
        marginBottom: 30,
        paddingHorizontal: 10,
        textTransform: 'capitalize',
    },
    description: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.black,
        textAlign: 'left',
        marginTop: 20,
        paddingHorizontal: 20,
        lineHeight: 24,
        textTransform: 'capitalize',
    },
    closeButton: {
        // padding: 10,
        zIndex: 1001,
        backgroundColor: 'transparent',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 10,
        top: '50%',
        transform: [{ translateY: -20 }],
    },
    headerContainer: {
        backgroundColor: Colors.darkGray,
        padding: 16,
        flexDirection: 'row',
    },
    headerTitle: {
        color: Colors.white,
        fontSize: 20,
        fontFamily: ThemeFonts.Exo_600,
        textAlign: 'center',
        // flex: 1,
    },
    youtubeLink: {
        color: Colors.white,
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        textAlign: 'center',
        width: '70%',
    },
});

export default YouTubePlayerModal;