import { FlatList, StyleSheet, Text, View } from 'react-native'
import React, { memo } from 'react'
import RecipeCard from '../CustomCards/RecipeCard'
import { Colors } from 'constants/theme/colors'
import { ThemeFonts } from 'constants/theme/fonts'
import SkeletonItem, { SkeletonRow } from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader'
import { screenWidth } from 'constants/sizes'

const RecipeList = memo(({ heading, data, isLoading }) => {
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.heading}>{heading}</Text>
                <Text style={styles.filterTag}>Popular</Text>
            </View>
            <SkeletonRow items={3} height={150} width={screenWidth * .37} borderRadius={25} isLoading={isLoading} style={{ marginVertical: 20 }}>
                <FlatList
                    data={data}
                    keyExtractor={(item) => item.id}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={{
                        marginVertical: 20
                    }}
                    initialNumToRender={3}
                    maxToRenderPerBatch={3}
                    contentContainerStyle={{ gap: 12 }}
                    renderItem={({ item }) => <RecipeCard recipe={item} />}
                />
            </SkeletonRow>
        </View>
    )
})

export default RecipeList

const styles = StyleSheet.create({
    container: {
    },
    header: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        marginTop: 16,
        marginHorizontal: 24,
    },
    heading: {
        fontSize: 25,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_500
    },
    filterTag: {
        backgroundColor: Colors.primaryGreen,
        fontSize: 18,
        color: Colors.white,
        padding: 2,
        paddingHorizontal: 24,
        borderRadius: 50,
        fontFamily: ThemeFonts.Exo_400
    }
})