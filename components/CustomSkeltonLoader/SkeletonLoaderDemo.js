import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import SkeletonItem, {
  SkeletonCard,
  SkeletonCircle,
  SkeletonText,
  SkeletonRow,
  SkeletonList,
  SkeletonProfile,
  SkeletonCardWithContent,
  SkeletonDashboard,
} from './AdvancedSkeletonLoader';

const SkeletonLoaderDemo = () => {
  const [isLoading, setIsLoading] = useState(true);

  // Toggle loading state for demo purposes
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const toggleLoading = () => {
    setIsLoading(!isLoading);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Skeleton Loader Demo</Text>
      
      <TouchableOpacity style={styles.toggleButton} onPress={toggleLoading}>
        <Text style={styles.toggleButtonText}>
          {isLoading ? 'Show Content' : 'Show Skeleton'}
        </Text>
      </TouchableOpacity>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Basic Skeleton Items</Text>
        
        <View style={styles.demoRow}>
          <Text style={styles.demoLabel}>Text:</Text>
          <SkeletonText isLoading={isLoading}>
            <Text>Loaded Content</Text>
          </SkeletonText>
        </View>
        
        <View style={styles.demoRow}>
          <Text style={styles.demoLabel}>Circle:</Text>
          <SkeletonCircle size={40} isLoading={isLoading} />
        </View>
        
        <View style={styles.demoRow}>
          <Text style={styles.demoLabel}>Card:</Text>
          <SkeletonCard height={80} isLoading={isLoading}>
            <View style={styles.loadedCard}>
              <Text>Loaded Card Content</Text>
            </View>
          </SkeletonCard>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Composite Skeletons</Text>
        
        <View style={styles.demoItem}>
          <Text style={styles.demoLabel}>Row:</Text>
          <SkeletonRow items={3} height={30} isLoading={isLoading}>
            <View style={styles.rowContent}>
              <View style={styles.rowItem}><Text>Item 1</Text></View>
              <View style={styles.rowItem}><Text>Item 2</Text></View>
              <View style={styles.rowItem}><Text>Item 3</Text></View>
            </View>
          </SkeletonRow>
        </View>
        
        <View style={styles.demoItem}>
          <Text style={styles.demoLabel}>Profile:</Text>
          <SkeletonProfile isLoading={isLoading}>
            <View style={styles.profileContent}>
              <View style={styles.avatar} />
              <View>
                <Text style={styles.profileName}>John Doe</Text>
                <Text style={styles.profileInfo}>Software Developer</Text>
              </View>
            </View>
          </SkeletonProfile>
        </View>
        
        <View style={styles.demoItem}>
          <Text style={styles.demoLabel}>Card with Content:</Text>
          <SkeletonCardWithContent isLoading={isLoading}>
            <View style={styles.cardWithContentLoaded}>
              <View style={styles.cardImagePlaceholder} />
              <View style={styles.cardContentLoaded}>
                <Text style={styles.cardTitle}>Card Title</Text>
                <Text style={styles.cardDescription}>This is a description of the card content that would be displayed when loaded.</Text>
                <Text style={styles.cardSubtext}>Additional information</Text>
              </View>
            </View>
          </SkeletonCardWithContent>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Complex Layout</Text>
        
        <View style={styles.demoItem}>
          <Text style={styles.demoLabel}>Dashboard:</Text>
          <SkeletonDashboard isLoading={isLoading}>
            <View style={styles.dashboardContent}>
              <View style={styles.dashboardHeader}>
                <Text style={styles.dashboardTitle}>Dashboard Content</Text>
              </View>
              <View style={styles.dashboardStats}>
                <View style={styles.statItem}><Text>Stat 1</Text></View>
                <View style={styles.statItem}><Text>Stat 2</Text></View>
                <View style={styles.statItem}><Text>Stat 3</Text></View>
              </View>
              <Text style={styles.listTitle}>Recent Items</Text>
              <View style={styles.listContent}>
                <View style={styles.listItem}><Text>List Item 1</Text></View>
                <View style={styles.listItem}><Text>List Item 2</Text></View>
                <View style={styles.listItem}><Text>List Item 3</Text></View>
              </View>
            </View>
          </SkeletonDashboard>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom Skeleton</Text>
        
        <View style={styles.demoItem}>
          <Text style={styles.demoLabel}>Timer Circle:</Text>
          <View style={styles.timerContainer}>
            <SkeletonCircle size={150} isLoading={isLoading} style={styles.outerCircle}>
              <View style={styles.loadedOuterCircle}>
                <View style={styles.loadedInnerCircle}>
                  <Text style={styles.timerText}>25:00</Text>
                </View>
              </View>
            </SkeletonCircle>
            
            <View style={styles.timerControls}>
              <SkeletonRow items={3} height={40} gap={16} isLoading={isLoading}>
                <View style={styles.rowContent}>
                  <View style={styles.controlButton}><Text>Pause</Text></View>
                  <View style={styles.controlButton}><Text>Resume</Text></View>
                  <View style={styles.controlButton}><Text>End</Text></View>
                </View>
              </SkeletonRow>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.primaryGreen,
    marginBottom: 16,
  },
  toggleButton: {
    backgroundColor: Colors.primaryGreen,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 24,
  },
  toggleButtonText: {
    color: Colors.white,
    fontFamily: ThemeFonts.Exo_600,
    fontSize: 16,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.primaryGreen,
    marginBottom: 16,
  },
  demoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  demoItem: {
    marginBottom: 24,
  },
  demoLabel: {
    width: 80,
    fontSize: 14,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.darkGray,
    marginRight: 8,
  },
  loadedCard: {
    height: 80,
    backgroundColor: Colors.lightGreen,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rowContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  rowItem: {
    flex: 1,
    backgroundColor: Colors.lightGreen,
    padding: 8,
    borderRadius: 4,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  profileContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.lightGreen,
    marginRight: 16,
  },
  profileName: {
    fontSize: 16,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.black,
    marginBottom: 4,
  },
  profileInfo: {
    fontSize: 14,
    fontFamily: ThemeFonts.Exo_400,
    color: Colors.darkGray,
  },
  cardWithContentLoaded: {
    borderRadius: 25,
    overflow: 'hidden',
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.lightGray,
  },
  cardImagePlaceholder: {
    height: 150,
    backgroundColor: Colors.lightGreen,
  },
  cardContentLoaded: {
    padding: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.black,
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    fontFamily: ThemeFonts.Exo_400,
    color: Colors.darkGray,
    marginBottom: 4,
  },
  cardSubtext: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_400,
    color: Colors.gray,
  },
  dashboardContent: {
    padding: 16,
  },
  dashboardHeader: {
    height: 180,
    backgroundColor: Colors.lightGreen,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  dashboardTitle: {
    fontSize: 24,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.primaryGreen,
  },
  dashboardStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statItem: {
    flex: 1,
    height: 80,
    backgroundColor: Colors.lightGreen,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  listTitle: {
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.primaryGreen,
    marginBottom: 16,
  },
  listContent: {
    gap: 16,
  },
  listItem: {
    height: 80,
    backgroundColor: Colors.lightGreen,
    borderRadius: 8,
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  timerContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  outerCircle: {
    borderWidth: 8,
    borderColor: Colors.lightGreen,
  },
  loadedOuterCircle: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: Colors.lightGreen,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadedInnerCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.primaryGreen,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerText: {
    fontSize: 24,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
  },
  timerControls: {
    width: '100%',
    marginTop: 24,
  },
  controlButton: {
    flex: 1,
    height: 40,
    backgroundColor: Colors.lightGreen,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
});

export default SkeletonLoaderDemo;
