import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { ThemeFonts } from 'constants/theme/fonts';
import { Colors } from 'constants/theme/colors';
import { screenWidth } from 'constants/sizes';
import { useNavigation } from '@react-navigation/native';
import { Entypo } from "@expo/vector-icons";

const RecipeCard = ({ recipe, onPress }) => {

    const navigator = useNavigation();

    return (
        <TouchableOpacity activeOpacity={.8} style={styles.container} onPress={() => {
            navigator.navigate("Recipe Details", { id: recipe.id });
            onPress && onPress();
        }}>
            {
                recipe?.thumbnailUrl ? (
                    <Image source={{ uri: recipe.thumbnailUrl }} resizeMode='cover' style={styles.recipeImg} />
                ) : (
                    <View style={styles.imagePlaceholder}>
                        <View style={styles.addImageIcon}>
                            <Entypo name="image" size={40} color={Colors.gray} />
                        </View>
                    </View>
                )
            }
            <Text style={styles.recipeName} numberOfLines={1}>{recipe.title}</Text>
            <Text style={styles.recipeCal}>{`${recipe.nutritionByQuantity[0].calories || 0} kcal`}</Text>
        </TouchableOpacity>
    )
}

export default RecipeCard

const styles = StyleSheet.create({
    container: {
        width: screenWidth * .37,
    },
    recipeImg: {
        width: "100%",
        borderRadius: 25,
        aspectRatio: 1,
        borderWidth: 1,
        borderColor: Colors.primaryGreen
    },
    recipeName: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_700,
        marginTop: 8
    },
    recipeCal: {
        fontSize: 12,
        fontFamily: ThemeFonts.Exo_400
    },
    imagePlaceholder: {
        width: "100%",
        backgroundColor: Colors.lightGray,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 25,
        aspectRatio: 1,
        borderWidth: 1,
        borderColor: Colors.primaryGreen
    },
    addImageIcon: {
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
    },
})