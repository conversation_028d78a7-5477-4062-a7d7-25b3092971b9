import React, { useMemo, useCallback, useRef } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, FlatList } from 'react-native';
import { Colors } from 'constants/theme/colors';
import { memo } from 'react';

const menuItems = [
    { name: 'Nutrition', icon: 'nutrition' },
    { name: 'Activity', icon: 'fitness' },
    { name: 'Mo<PERSON>', icon: 'happy' },
    { name: 'Weight Loss Tracking', icon: 'scale' },
    { name: 'Video Library', icon: 'videocam' },
    { name: 'Sleep', icon: 'moon' },
    { name: 'Device', icon: 'hardware-chip' },
    { name: 'Recipes', icon: 'restaurant' },
];

// Move this outside component to prevent recreation
const keyExtractor = (item) => item.name;

const MenuItem = memo(({ item, onPress }) => (
    <TouchableOpacity
        activeOpacity={0.85}
        style={menuStyles.menuItem}
        onPress={onPress}
    >
        <Text style={menuStyles.menuText}>{item.name}</Text>
    </TouchableOpacity>
));

export const MenuList = memo(({ onNavigate, bottomPadding = 0, throttleDelay = 500 }) => {
    const lastTapTime = useRef(0);

    // Memoize styles that depend on props
    const containerStyle = useMemo(() => [
        menuStyles.menuList,
        { paddingBottom: bottomPadding }
    ], [bottomPadding]);

    const contentContainerStyle = useMemo(() => ({
        paddingBottom: bottomPadding
    }), [bottomPadding]);

    // Throttled navigation handler
    const handleItemPress = useCallback((itemName) => {
        const now = Date.now();

        // Check if enough time has passed since last tap
        if (now - lastTapTime.current >= throttleDelay) {
            lastTapTime.current = now;
            onNavigate(itemName);
        }
    }, [onNavigate, throttleDelay]);

    // Memoize renderItem to prevent recreation
    const renderItem = useCallback(({ item }) => (
        <MenuItem
            item={item}
            onPress={() => handleItemPress(item.name)}
        />
    ), [handleItemPress]);

    return (
        <View style={containerStyle}>
            <Text style={menuStyles.menuHeader}>Menu</Text>
            <FlatList
                data={menuItems}
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                keyExtractor={keyExtractor}
                renderItem={renderItem}
                contentContainerStyle={contentContainerStyle}
                initialNumToRender={8}
                maxToRenderPerBatch={5}
                updateCellsBatchingPeriod={50}
                removeClippedSubviews={true}
            />
        </View>
    );
});

const menuStyles = StyleSheet.create({
    menuHeader: {
        fontSize: 28,
        color: Colors.black,
        textAlign: 'flex-start',
        marginHorizontal: 24,
        marginVertical: 10,
        fontFamily: 'Exo_700Bold',
    },
    menuList: {
        width: '100%',
        height: '100%',
        paddingHorizontal: 20,
        paddingVertical: 28,
    },
    menuItem: {
        width: '100%',
        backgroundColor: Colors.primaryPurple,
        borderRadius: 20,
        padding: 5,
        marginVertical: 6,
        justifyContent: 'center',
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    menuText: {
        marginHorizontal: 16,
        marginVertical: 6,
        fontSize: 16,
        fontWeight: '600',
        color: Colors.white,
        textAlign: 'flex-start',
        fontFamily: 'Exo_500Medium',
    }
});