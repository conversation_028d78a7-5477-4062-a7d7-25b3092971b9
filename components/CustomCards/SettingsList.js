import React, { memo, useCallback, useRef, useMemo } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, StatusBar, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from 'constants/theme/colors';

const settingsItems = [
    { name: 'Profile', icon: 'person' },
    { name: 'Goals', icon: 'notifications' },
    { name: 'Device Management', icon: 'lock-closed' },
    { name: 'Reminders', icon: 'help-circle' },
    { name: 'App Integration', icon: 'language' },
    { name: 'FAQs', icon: 'color-palette' },
    { name: 'Help', icon: 'information-circle' },
    { name: 'Contact Us', icon: 'dialer' },
    { name: 'Logout', icon: 'log-out' }
];

const keyExtractor = (item) => item.name;

const SettingsItem = memo(({ item, onPress }) => (
    <TouchableOpacity
        activeOpacity={0.85}
        style={menuStyles.menuItem}
        onPress={onPress}
    >
        <Text style={menuStyles.menuText}>{item.name}</Text>
    </TouchableOpacity>
));

export const SettingsList = memo(({ onNavigate, bottomPadding = 0, throttleDelay = 500 }) => {
    const lastTapTime = useRef(0);

    // Memoize styles that depend on props
    const containerStyle = useMemo(() => [
        menuStyles.menuList,
        { paddingBottom: bottomPadding }
    ], [bottomPadding]);

    const contentContainerStyle = useMemo(() => ({
        paddingBottom: bottomPadding
    }), [bottomPadding]);

    // Throttled navigation handler
    const handleItemPress = useCallback((itemName) => {
        const now = Date.now();

        // Check if enough time has passed since last tap
        if (now - lastTapTime.current >= throttleDelay) {
            lastTapTime.current = now;
            onNavigate(itemName);
        }
    }, [onNavigate, throttleDelay]);

    // Memoize renderItem to prevent recreation
    const renderItem = useCallback(({ item }) => (
        <SettingsItem
            item={item}
            onPress={() => handleItemPress(item.name)}
        />
    ), [handleItemPress]);

    return (
        <View style={containerStyle}>
            {/* <StatusBar /> */}
            <Text style={menuStyles.menuHeader}>Settings</Text>
            <FlatList
                data={settingsItems}
                showsHorizontalScrollIndicator={false}
                accessibilityShowsLargeContentViewer={false}
                showsVerticalScrollIndicator={false}
                keyExtractor={keyExtractor}
                renderItem={renderItem}
                contentContainerStyle={contentContainerStyle}
                initialNumToRender={9}
                maxToRenderPerBatch={5}
                updateCellsBatchingPeriod={50}
                removeClippedSubviews={true}
            />
        </View>
    );
});

const menuStyles = StyleSheet.create({
    menuHeader: {
        fontSize: 28,
        color: Colors.black,
        textAlign: 'start',
        marginHorizontal: 24,
        marginVertical: 10,
        fontFamily: 'Exo_700Bold',
    },
    menuList: {
        width: '100%',
        height: '100%',
        paddingHorizontal: 20,
        paddingVertical: 28,
    },
    menuItem: {
        width: '100%',
        backgroundColor: Colors.primaryPurple,
        borderRadius: 20,
        padding: 5,
        marginVertical: 6,
        justifyContent: 'center',
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    menuText: {
        marginHorizontal: 16,
        marginVertical: 6,
        fontSize: 16,
        fontWeight: '600',
        color: Colors.white,
        textAlign: 'start',
        fontFamily: 'Exo_500Medium',
    }
});