import React, { memo } from 'react';
import { View, Text, Image, StyleSheet, Platform } from 'react-native';
import CustomToggleButton from 'components/CustomAction/CustomToggleButton';
import { Colors } from 'constants/theme/colors';
import useHealthPermissionStore from 'store/healthPermissionStore';

export const HealthOptionRow = memo(({
    icon,
    onToggle,
    checked,
}) => {
    const { isLoadingHealthPermissions } = useHealthPermissionStore(state => state);

    // Get platform-specific description text
    const descriptionText = Platform.OS === 'ios'
        ? "Appetec would like to access and update your health data. For syncing your data with Apple Health, we need your permission to read health metrics."
        : "Appetec would like to access and update your health data. For syncing your data with app download any health metric tracker, we recommend using Google Fit.";

    return (
        <View style={styles.rowContainer}>
            <View style={styles.imageContainer}>
                <Image
                    source={icon}
                    style={[
                        styles.healthIcon,
                        { opacity: 2 }
                    ]}
                />
            </View>
            <View style={styles.textContainer}>
                <Text style={styles.sectionHeading}>Workout & Health data</Text>
                <Text style={styles.sectionDescription}>
                    {descriptionText}
                </Text>
            </View>

            <View style={styles.toggleContainer}>
                <CustomToggleButton
                    checked={checked}
                    onToggle={onToggle}
                    disabled={isLoadingHealthPermissions}
                />
            </View>
        </View>
    );
});


const styles = StyleSheet.create({
    rowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: 10,
    },
    imageContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        width: 40,
        height: 60,
        borderColor: Colors.gray,
        borderWidth: 1,
        borderRadius: 20,
        padding: 1,
    },
    healthIcon: {
        width: '90%',
        height: '90%',
        resizeMode: 'contain',
        padding: 5
    },
    textContainer: {
        flex: 3,
        marginLeft: 10,
    },
    sectionHeading: {
        fontSize: 12,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
    },
    sectionDescription: {
        fontSize: 8,
        fontFamily: 'Exo_400Regular',
        color: Colors.darkGray,
    },
    toggleContainer: {
        flex: 1,
        alignItems: 'flex-end',
    },
});