import { StyleSheet, Text, View } from "react-native";
import React, { memo } from "react";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import { FlatList } from "react-native";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const HighlightCard = memo(({ highlightData, isLoading }) => {
  if (isLoading) return <SkeletonItem height={180} borderRadius={25} />;

  if (!highlightData || highlightData.length === 0) {
    return (
      <View style={styles.highlightSection}>
        <Text style={styles.highlightSectionHeading}>Highlights</Text>
        <View style={{ flexDirection: "row", gap: 5, paddingVertical: 8 }}>
          <Text style={styles.highlightsText}>-</Text>
          <Text style={[styles.highlightsText, { flex: 1 }]}>
            {"No highlights. Keep a positive attitude!"}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.highlightSection}>
      <Text style={styles.highlightSectionHeading}>Highlights</Text>
      <FlatList
        data={highlightData}
        keyExtractor={(_, index) => index.toString()}
        contentContainerStyle={{
          paddingVertical: 8,
          gap: 4,
        }}
        scrollEnabled={false}
        renderItem={({ item }) => (
          <View style={{ flexDirection: "row", gap: 5 }}>
            <Text style={styles.highlightsText}>-</Text>
            <Text
              style={[styles.highlightsText, { flex: 1 }]}
            >{`${item}`}</Text>
          </View>
        )}
      />
    </View>
  );
});

export default HighlightCard;

const styles = StyleSheet.create({
  highlightSection: {
    backgroundColor: Colors.primaryGreen,
    // marginHorizontal: 8,
    padding: 22,
    borderRadius: 25,
    paddingTop: 16,
  },
  highlightSectionHeading: {
    fontSize: 19,
    color: Colors.veryLightGreen,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  highlightsText: {
    fontSize: 16,
    color: Colors.white,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_400,
    flexWrap: "wrap",
  },
});
