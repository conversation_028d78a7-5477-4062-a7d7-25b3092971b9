import { CustomToggleButton } from 'components/CustomAction';
import { Colors } from 'constants/theme/colors';
import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import useHealthPermissionStore from 'store/healthPermissionStore';

const CustomHealthPermissionCard = ({
    iconSource,
    title,
    description,
    isEnabled,
    onToggle,
    lastUpdated
}) => {
    const { isLoadingHealthPermissions } = useHealthPermissionStore(state => state);

    return (
        <View style={styles.dropdownItem}>
            <View style={styles.rowContainer}>
                <View style={styles.imageContainer}>
                    <Image source={iconSource} style={styles.healthIcon} />
                </View>
                <View style={styles.textContainer}>
                    <Text style={styles.sectionHeading}>{title}</Text>
                    <Text style={styles.sectionSubHeading}>{`Last updated ${lastUpdated}`}</Text>
                </View>
            </View>
            <View style={styles.secondRowContainer}>
                <Text style={styles.sectionDescription}>{description}</Text>
                <View style={{ alignSelf: "flex-start", marginTop: 8 }}>
                    <CustomToggleButton checked={isEnabled} onToggle={onToggle} disabled={isLoadingHealthPermissions} />
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    dropdownItem: {
        marginLeft: -20,
        paddingTop: 2,
        paddingHorizontal: 2,
        backgroundColor: '#fff',
        // borderRadius: 8,
        marginBottom: 10,
    },
    rowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    secondRowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    imageContainer: {
        marginRight: 16,
        borderWidth: .5,
        borderColor: Colors.black,
        borderRadius: 16,
        padding: 7,
    },
    healthIcon: {
        width: 52,
        height: 52,
    },
    textContainer: {
        flex: 1,
    },
    sectionHeading: {
        fontSize: 19,
        fontFamily: "Exo_700Bold"
    },
    sectionSubHeading: {
        fontSize: 12,
        fontFamily: "Exo_500Medium"
    },
    sectionDescription: {
        flex: 1,
        fontSize: 14,
        fontFamily: "Exo_400Regular"
    },
});

export default CustomHealthPermissionCard;