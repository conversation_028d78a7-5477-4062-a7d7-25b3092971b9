import { window } from 'constants/sizes';
import * as React from "react";
import { Text, View } from "react-native";
import { useSharedValue, withTiming } from "react-native-reanimated";
import Carousel from "react-native-reanimated-carousel";

const CustomCarousel = ({
    data = [],
    autoPlayInterval = 2000,
    height = 258,
    loop = true,
    pagingEnabled = true,
    snapEnabled = true,
    mode = "parallax",
    parallaxConfig = { parallaxScrollingScale: 0.9, parallaxScrollingOffset: 50 },
}) => {
    const progress = useSharedValue(0);

    const handleProgressChange = (progressValue) => {
        progress.value = withTiming(progressValue, { duration: 500 });
    };

    // Define renderItem function
    const renderItem = ({ index }) => (
        <View
            style={{
                flex: 1,
                borderWidth: 1,
                justifyContent: "center",
            }}
        >
            <Text style={{ textAlign: "center", fontSize: 30 }}>{index}</Text>
        </View>
    );

    return (
        <View>
            <Carousel
                autoPlayInterval={autoPlayInterval}
                data={data}
                height={height}
                loop={loop}
                pagingEnabled={pagingEnabled}
                snapEnabled={snapEnabled}
                width={window.width}
                style={{ width: window.width }}
                mode={mode}
                modeConfig={parallaxConfig}
                onProgressChange={handleProgressChange} // use callback
                renderItem={renderItem}
            />
        </View>
    );
};

export default CustomCarousel;
