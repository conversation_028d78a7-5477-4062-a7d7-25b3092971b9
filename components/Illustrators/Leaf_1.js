import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import Leaf from 'assets/illustrator/illustrator_2.png';

const Leaf_1 = () => {
    return (
        <View>
            <Text>
                <Image source={Leaf} style={styles.image} />
            </Text>
        </View>
    );
};

export default Leaf_1;

const styles = StyleSheet.create({
    image: {
        position: 'absolute',
        bottom: '-35%',
        left: '-10%',
        width: '45%',
        height: '45%',
        resizeMode: 'contain',
        zIndex: 1,
        opacity: 0.7,
        transform: [{ translateY: '-50%' }],
    },
});