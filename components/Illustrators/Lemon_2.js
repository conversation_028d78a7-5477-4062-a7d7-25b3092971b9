import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import Lemon from 'assets/illustrator/illustrator_1.png';

const Lemon_2 = () => {
    return (
        <View>
            <Text>
                <Image source={Lemon} style={styles.image} />
            </Text>
        </View>
    );
};

export default Lemon_2;

const styles = StyleSheet.create({
    image: {
        position: 'absolute',
        bottom: '-35%',
        left: '-10%',
        width: '45%',
        height: '45%',
        resizeMode: 'contain',
        zIndex: 1,
        opacity: 0.7,
        transform: [{ translateY: '-50%' }],
    },
});