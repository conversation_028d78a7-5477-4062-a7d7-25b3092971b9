import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import Lemon from 'assets/illustrator/illustrator_1.png';
const Lemon_1 = () => {
    return (
        <View>
            <Text>
                <Image source={Lemon} style={styles.image} />
            </Text>
        </View>
    );
};

export default Lemon_1;

const styles = StyleSheet.create({
    image: {
        position: 'absolute',
        top: '12%',
        right: '-8%',
        width: '25%',
        height: '25%',
        resizeMode: 'contain',
        zIndex: 1,
        opacity: 0.7,
        transform: [{ translateY: '-50%' }],
    },
});