import { Linking, Modal, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { Colors } from 'constants/theme/colors';
import { CustomButton } from 'components/CustomAction';

const GoogleHealthInstallationPopUp = ({ showModal, setShowModal }) => {
    return (
        <Modal visible={showModal} transparent animationType="fade">
            <View style={styles.modalContainer}>
                <View style={styles.modalContent}>
                    <Text style={styles.modalTitle}>Google Health Not Installed</Text>
                    <Text style={styles.modalMessage}>To use health tracking features, please install Google Health from the Play Store.</Text>
                    <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "space-between" }}>
                        <CustomButton
                            title="Install Now"
                            onPress={() => {
                                Linking.openURL('market://details?id=com.google.android.apps.healthdata');
                                setShowModal();
                            }}
                        />
                        <CustomButton title="Cancel" onPress={setShowModal} style={styles.cancelButton} />
                    </View>
                </View>
            </View>
        </Modal>
    );
};

export default GoogleHealthInstallationPopUp;

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    }, modalContent: {
        backgroundColor: Colors.white,
        padding: 20,
        borderRadius: 15,
        width: '80%',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: Colors.darkGray,
        marginBottom: 10,
    },
    modalMessage: {
        fontSize: 14,
        color: Colors.darkGray,
        marginBottom: 20,
    },
    cancelButton: {
        // marginTop: 10,
    },
});