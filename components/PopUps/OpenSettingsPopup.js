import React from "react";
import { Colors } from 'constants/theme/colors';
import { View, Text, Modal, TouchableOpacity, Image, StyleSheet, Linking, Platform, TouchableWithoutFeedback } from "react-native";
import { BlurView } from 'expo-blur';

const OpenSettingsPopup = ({ visible, title, message, onClose, icon }) => {
    return (
        <Modal transparent visible={visible} animationType="fade" onRequestClose={() => {
            onClose();
        }}>
            <TouchableWithoutFeedback onPress={() => {
                onClose();
            }}>

                <View style={styles.modalBackground}>
                    {/* Glassmorphism Background Effect */}
                    <BlurView intensity={90} style={styles.blurOverlay} tint="light" blurReductionFactor={50} />

                    {/* Solid Alert Box */}
                    <View style={styles.alertBox}>
                        {icon && <Image source={icon} style={styles.alertIcon} />}
                        {title && <Text style={styles.alertTitle}>{title}</Text>}
                        <Text style={styles.alertMessage}>{message}</Text>
                        <View style={styles.buttonContainer}>
                            <TouchableOpacity
                                onPress={() => {
                                    if (Platform.OS === 'ios') {
                                        Linking.openURL('app-settings:');
                                    } else {
                                        Linking.openSettings();
                                    }
                                    onClose();
                                }}
                            >
                                <Text style={styles.buttonText}>{"Open settings"}</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </TouchableWithoutFeedback>
        </Modal>
    );
};

export default OpenSettingsPopup;

const styles = StyleSheet.create({
    modalBackground: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    blurOverlay: {
        ...StyleSheet.absoluteFillObject, // Covers the entire screen
        position: "absolute",
    },
    alertBox: {
        backgroundColor: Colors.white, // Keep the alert box solid
        borderRightWidth: 5,
        borderBottomWidth: 5,
        borderTopWidth: 1,
        borderLeftWidth: 1,
        borderColor: Colors.darkGreen,
        padding: 20,
        borderRadius: 20,
        textAlign: "center",
        width: "80%",
        maxWidth: 300,
        elevation: 10, // For Android shadow
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
    },
    alertIcon: {
        width: 30,
        height: 30,
        alignSelf: "center",
        marginBottom: 10,
    },
    alertTitle: {
        fontSize: 20,
        color: Colors.primaryPurple,
        textAlign: "center",
        marginBottom: 10,
        fontFamily: "Exo_700Bold",
    },
    alertMessage: {
        fontSize: 14,
        color: "black",
        textAlign: "center",
        marginBottom: 20,
        lineHeight: 20,
        fontFamily: "Exo_400Regular",
    },
    buttonContainer: {
        flexDirection: "row",
        justifyContent: "space-around",
    },
    button: {
        height: 34,
        width: 150,
        padding: 5,
        marginVertical: 2,
        justifyContent: "center",
        alignItems: "center",
    },
    buttonText: {
        color: Colors.black,
        fontFamily: "Exo_500Medium",
        fontSize: 16,
    },
});
