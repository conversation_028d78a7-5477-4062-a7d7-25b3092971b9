import { Colors } from 'constants/theme/colors';
import React from 'react';
import { View, TextInput, StyleSheet } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import { ThemeFonts } from 'constants/theme/fonts';

const CustomSearch = ({ value, onChangeText, placeholder, editable = true }) => {
    return (
        <View style={styles.searchContainer}>
            <Ionicons name="search" size={16} color={Colors.white} style={styles.searchIcon} />
            <TextInput
                autoCorrect={false}
                autoCapitalize="words"
                value={value}
                onChangeText={onChangeText}
                placeholder={placeholder}
                style={[styles.searchInput, {
                    fontFamily: value ? ThemeFonts.Exo_400 : ThemeFonts.Exo_400_Italic
                }]}
                placeholderTextColor={Colors.white}
                editable={editable}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.primaryPurple,
        borderRadius: 25,
        paddingHorizontal: 20,
        paddingVertical: 12,
        marginBottom: 10,
        gap: 10,
    },
    searchIcon: {
        marginRight: 2,
    },
    searchInput: {
        flex: 1,
        fontSize: 16,
        fontFamily: 'Exo_400Regular',
        color: Colors.white,
    },
});

export default CustomSearch;
