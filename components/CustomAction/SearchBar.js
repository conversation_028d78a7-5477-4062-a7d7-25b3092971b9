import { Animated, Easing, StyleSheet, Text, TextInput, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import Fontisto from '@expo/vector-icons/Fontisto'
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import CustomAnimatedBtn from './CustomAnimatedBtn';
import FlatListBottomLoader from 'components/Loaders/FlatListBottomLoader';
import { FlatList } from 'react-native-gesture-handler';

const SearchBar = ({ query, onValueChange, options, onPress, id, currentOpenId, setCurrentOpenId, onEndReached, loading, scrollToOffset, triggerZ = 10, listZ = 9, triggerBgColor = Colors.primaryPurple, triggerTextColor = Colors.white, placeholder = "Search for food", remeasureYpos, disabled = false }) => {
    const ref = useRef(null);

    const [showSearchResults, setShowSearchResults] = useState(false);
    const [scrollBarHeight, setScrollBarHeight] = useState(0);
    const [queryViewHeight, setQueryViewHeight] = useState(-10);

    const animatedHeight = useRef(new Animated.Value(40)).current;

    const [yPos, setYPos] = useState(0);

    useEffect(() => {
        const measurePosition = () => {
            ref.current?.measureInWindow((x, y) => {
                setYPos(y);
            });
        };

        measurePosition();
    }, [ref, remeasureYpos]);

    // useEffect(() => {
    //     console.log("called", options.length, (options.length >= 5 ? "5 * 42 + 20 " : "5 * (options.length + 1) * 42 + 20"));

    //     Animated.timing(animatedHeight, {
    //         toValue: showSearchResults ? (options.length >= 5 ? 5 * 42 + 20 : (options.length + 1) * 42 + 40) : 40,
    //         duration: 200,
    //         easing: Easing.ease,
    //         useNativeDriver: false,
    //     }).start();
    // }, [showSearchResults, query])


    useEffect(() => {
        setShowSearchResults(currentOpenId == id);
    }, [currentOpenId])

    const debounce = (func, delay) => {
        let timer;
        return function (...args) {
            clearTimeout(timer);
            timer = setTimeout(() => func.apply(this, args), delay);
        };
    };

    return (
        <View style={{ position: 'relative' }} ref={ref}>
            <View style={[styles.searchInputContainer, { zIndex: triggerZ, backgroundColor: triggerBgColor, }]}
                onLayout={(event) => {
                    const { height } = event.nativeEvent.layout;
                    setScrollBarHeight(height);
                }}
            >
                <Fontisto name='search' style={styles.searchIcon} size={16} color={triggerTextColor} />
                <TextInput editable={!disabled} placeholder={placeholder} placeholderTextColor={triggerTextColor} style={[styles.searchInputBox, {
                    color: triggerTextColor
                }, {
                    fontFamily: query ? ThemeFonts.Exo_400 : ThemeFonts.Exo_400_Italic
                }]}
                    onChangeText={debounce((value) => {
                        onValueChange(value);
                        setShowSearchResults(true);
                        setCurrentOpenId(id);
                    }, 300)}
                    numberOfLines={1}
                    onPress={() => {
                        setCurrentOpenId(id);
                        setShowSearchResults(true);
                        // ref.current?.measureInWindow((x, y,) => {
                        if (scrollToOffset) {
                            scrollToOffset(yPos);
                        }
                        // });
                    }}
                />
                <Ionicons
                    name={showSearchResults ? 'chevron-up' : 'chevron-down'}
                    size={20}
                    color={triggerTextColor}
                    onPress={() => {
                        setShowSearchResults((prev) => !prev);
                        setCurrentOpenId(id);
                        // ref.current?.measureInWindow((x, y,) => {
                        if (scrollToOffset) {
                            scrollToOffset(yPos);
                        }
                        // });
                        // setScrollEnabled();
                    }}
                    style={styles.arrowIcon}
                    disabled={disabled}
                />
            </View >
            {
                queryViewHeight == -10 && (
                    <View
                        style={[styles.yourViewStyle, { opacity: 0, position: 'absolute', zIndex: listZ }]}
                        onLayout={(event) => {
                            const { height } = event.nativeEvent.layout;
                            setQueryViewHeight(height);
                        }}
                    >
                        <CustomAnimatedBtn startColor={Colors.white} endColor={Colors.lightPurple} label={"item.label"} index={"index"} onPress={() => onPress("item.value")} />
                    </View>
                )
            }
            <Animated.View style={[
                styles.searchResultContainer, showSearchResults ? { height: options.length < 5 ? (loading ? (40 + scrollBarHeight - 4) : (options.length == 0 ? scrollBarHeight - 0 : ((options.length) * queryViewHeight + scrollBarHeight - 4))) : 5 * queryViewHeight + 20 } : { height: scrollBarHeight - 4 }, { backgroundColor: Colors.white },
                {

                    display: showSearchResults != 0 ? "flex" : "none"
                }
            ]}>
                <FlatList data={options} scrollEnabled={true} nestedScrollEnabled={true} keyExtractor={(item) => item.id} renderItem={({ item, index }) => (
                    <CustomAnimatedBtn startColor={Colors.white} endColor={Colors.lightPurple} label={item.label} index={index} onPress={() => onPress(item.value)} icon={item.icon} />
                )}
                    onEndReached={onEndReached}
                    ListFooterComponent={loading ? <View style={[{ transform: [{ scale: .7 }] }, options.length == 0 && { marginTop: 30 }]}>
                        <FlatListBottomLoader />
                    </View> : null}
                />
            </Animated.View>
        </View>
    )
}

export default SearchBar

const styles = StyleSheet.create({
    searchInputContainer: {
        flexDirection: "row",
        backgroundColor: Colors.primaryPurple,
        borderRadius: 25,
        alignItems: "center",
        gap: 8,
        overflow: "hidden",
        zIndex: 55,
        borderWidth: 2,
    },
    searchIcon: {
        paddingLeft: 24,
        paddingVertical: 8,
    },
    searchInputBox: {
        fontSize: 18,
        fontFamily: ThemeFonts.Exo_400,
        flex: 1,
        paddingVertical: 8,
        color: Colors.white,
    },
    arrowIcon: {
        paddingRight: 24,
        paddingVertical: 8,
    },
    searchResultContainer: {
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
        borderRadius: 25,
        overflow: "hidden",
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1,
    }
})