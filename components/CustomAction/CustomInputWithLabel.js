import { StyleSheet, Text, View } from 'react-native';
import React, { memo } from 'react';
import CustomInput from './CustomInput';
import { Colors } from 'constants/theme/colors';

const CustomInputWithLabel = ({ value, label, placeholder, onChangeText, backgroundColor = Colors.white, setCloseDropdowns, error, editable = true, changeBG = true, keyboardType = "default", clearValidationError, loading = false }) => {
    return (
        <View>
            <Text style={styles.inputLabel}>
                {label}
            </Text>
            <CustomInput value={value} label={label} placeholder={placeholder} onChangeText={onChangeText} backgroundColor={backgroundColor} setCloseDropdowns={setCloseDropdowns} editable={editable} changeBG={changeBG} keyboardType={keyboardType} clearValidationError={clearValidationError} loading={loading} />
            {error && <Text style={styles.error}>{error}</Text>}
        </View>
    );
};

export default memo(CustomInputWithLabel);

const styles = StyleSheet.create({
    inputLabel: {
        padding: 16,
        paddingHorizontal: 24,
        backgroundColor: Colors.primaryGreen,
        borderRadius: 25,
        color: Colors.white,
        fontSize: 19,
        fontFamily: "Exo_700Bold"
    },
    error: {
        fontFamily: "Exo_500Medium",
        fontSize: 12,
        color: "red",
        marginLeft: 16,
        position: "absolute",
        bottom: -10,
    }
});