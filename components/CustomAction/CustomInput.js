import { Colors } from "constants/theme/colors";
import React, { useState } from "react";
import { View, TextInput, StyleSheet, TouchableOpacity } from "react-native";
import Ionicons from "@expo/vector-icons/Ionicons";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";
import { ThemeFonts } from "constants/theme/fonts";

const CustomInput = ({
  value,
  onChangeText,
  placeholder,
  secureTextEntry,
  keyboardType,
  icon,
  onBlur,
  editable = false,
  backgroundColor = Colors.white,
  setCloseDropdowns,
  changeBG = false,
  integer = false,
  precision = null,
  isNegative = true,
  textColor = Colors.black,
  clearValidationError,
  loading = false,
}) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const handleTextChange = (text) => {
    if (clearValidationError) clearValidationError();

    if (integer) {
      if (isNegative) {
        const isValidNegative = /^-?\d*$/.test(text);
        if (isValidNegative) {
          onChangeText(text);
        }
      } else {
        const isPositiveInteger = /^\d*$/.test(text);
        if (isPositiveInteger) {
          onChangeText(text);
        }
      }
    } else if (precision != null) {
      if (!text.includes(",")) {
        const hasInvalidMinusSign = text.indexOf("-") > 0;
        if (hasInvalidMinusSign) {
          return;
        }

        if (!isNegative && text.startsWith("-")) {
          return;
        }

        if (text === "" || (isNegative && text === "-") || text === ".") {
          onChangeText(String(text).trimEnd());
        } else if (text.includes(".")) {
          const [integerPart, decimalPart] = text.split(".");
          if (text.split(".").length > 2) {
            onChangeText(
              `${integerPart}.${decimalPart.slice(0, precision)}`.trimEnd()
            );
          } else if (decimalPart && decimalPart.length > precision) {
            onChangeText(
              `${integerPart}.${decimalPart.slice(0, precision)}`.trimEnd()
            );
          } else {
            onChangeText(String(text).trimEnd());
          }
        } else {
          onChangeText(text.trimEnd());
        }
      }
    } else {
      onChangeText(text);
    }
  };

  if (loading) {
    return (
      <SkeletonItem height={44} borderRadius={25} isLoading={loading} style={{ marginVertical: 10 }} />
    );
  }

  return (
    <View
      style={[
        styles.inputContainer,
        editable && changeBG
          ? {
            backgroundColor: Colors.lightGray,
          }
          : { backgroundColor: backgroundColor },
      ]}
    >
      {icon && <View style={styles.iconContainer}>{icon}</View>}
      <TextInput
        style={[
          styles.input,
          icon ? styles.inputWithIcon : null,
          editable && changeBG
            ? {
              color: Colors.black,
            }
            : { color: textColor },
          {
            fontFamily: value ? ThemeFonts.Exo_400 : ThemeFonts.Exo_400_Italic
          }
        ]}
        keyboardType={keyboardType}
        value={value}
        onChangeText={handleTextChange}
        placeholder={placeholder}
        onBlur={onBlur}
        placeholderTextColor={editable && changeBG ? Colors.black : textColor}
        secureTextEntry={secureTextEntry && !isPasswordVisible}
        editable={editable || !value}
        onPress={() => {
          if (!setCloseDropdowns) return;
          setCloseDropdowns();
        }}
        onPressIn={() => {
          if (!clearValidationError) return;
          clearValidationError();
        }}
        numberOfLines={1}
      />

      {/* Show the eye icon only for password fields */}
      {secureTextEntry && (
        <TouchableOpacity
          style={styles.eyeIcon}
          onPress={() => setIsPasswordVisible(!isPasswordVisible)}
        >
          {isPasswordVisible ? (
            <Ionicons name="eye" size={24} color={Colors.primaryGreen} />
          ) : (
            <Ionicons name="eye-off" size={24} color={Colors.primaryGreen} />
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 10,
    width: "100%",
    borderRadius: 25,
    borderWidth: 2,
    borderColor: Colors.primaryPurple,
    backgroundColor: Colors.white,
    shadowColor: Colors.primaryPurple,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    paddingHorizontal: 20,
  },
  iconContainer: {
    paddingLeft: 6,
  },
  input: {
    flex: 1,
    paddingVertical: 7,
    fontFamily: "Exo_400Regular",
    fontSize: 18,
    color: Colors.black,
  },
  inputWithIcon: {
    paddingLeft: 10,
  },
  eyeIcon: {
    marginLeft: 10,
    marginRight: -4,
  },
});

export default CustomInput;
