import { StyleSheet, Text, View } from 'react-native'
import React, { memo, useEffect } from 'react'
import Toast from 'react-native-toast-message';
import { Colors } from 'constants/theme/colors';

const CustomToast = memo(({ error, clearErrors, isError = true, position = "bottom" }) => {
    useEffect(() => {
        if (error) {
            // requestAnimationFrame(() => {
            Toast.show({
                type: isError ? 'error' : 'success',
                text1: isError ? "Error" : "Success",
                text2: error,
                position: position,
                autoHide: true,
                bottomOffset: 80,
                topOffset: 80,
                hideOnPress: true,
                text1Style: { fontFamily: "Exo_700Bold", color: Colors.primaryPurple, fontSize: 16 },
                text2Style: { fontFamily: "Exo_500Medium", color: Colors.primaryPurple, fontSize: 14 },

            });
            clearErrors();
            // }); // Wait for the animation to finish before showing the toast    

        }
    }, [error])

    return null;
})

export default CustomToast

const styles = StyleSheet.create({})