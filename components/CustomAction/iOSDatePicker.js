import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from 'constants/theme/colors';
import CustomInput from './CustomInput';
import Ionicons from '@expo/vector-icons/Ionicons';

const DateOfBirthPicker = ({
    value,
    placeholder = "Date of Birth",
    onSelectDate,
    error,
    clearValidationError,
    onPress
}) => {
    const [showCalendar, setShowCalendar] = useState(false);
    const [selectedDate, setSelectedDate] = useState(value ? new Date(value) : null);

    // iOS-specific state to track the selection process
    const [iosDateSelectionInProgress, setIosDateSelectionInProgress] = useState(false);
    const [tempDate, setTempDate] = useState(null);

    // Set date limits
    const today = new Date();
    const maxDate = today;
    const minDate = new Date(today.getFullYear() - 120, today.getMonth(), today.getDate());

    // Update selectedDate when value prop changes
    useEffect(() => {
        if (value) {
            setSelectedDate(new Date(value));
        }
    }, [value]);

    // Calculate age from date of birth
    const calculateAge = (birthDate) => {
        if (!birthDate) return null;

        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDifference = today.getMonth() - birthDate.getMonth();

        if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }

        return age;
    };

    // Format date for display (Date object to DD/MM/YYYY)
    const formatDateForDisplay = (date) => {
        if (!date) return '';

        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    };

    // Handle date picker change
    const handleDateChange = (event, date) => {
        // If user cancels, close the picker
        if (event.type === 'dismissed') {
            setShowCalendar(false);
            setIosDateSelectionInProgress(false);
            setTempDate(null);
            return;
        }

        // Different handling for iOS vs Android
        if (Platform.OS === 'ios') {
            // Store the currently selected date
            setTempDate(date);

            // If this is the first selection in iOS picker flow
            if (!iosDateSelectionInProgress) {
                setIosDateSelectionInProgress(true);
                // Keep picker visible for iOS - don't close it yet
                return;
            }

            // On iOS, after the user has made selections, proceed with validation
            setIosDateSelectionInProgress(false);
        }

        // Use the appropriate date value based on platform
        const finalDate = Platform.OS === 'ios' ? tempDate || date : date;

        if (finalDate) {
            setSelectedDate(finalDate);

            // Format date for API (YYYY-MM-DD)
            const formattedDate = finalDate.toISOString().split('T')[0];
            const age = calculateAge(finalDate);

            // Call the parent component's callback with the selected date and age
            onSelectDate(formattedDate, age);
        }

        // Close picker on Android or on iOS if we've completed the selection process
        if (Platform.OS === 'android' || (Platform.OS === 'ios' && !iosDateSelectionInProgress)) {
            setShowCalendar(false);
        }
    };

    const handleOpenPicker = () => {
        // Reset iOS selection tracking when opening the picker
        if (Platform.OS === 'ios') {
            setIosDateSelectionInProgress(false);
            setTempDate(null);
        }

        setShowCalendar(true);

        if (onPress) {
            onPress();
        }

        if (clearValidationError) {
            clearValidationError();
        }
    };

    return (
        <View>
            <TouchableOpacity
                activeOpacity={0.9}
                onPress={handleOpenPicker}
            >
                <View pointerEvents="none">
                    <CustomInput
                        value={selectedDate ? formatDateForDisplay(selectedDate) : ''}
                        placeholder={placeholder}
                        editable={false}
                        icon={<Ionicons name="calendar" size={20} color={Colors.primaryGreen} />}
                    />
                </View>
            </TouchableOpacity>

            {error && <Text style={styles.error}>{error}</Text>}

            {showCalendar && (
                <DateTimePicker
                    value={selectedDate || tempDate || new Date()}
                    mode="date"
                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                    maximumDate={maxDate}
                    minimumDate={minDate}
                    onChange={handleDateChange}
                />
            )}

            {selectedDate && (
                <Text style={styles.ageText}>Age: {calculateAge(selectedDate)} years</Text>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    calendarContainer: {
        width: '90%',
        backgroundColor: Colors.white,
        borderRadius: 20,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    calendarHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15,
    },
    calendarTitle: {
        fontSize: 18,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20,
    },
    confirmButton: {
        backgroundColor: Colors.primaryGreen,
        paddingVertical: 10,
        paddingHorizontal: 30,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    cancelButton: {
        backgroundColor: Colors.lightGray,
        paddingVertical: 10,
        paddingHorizontal: 30,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        fontSize: 16,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
    },
    ageText: {
        marginLeft: 20,
        marginTop: 5,
        fontSize: 14,
        fontFamily: 'Exo_400Regular',
        color: Colors.primaryGreen,
    },
    error: {
        color: 'red',
        marginHorizontal: 7,
        fontSize: 12,
        marginBottom: 0,
    },
});

export default DateOfBirthPicker;