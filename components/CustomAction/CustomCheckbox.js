import React, { useState, memo, useImperativeHandle, forwardRef } from 'react';
import { View, TouchableWithoutFeedback, Text, StyleSheet } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import { Colors } from 'constants/theme/colors';

const CustomCheckbox = (props, ref) => {
    const { title = "Checkbox", checked = false, updateStatus, value, disabled, setList, updateSetList,clearValidationError } = props;
    const [isChecked, setIsChecked] = useState(checked);

    useImperativeHandle(ref, () => ({
        setCheckBox: (val) => setIsChecked(val),
    }));

    const handlePress = () => {
        const newCheckedState = !isChecked;
        setIsChecked(newCheckedState);

        if (typeof updateStatus === "function") {
            updateStatus(newCheckedState);
        }

        if (typeof updateSetList === "function" && value) {
            if (newCheckedState) {
                updateSetList(prev => [...prev, value]);
            } else {
                updateSetList(prev => prev.filter(item => item !== value));
            }
        }
    };

    return (
        <View style={styles.checkboxMainConatiner}>
            <TouchableWithoutFeedback disabled={disabled} onPress={handlePress} onPressIn={()=>{
                if(clearValidationError) clearValidationError();
            }}>
                <View style={[
                    styles.checkBoxContainer,
                    !isChecked && styles.checkboxInActive
                ]}>
                    {isChecked && (
                        <Ionicons
                            name="checkmark-sharp"
                            size={15}
                            color={Colors.primaryPurple}
                        // style={{ color: Colors.primaryPurple }}
                        />
                    )}
                </View>
            </TouchableWithoutFeedback>
            <Text style={styles.checkboxTitle}>{title}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    checkboxMainConatiner: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 15,
        marginHorizontal: 10,
    },
    checkBoxContainer: {
        width: 18,
        height: 18,
        borderRadius: 2,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
        backgroundColor: Colors.white,
    },
    checkboxInActive: {
        borderColor: Colors.primaryPurple,
        // backgroundColor: Colors.lightGreen
    },
    checkboxTitle: {
        color: Colors.white,
        fontFamily: 'Exo_400Regular',
        marginLeft: 10,
        fontSize: 12,
    },
});

export default memo(forwardRef(CustomCheckbox));