import React from "react";
import { View, StyleSheet } from "react-native";
import LottieView from "lottie-react-native";
import Loader from 'assets/json/loaderDarkPurple.json';
import { Colors } from 'constants/theme/colors';


const CustomLoader = ({ visible = true, small = false }) => {
    if (!visible) return null;

    return (
        <View style={styles.overlay}>
            <LottieView
                source={Loader} // Use your Lottie JSON animation
                autoPlay
                loop
                speed={2.5}
                style={[styles.animation, small && { width: 50, height: 50 }]}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        position: "absolute",
        width: "100%",
        height: "100%",
        // backgroundColor: Colors.primaryGreen,
        justifyContent: "center",
        alignItems: "center",
        color: Colors.primaryPurple,
        zIndex: 9999
    },
    animation: {
        width: 120,
        height: 120,
    },
});

export default CustomLoader;
