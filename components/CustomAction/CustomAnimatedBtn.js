import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useRef } from 'react'
import { Animated } from 'react-native';
import { ThemeFonts } from 'constants/theme/fonts';
import { Colors } from 'constants/theme/colors';
import { Image } from 'react-native';

const CustomAnimatedBtn = ({ startColor, endColor, label, onPress, index, icon }) => {
    const animatedBackgroundColorsRef = useRef(new Animated.Value(0)).current;

    const interpolatedBGColor = animatedBackgroundColorsRef.interpolate({
        inputRange: [0, 1],
        outputRange: [startColor, endColor],
    })

    return (
        <TouchableOpacity activeOpacity={.9}
            style={[styles.searchResultBtn, {
                backgroundColor: interpolatedBGColor
            }]}
            onPressIn={() => {
                Animated.timing(animatedBackgroundColorsRef, {
                    toValue: 1,
                    duration: 0,
                    useNativeDriver: true,
                }).start();
            }}
            onPressOut={() => {
                Animated.timing(animatedBackgroundColorsRef, {
                    toValue: 0,
                    duration: 500,
                    useNativeDriver: true,
                }).start();
            }}
            onPress={onPress}
        >
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                {icon && (icon == "custom" ? <Image source={require('assets/icons/custom_meal_icon.png')} style={[{ width: 16, height: 16, position: "absolute", left: 4 }, index == 0 && { top: 46 }]} /> : <Image source={require('assets/icons/recommended_icon.png')} style={[{ width: 16, height: 16, position: "absolute", left: 4 }, index == 0 && { top: 46 }]} />)}
                <Text style={[styles.searchResultText, index == 0 && { paddingTop: 44 }]} >{label}</Text>
            </View>
        </TouchableOpacity>
    )
}

export default CustomAnimatedBtn

const styles = StyleSheet.create({
    searchResultBtn: {
        backgroundColor: Colors.white
    },
    searchResultText: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_400,
        textTransform: "capitalize",
        color: Colors.black,
        paddingVertical: 8,
        paddingHorizontal: 26,
        // paddingLeft: 36
    },
})