import React, { useState, useRef, useEffect } from 'react';
import { Colors } from 'constants/theme/colors';
import { View, TouchableOpacity, Animated, StyleSheet, Text } from 'react-native';

const CustomToggleButton = ({ onToggle, checked, disabled = false }) => {
    const toggleAnim = useRef(new Animated.Value(checked ? 1 : 0)).current;

    useEffect(() => {
        const toValue = checked ? 1 : 0;
        Animated.timing(toggleAnim, {
            toValue,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, [checked]);

    const toggleSwitch = () => {
        if (disabled) return;
        onToggle();
    };

    const translateX = toggleAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [0, 22],
    });

    return (
        <View style={styles.container}>
            <TouchableOpacity onPress={toggleSwitch} activeOpacity={0.8}>
                <View style={[
                    styles.toggleContainer,
                    checked && styles.toggleContainerActive
                ]}>
                    <Animated.View
                        style={[
                            styles.toggleCircle,
                            { transform: [{ translateX }] },
                            checked && styles.toggleCircleActive,
                        ]}
                    />
                </View>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    label: {
        marginRight: 10,
        fontSize: 16,
    },
    toggleContainer: {
        width: 50,
        height: 27,
        borderRadius: 15,
        backgroundColor: Colors.white,
        justifyContent: 'center',
        padding: 1,
        borderColor: Colors.primaryGreen,
        borderWidth: 1,
    },
    toggleContainerActive: {
        backgroundColor: Colors.lightGreen,
    },
    toggleCircle: {
        width: 24,
        height: 24,
        borderRadius: 12,
        backgroundColor: Colors.gray,
    },
    toggleCircleActive: {
        backgroundColor: Colors.primaryGreen,
    },
});

export default CustomToggleButton;