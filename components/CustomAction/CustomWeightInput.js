import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { Colors } from 'constants/theme/colors';
import { TextInput } from 'react-native';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';
import { ThemeFonts } from 'constants/theme/fonts';

const CustomWeightInput = ({
    value,
    label,
    placeholder,
    onChangeText,
    backgroundColor = Colors.white,
    error,
    editable = true,
    changeBG = true,
    rightText,
    keyboardType = "number-pad",
    isLoading = false
}) => {
    const handleTextChange = (text) => {
        // Remove any non-numeric characters except decimal point
        const cleanedText = text.replace(/[^0-9.]/g, '');
        onChangeText(cleanedText);
    };

    return (
        <View>
            <Text style={styles.inputLabel}>
                {label}
            </Text>
            <SkeletonItem isLoading={isLoading} height={44} borderRadius={25} style={{ marginVertical: 10 }}>
                <View style={[
                    styles.inputContainer,
                    editable && changeBG ? {
                        backgroundColor: Colors.lightGray
                    } : { backgroundColor: backgroundColor }
                ]}>
                    <TextInput
                        style={[styles.input, {
                            fontFamily: value ? ThemeFonts.Exo_400 : ThemeFonts.Exo_400_Italic
                        }]}
                        keyboardType={keyboardType}
                        value={value}
                        onChangeText={handleTextChange}
                        placeholder={placeholder}
                        placeholderTextColor={Colors.darkGray}
                        editable={editable}
                    />
                    <Text style={styles.unitText}>{rightText}</Text>
                </View>
            </SkeletonItem>
            {error && <Text style={styles.error}>{error}</Text>}
        </View>
    );
};

export default CustomWeightInput;

const styles = StyleSheet.create({
    inputLabel: {
        padding: 16,
        paddingHorizontal: 24,
        backgroundColor: Colors.primaryGreen,
        borderRadius: 25,
        color: Colors.white,
        fontSize: 19,
        fontFamily: "Exo_700Bold"
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
        width: '100%',
        borderRadius: 25,
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
        backgroundColor: Colors.white,
        shadowColor: Colors.primaryPurple,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        paddingHorizontal: 20,
    },
    input: {
        flex: 1,
        paddingVertical: 7,
        fontFamily: 'Exo_400Regular',
        fontSize: 18,
        color: Colors.black,
    },
    unitText: {
        fontFamily: 'Exo_400Regular',
        fontSize: 18,
        color: Colors.darkGray,
        marginLeft: 5,
    },
    error: {
        fontFamily: "Exo_500Medium",
        fontSize: 12,
        color: "red",
        marginLeft: 16,
        position: "absolute",
        bottom: -10,
    }
});
