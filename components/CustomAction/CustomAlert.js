import React, { useEffect, useState } from "react";
import { Colors } from "constants/theme/colors";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Image,
  StyleSheet,
  TouchableWithoutFeedback,
} from "react-native";
import { BlurView } from "expo-blur";
import Icon from "react-native-vector-icons/Ionicons";
import { InteractionManager } from "react-native";
import { screenHeight, screenWidth } from "constants/sizes";

const CustomAlert = ({
  visible,
  title,
  message,
  buttons,
  onClose,
  icon,
  ionIcon,
}) => {
  const [canRenderModal, setCanRenderModal] = useState(false);

  useEffect(() => {
    const interaction = InteractionManager.runAfterInteractions(() => {
      setCanRenderModal(true);
    });

    return () => interaction.cancel();
  }, []);

  return (
    <Modal
      transparent
      visible={visible && canRenderModal}
      animationType="fade"
      onRequestClose={() => {
        onClose();
      }}
    >
      <TouchableWithoutFeedback
        onPress={() => {
          onClose();
        }}
      >
        <View style={styles.modalBackground}>
          {/* Glassmorphism Background Effect */}
          <BlurView
            intensity={90}
            style={styles.blurOverlay}
            tint="light"
            blurReductionFactor={50}
          />

          {/* Solid Alert Box */}
          <View style={styles.alertBox}>
            {icon && <Image source={icon} style={styles.alertIcon} />}
            {ionIcon && (
              <Icon
                name={ionIcon}
                size={30}
                color={Colors.primaryGreen}
                style={styles.alertIcon}
              />
            )}
            {title && <Text style={styles.alertTitle}>{title}</Text>}
            <Text style={styles.alertMessage}>{message}</Text>
            <View style={styles.buttonContainer}>
              {buttons.map((button, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    button.onPress();
                  }}
                  style={[styles.button, button.style && styles[button.style]]}
                >
                  <Text style={styles.buttonText}>{button.text}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default CustomAlert;

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    width: screenWidth,
    height: screenHeight,
    zIndex: 9999,
  },
  blurOverlay: {
    ...StyleSheet.absoluteFillObject, // Covers the entire screen
    position: "absolute",
  },
  alertBox: {
    backgroundColor: Colors.white, // Keep the alert box solid
    borderRightWidth: 5,
    borderBottomWidth: 5,
    borderTopWidth: 1,
    borderLeftWidth: 1,
    borderColor: Colors.darkGreen,
    padding: 20,
    borderRadius: 20,
    textAlign: "center",
    width: "90%",
    maxWidth: 350,
    elevation: 10, // For Android shadow
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  alertIcon: {
    width: 30,
    height: 30,
    alignSelf: "center",
    marginBottom: 10,
  },
  alertTitle: {
    fontSize: 20,
    color: Colors.primaryPurple,
    textAlign: "center",
    marginBottom: 10,
    fontFamily: "Exo_700Bold",
  },
  alertMessage: {
    fontSize: 12,
    color: "black",
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 20,
    fontFamily: "Exo_400Regular",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    gap: 5,
  },
  button: {
    flex: 1,
    padding: 7,
    marginVertical: 5,
    justifyContent: "center",
    alignItems: "stretch",
  },
  buttonText: {
    color: Colors.black,
    fontFamily: "Exo_500Medium",
    fontSize: 16,
    textAlign: "center",
  },
});