import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import React, { memo } from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";
import { color } from "react-native-reanimated";

const CustomButton = memo(({
  title,
  onPress,
  style = {},
  textStyle = {},
  backgroundColor,
  textColor,
  disabled = false,
  disabledBgColor = Colors.lightGray,
  disabledTextColor = Colors.darkGray,
  isLoading = false,
  Icon,
}) => {
  if (isLoading) {
    return (
      <View style={{ ...style }}>
        <SkeletonItem height={34} width={"100%"} borderRadius={20} />
      </View>
    );
  }
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={[
        styles.button,
        backgroundColor ? { backgroundColor } : styles.nextButton,
        // isBack ? styles.backButton : styles.nextButton,
        style,
        disabled && {
          ...styles.disabled,
          backgroundColor: disabledBgColor,
        },
      ]}
      onPress={onPress}
      disabled={disabled}
    >
      {
        Icon && Icon
      }
      <Text
        style={[
          styles.buttonText,
          textColor ? { color: textColor } : null,
          textStyle,
          disabled && {
            ...styles.disabled,
            color: disabledTextColor,
          },
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  button: {
    borderRadius: 20,
    padding: 6,
    paddingHorizontal: 8,
    marginVertical: 2,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    gap: 2,
    width: 110
  },
  nextButton: {
    backgroundColor: "#3D1D66",
  },
  backButton: {
    backgroundColor: "#3D1D66",
    marginRight: 10,
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontFamily: ThemeFonts.Exo_600,
  },
  disabled: {
    color: Colors.darkGray,
    fontFamily: ThemeFonts.Exo_700,
  },
});

export default CustomButton;
