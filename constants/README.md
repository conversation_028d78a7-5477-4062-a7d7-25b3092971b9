# Typography Constants

This file provides a comprehensive set of typography constants for consistent text styling across the app.

## How to Use

Import the Typography constants in your component:

```javascript
import { Typography } from 'constants/typography';
```

### Using Pre-defined Styles

The easiest way to use the typography constants is to use the pre-defined styles:

```javascript
<Text style={Typography.STYLES.H1}>Heading 1</Text>
<Text style={Typography.STYLES.BODY_REGULAR}>Regular body text</Text>
<Text style={Typography.STYLES.CAPTION_REGULAR}>Caption text</Text>
```

### Combining with Other Styles

You can combine the typography styles with other styles:

```javascript
<Text style={[Typography.STYLES.BODY_REGULAR, { marginBottom: 10 }]}>
  Text with additional styles
</Text>
```

### Creating Custom Styles

You can create custom styles using the individual typography constants:

```javascript
const customStyle = {
  fontFamily: Typography.FONT_FAMILY.EXO.BOLD,
  fontSize: Typography.FONT_SIZE.HEADING_3,
  color: Typography.TEXT_COLOR.PRIMARY,
  textAlign: Typography.TEXT_ALIGN.CENTER,
};

<Text style={customStyle}>Custom styled text</Text>
```

### Using in StyleSheet

You can use the typography constants in your StyleSheet:

```javascript
const styles = StyleSheet.create({
  title: {
    ...Typography.STYLES.H2,
    marginBottom: 10,
  },
  description: {
    ...Typography.STYLES.BODY_REGULAR,
    color: Typography.TEXT_COLOR.DARK_GRAY,
  },
});
```

## Available Constants

### Font Sizes

```javascript
Typography.FONT_SIZE.HEADING_1  // 35
Typography.FONT_SIZE.HEADING_2  // 29
Typography.FONT_SIZE.HEADING_3  // 24
Typography.FONT_SIZE.HEADING_4  // 20
Typography.FONT_SIZE.BODY_LARGE  // 19
Typography.FONT_SIZE.BODY_MEDIUM  // 16
Typography.FONT_SIZE.BODY_REGULAR  // 14
Typography.FONT_SIZE.BODY_SMALL  // 13
Typography.FONT_SIZE.CAPTION  // 12
Typography.FONT_SIZE.TINY  // 10
Typography.FONT_SIZE.MICRO  // 8
```

### Font Families

```javascript
Typography.FONT_FAMILY.EXO.THIN
Typography.FONT_FAMILY.EXO.EXTRA_LIGHT
Typography.FONT_FAMILY.EXO.LIGHT
Typography.FONT_FAMILY.EXO.REGULAR
Typography.FONT_FAMILY.EXO.MEDIUM
Typography.FONT_FAMILY.EXO.SEMI_BOLD
Typography.FONT_FAMILY.EXO.BOLD
Typography.FONT_FAMILY.EXO.EXTRA_BOLD
Typography.FONT_FAMILY.EXO.BLACK
Typography.FONT_FAMILY.EXO.MEDIUM_ITALIC
Typography.FONT_FAMILY.EXO.SEMI_BOLD_ITALIC
Typography.FONT_FAMILY.EXO.BOLD_ITALIC

Typography.FONT_FAMILY.LEXEND.THIN
Typography.FONT_FAMILY.LEXEND.EXTRA_LIGHT
Typography.FONT_FAMILY.LEXEND.LIGHT
Typography.FONT_FAMILY.LEXEND.REGULAR
Typography.FONT_FAMILY.LEXEND.MEDIUM
Typography.FONT_FAMILY.LEXEND.SEMI_BOLD
Typography.FONT_FAMILY.LEXEND.BOLD
Typography.FONT_FAMILY.LEXEND.EXTRA_BOLD
Typography.FONT_FAMILY.LEXEND.BLACK
```

### Text Colors

```javascript
Typography.TEXT_COLOR.PRIMARY  // primaryGreen
Typography.TEXT_COLOR.SECONDARY  // primaryPurple
Typography.TEXT_COLOR.BLACK
Typography.TEXT_COLOR.WHITE
Typography.TEXT_COLOR.GRAY
Typography.TEXT_COLOR.DARK_GRAY
Typography.TEXT_COLOR.LIGHT_GRAY
Typography.TEXT_COLOR.ERROR  // red
```

### Complete Styles

See the `Typography.STYLES` object for all available pre-defined styles.

## Example

See the `examples/TypographyExample.js` file for a complete example of how to use the typography constants.
