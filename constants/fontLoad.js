import {
    useFonts,
    Exo_100Thin,
    Exo_200ExtraLight,
    Exo_300Light,
    Exo_400Regular,
    Exo_500Medium,
    Exo_600SemiBold,
    Exo_700Bold,
    Exo_800ExtraBold,
    Exo_900Black,
    Exo_400Regular_Italic,
    Exo_500Medium_Italic,
    Exo_600SemiBold_Italic,
    Exo_700Bold_Italic,
} from '@expo-google-fonts/exo';

import {
    Lexend_100Thin,
    Lexend_200ExtraLight,
    Lexend_300Light,
    Lexend_400Regular,
    Lexend_500Medium,
    Lexend_600SemiBold,
    Lexend_700Bold,
    Lexend_800ExtraBold,
    Lexend_900Black,

} from '@expo-google-fonts/lexend';




export const FontLoad = () => {
    const [fontsLoaded] = useFonts({
        Exo_100Thin,
        Exo_200ExtraLight,
        Exo_300Light,
        Exo_400Regular,
        Exo_500Medium,
        Exo_600SemiBold,
        Exo_700Bold,
        Exo_800ExtraBold,
        Exo_900Black,
        Exo_400Regular_Italic,
        Exo_500Medium_Italic,
        Exo_600SemiBold_Italic,
        Exo_700Bold_Italic,
        Lexend_100Thin,
        Lexend_200ExtraLight,
        Lexend_300Light,
        Lexend_400Regular,
        Lexend_500Medium,
        Lexend_600SemiBold,
        Lexend_700Bold,
        Lexend_800ExtraBold,
        Lexend_900Black,
    });

    return fontsLoaded;
};
