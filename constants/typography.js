// typography.js
import { ThemeFonts } from './theme/fonts';
import { Colors } from './theme/colors';

/**
 * Typography constants for consistent text styling across the app
 * Use these constants for all text styling to maintain consistency
 */
export const Typography = {
    // Font Sizes
    FONT_SIZE: {
        HEADING_1: 35,
        HEADING_2: 29,
        HEADING_3: 24,
        HEADING_4: 20,
        BODY_LARGE: 19,
        BODY_MEDIUM: 16,
        BODY_REGULAR: 14,
        BODY_SMALL: 13,
        CAPTION: 12,
        TINY: 10,
        MICRO: 8,
    },

    // Line Heights
    LINE_HEIGHT: {
        TIGHT: 1.2,
        NORMAL: 1.5,
        LOOSE: 1.8,
    },

    // Letter Spacing
    LETTER_SPACING: {
        TIGHT: -0.5,
        NORMAL: 0,
        WIDE: 0.5,
    },

    // Text Transforms
    TEXT_TRANSFORM: {
        NONE: 'none',
        UPPERCASE: 'uppercase',
        LOWERCASE: 'lowercase',
        CAPITALIZE: 'capitalize',
    },

    // Text Decorations
    TEXT_DECORATION: {
        NONE: 'none',
        UNDERLINE: 'underline',
        LINE_THROUGH: 'line-through',
    },

    // Text Alignments
    TEXT_ALIGN: {
        AUTO: 'auto',
        LEFT: 'left',
        RIGHT: 'right',
        CENTER: 'center',
        JUSTIFY: 'justify',
    },

    // Font Families
    FONT_FAMILY: {
        EXO: {
            THIN: ThemeFonts.Exo_100,
            EXTRA_LIGHT: ThemeFonts.Exo_200,
            LIGHT: ThemeFonts.Exo_300,
            REGULAR: ThemeFonts.Exo_400,
            MEDIUM: ThemeFonts.Exo_500,
            SEMI_BOLD: ThemeFonts.Exo_600,
            BOLD: ThemeFonts.Exo_700,
            EXTRA_BOLD: ThemeFonts.Exo_800,
            BLACK: ThemeFonts.Exo_900,
            MEDIUM_ITALIC: ThemeFonts.Exo_500_Italic,
            SEMI_BOLD_ITALIC: ThemeFonts.Exo_600_Italic,
            BOLD_ITALIC: ThemeFonts.Exo_700_Italic,
        },
        LEXEND: {
            THIN: ThemeFonts.Lexend_100,
            EXTRA_LIGHT: ThemeFonts.Lexend_200,
            LIGHT: ThemeFonts.Lexend_300,
            REGULAR: ThemeFonts.Lexend_400,
            MEDIUM: ThemeFonts.Lexend_500,
            SEMI_BOLD: ThemeFonts.Lexend_600,
            BOLD: ThemeFonts.Lexend_700,
            EXTRA_BOLD: ThemeFonts.Lexend_800,
            BLACK: ThemeFonts.Lexend_900,
        },
    },

    // Text Colors
    TEXT_COLOR: {
        PRIMARY: Colors.primaryGreen,
        SECONDARY: Colors.primaryPurple,
        BLACK: Colors.black,
        WHITE: Colors.white,
        GRAY: Colors.gray,
        DARK_GRAY: Colors.darkGray,
        LIGHT_GRAY: Colors.lightGray,
        ERROR: Colors.red,
    },

    // Complete Text Styles
    STYLES: {
        // Headings
        H1: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 35,
            color: Colors.black,
            lineHeight: 42,
        },
        H2: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 29,
            color: Colors.black,
            lineHeight: 35,
        },
        H3: {
            fontFamily: ThemeFonts.Exo_600,
            fontSize: 24,
            color: Colors.black,
            lineHeight: 29,
        },
        H4: {
            fontFamily: ThemeFonts.Exo_600,
            fontSize: 20,
            color: Colors.black,
            lineHeight: 24,
        },

        // Body Text
        BODY_LARGE_BOLD: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 19,
            color: Colors.black,
            lineHeight: 24,
        },
        BODY_LARGE_REGULAR: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 19,
            color: Colors.black,
            lineHeight: 24,
        },
        BODY_MEDIUM_BOLD: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 16,
            color: Colors.black,
            lineHeight: 22,
        },
        BODY_MEDIUM_REGULAR: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 16,
            color: Colors.black,
            lineHeight: 22,
        },
        BODY_REGULAR_BOLD: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 14,
            color: Colors.black,
            lineHeight: 20,
        },
        BODY_REGULAR: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 14,
            color: Colors.black,
            lineHeight: 20,
        },
        BODY_SMALL_BOLD: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 13,
            color: Colors.black,
            lineHeight: 18,
        },
        BODY_SMALL_REGULAR: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 13,
            color: Colors.black,
            lineHeight: 18,
        },

        // Captions and Small Text
        CAPTION_BOLD: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 12,
            color: Colors.darkGray,
            lineHeight: 16,
        },
        CAPTION_REGULAR: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 12,
            color: Colors.darkGray,
            lineHeight: 16,
        },
        TINY_BOLD: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 10,
            color: Colors.darkGray,
            lineHeight: 14,
        },
        TINY_REGULAR: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 10,
            color: Colors.darkGray,
            lineHeight: 14,
        },

        // Special Styles
        BUTTON_TEXT: {
            fontFamily: ThemeFonts.Exo_600,
            fontSize: 16,
            color: Colors.white,
            textAlign: 'center',
        },
        LINK_TEXT: {
            fontFamily: ThemeFonts.Exo_500,
            fontSize: 14,
            color: Colors.primaryGreen,
            textDecorationLine: 'underline',
        },
        ERROR_TEXT: {
            fontFamily: ThemeFonts.Exo_500,
            fontSize: 12,
            color: Colors.red,
        },
        SUCCESS_TEXT: {
            fontFamily: ThemeFonts.Exo_500,
            fontSize: 12,
            color: Colors.primaryGreen,
        },

        // Legacy styles (for backward compatibility)
        heading1Bold: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 35,
            color: Colors.black,
        },
        heading2Bold: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 29,
            color: Colors.black,
        },
        bodyLargeRegular: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 19,
            color: Colors.black,
        },
        bodyMediumRegular: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 13,
            color: Colors.black,
        },
        bodyMediumBold: {
            fontFamily: ThemeFonts.Exo_700,
            fontSize: 13,
            color: Colors.black,
        },
        bodySmallRegular: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 10,
            color: Colors.black,
        },
        captionRegular: {
            fontFamily: ThemeFonts.Exo_400,
            fontSize: 6,
            color: Colors.black,
        },
    },
};