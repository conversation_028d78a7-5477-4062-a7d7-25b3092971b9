export const physicalOptions = [
    { label: "Lose 5 kg", value: "lose5" },
    { label: "Lose 10 kg", value: "lose10" },
    { label: "Lose 20 kg or more", value: "lose20" },
    // { label: "Custom bodyweight goals", value: "custom" },
];

export const movementOptions = [
    { label: "Burn 100kcal", value: "burn100" },
    { label: "Burn 500kcal", value: "burn500" },
    { label: "Burn more than 500kcal", value: "burnMore" },
];

export const getMovementCalories = (value) => {
    switch (value) {
        case "burn100":
            return 100;
        case "burn500":
            return 500;
        case "burnMore":
            return "500+";
        default:
            return 0;
    }
};

export const mindfulnessOptions = [
    { label: "Avoiding Distractions", value: "distractions" },
    { label: "Practice mindful eating", value: "mindfulEating" },
    { label: "Balancing food & activity", value: "balance" },
];

export const sleepOptions = [
    { label: "6 hours", value: "6" },
    { label: "7 hours", value: "7" },
    { label: "8 hours", value: "8" },
];

export const deviceUsageLimitOptions = [
    { label: "No device", value: "null" },
    { label: "2 hours", value: 2 },
    { label: "2.5 hours", value: 2.5 },
    { label: "3 hours", value: 3 },
];

export const dietOptions = [
    { label: "Vegan", value: "vegan" },
    { label: "Vegetarian", value: "veg" },
    { label: "Non-vegetarian", value: "non-veg" },
    { label: "Other", value: "others" },
];

export const devices = [
    { id: "000001", name: "Belt", version: "v01" },
    { id: "000002", name: "Belt", version: "v02" },
    { id: "000003", name: "Mark", version: "v01" },
];