
export const CATEGORIES = [
    { label: 'Food', value: 'food' },
    { label: 'Workout', value: 'workout' },
    { label: 'Device', value: 'device' },
];

export const TIME_SLOTS = [
    { label: "12:00 PM", value: "12:00 PM" },
    { label: "06:00 PM", value: "06:00 PM" },
    { label: "00:00 AM", value: "00:00 AM" },
    { label: "06:00 AM", value: "06:00 AM" }
];

export const NOTIFICATION_SOUNDS = [
    { label: 'Default', value: 'default' },
    { label: 'Sound 1', value: 'sound1' },
    { label: 'Sound 2', value: 'sound2' },
    { label: 'Sound 3', value: 'sound3' },
    { label: 'Sound 4', value: 'sound4' },
];

export const notificationSoundMap = {
    default: {
        ios: "default",
        android: "default"
    },
    sound1: {
        ios: "sound1.wav",
        android: "sound1"
    },
    sound2: {
        ios: "sound2.wav",
        android: "sound2"
    },
    sound3: {
        ios: "sound3.wav",
        android: "sound3"
    },
    sound4: {
        ios: "sound4.wav",
        android: "sound4"
    }
};

export const TIMER_DURATION_OPTIONS = [
    { label: "1 min", value: 1 },
    { label: "3 mins", value: 3 },
    { label: "5 mins", value: 5 },
    { label: "7 mins", value: 7 },
];

export const WEIGH_UNIT_OPTIONS = [
    { label: "LBS", value: 'lbs' },
    { label: "KG", value: 'kg' }
];

export const MOOD_OPTIONS = [
    {
        value: "happy",
        label: "Happy",
        borderIcon: require("assets/icons/moods/happy.png"),
        filledIcon: require("assets/icons/moods/happy_filled.png"),
        quote: "Happiness is a choice. Maintain this positive energy!"
    },
    {
        value: "moderately happy",
        label: "Moderate",
        borderIcon: require("assets/icons/moods/moderate_happy.png"),
        filledIcon: require("assets/icons/moods/moderate_happy_filled.png"),
        quote: "You're doing well! Keep building on this balanced state."
    },
    {
        value: "irritated",
        label: "Irritated",
        borderIcon: require("assets/icons/moods/neutral.png"),
        filledIcon: require("assets/icons/moods/neutral_filled.png"),
        quote: "Take a deep breath. Small irritations will pass with time."
    },
    {
        value: "anxious",
        label: "Anxious",
        borderIcon: require("assets/icons/moods/angry.png"),
        filledIcon: require("assets/icons/moods/angry_filled.png"),
        quote: "Focus on what you can control. This feeling is temporary."
    },
    {
        value: "sad",
        label: "Sad",
        borderIcon: require("assets/icons/moods/sad.png"),
        filledIcon: require("assets/icons/moods/sad_filled.png"),
        quote: "It's okay to feel down sometimes. Reach out if you need support."
    }
];

export function getKeyForSound(iosSoundName, androidSoundName) {
    for (let key in notificationSoundMap) {
        if (notificationSoundMap[key].ios === iosSoundName && notificationSoundMap[key].android === androidSoundName) {
            return key;
        }
    }
    return "default";
}