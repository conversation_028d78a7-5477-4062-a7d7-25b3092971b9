import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Device } from 'models/device';
import { isValidObjectId, Model } from 'mongoose';
import { EmailService, UtilsService } from 'src/common/services';
import { getAllDeviceQueryInterface } from './interfaces';
import { AwsS3Service } from 'src/third-party/aws';
import {
  GetAllDeviceResDTO,
  DeleteDeviceResDTO,
  GetSingleDeviceResDTO,
  DeviceDTO,
  CreateDeviceResDTO,
  CreateDeviceReqDTO,
  UpdateDeviceReqDTO,
  UpdateDeviceResDTO,
  CreateBulkDevicesResDTO,
} from './dto';
import { AdminCreateDeviceUtilsService } from './admin-add-device-utils.service';
import { User } from 'models/user';

@Injectable()
export class AdminDeviceService {
  constructor(
    @InjectModel(Device.name) private DeviceModel: Model<Device>,
    private readonly utilsService: UtilsService,
    private readonly s3Service: AwsS3Service,
    private readonly createDeviceUtilsService: AdminCreateDeviceUtilsService,
    private readonly emailService: EmailService,
  ) {}

  async getAllDevices(
    queryFilters: getAllDeviceQueryInterface,
  ): Promise<GetAllDeviceResDTO> {
    const { page, name } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };

    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }

    const Devices = await this.DeviceModel.find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.DeviceModel.countDocuments(query);

    const DeviceResp = Devices.map((item) => DeviceDTO.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: DeviceResp.length,
      devices: DeviceResp,
    };
  }

  async getSingleDevice(id: string): Promise<GetSingleDeviceResDTO> {
    if (!isValidObjectId(id)) {
      throw new BadRequestException('Invalid Device ID format');
    }

    const Device = await this.DeviceModel.findById(id).exec();

    if (!Device || Device.isDeleted) {
      throw new NotFoundException('Device not found');
    }

    const resp = DeviceDTO.transform(Device);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      device: resp,
    };
  }

  async createSingleDevice(
    DeviceData: CreateDeviceReqDTO,
    thumbnailFile: Express.Multer.File,
  ): Promise<CreateDeviceResDTO> {
    if (!thumbnailFile) {
      throw new BadRequestException('Please provide thumbnail !!');
    }

    const { name, description, deviceGuide, version, type, serialIds } =
      DeviceData;

    const createData: any = {
      name,
      description,
      deviceGuide,
      version,
      type,
      serialIds: serialIds.split(','),
    };

    const existingDevice = await this.DeviceModel.findOne({
      name: { $regex: new RegExp(`^${name}$`, 'i') }, // Ensures an exact case-insensitive match
      version,
      isDeleted: false,
    });

    if (existingDevice) {
      throw new BadRequestException(
        'Device with this name and version already exists !!',
      );
    }

    const thumbnailUrl = (await this.s3Service.uploadFile(thumbnailFile))
      .Location;
    createData.thumbnailUrl = thumbnailUrl;

    const newDevice = await this.DeviceModel.create(createData);

    const resp = DeviceDTO.transform(newDevice);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Device created successfully !!',
      device: resp,
    };
  }

  async updateDevice(
    id: string,
    updateBody: UpdateDeviceReqDTO,
    thumbnailFile: Express.Multer.File,
  ): Promise<UpdateDeviceResDTO> {
    const { description, deviceGuide, serialIds } = updateBody;

    const Device = await this.DeviceModel.findById(id);

    if (!Device || Device.isDeleted) {
      throw new NotFoundException('Device not found');
    }

    const updateData: any = {};

    if (description) updateData.description = description;
    if (deviceGuide) updateData.deviceGuide = deviceGuide;
    if (serialIds) updateData.serialIds = serialIds.split(',');

    if (thumbnailFile) {
      updateData.thumbnailUrl = (
        await this.s3Service.uploadFile(thumbnailFile)
      ).Location;
    }

    const updatedDevice = await this.DeviceModel.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true },
    ).exec();

    const resp = DeviceDTO.transform(updatedDevice);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Updated Device Successfully !!',
      device: resp,
    };
  }

  async deleteDevice(id: string): Promise<DeleteDeviceResDTO> {
    const Device = await this.DeviceModel.findById(id);

    if (!Device || Device.isDeleted) {
      throw new NotFoundException('Device not found');
    }

    const deletedDevice = await this.DeviceModel.findByIdAndUpdate(
      id,
      { isDeleted: true },
      { new: true, runValidators: true },
    ).exec();

    const resp = DeviceDTO.transform(deletedDevice);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Device deleted successfully',
      device: resp,
    };
  }

  async createBulkDevices(
    csvFile: Express.Multer.File,
    user: User,
  ): Promise<CreateBulkDevicesResDTO> {
    if (!csvFile) {
      throw new BadRequestException('Please provide the CSV file to upload.');
    }

    const uncompiledDevices: { row: any; reason: string }[] = [];
    const compiledDevices: { Device: Device; status: string }[] = [];

    const rows = await this.createDeviceUtilsService.parseCSV(csvFile);

    for (const row of rows) {
      try {
        const DeviceData =
          this.createDeviceUtilsService.extractDeviceDetails(row);

        const existingDevice = await this.DeviceModel.findOne({
          name: DeviceData.name,
          version: DeviceData.version,
        });

        if (existingDevice) {
          // Update existing Device
          existingDevice.description = DeviceData.description;
          existingDevice.deviceGuide = DeviceData.deviceGuide;
          existingDevice.serialIds = DeviceData.serialIds;
          existingDevice.thumbnailUrl = DeviceData.thumbnailUrl;

          await existingDevice.save();
          compiledDevices.push({ Device: existingDevice, status: 'UPDATED' });
        } else {
          // Create new Device
          const newDevice =
            this.createDeviceUtilsService.buildDeviceData(DeviceData);

          const savedDevice = await this.DeviceModel.create(newDevice);

          compiledDevices.push({ Device: savedDevice, status: 'CREATED' });
        }
      } catch (error) {
        uncompiledDevices.push({
          row,
          reason: error.message || 'Invalid Device data.',
        });
      }
    }

    await this.createDeviceUtilsService.sendReport(
      compiledDevices,
      uncompiledDevices,
      user,
    );

    const createdCount = compiledDevices.filter(
      (r) => r.status === 'CREATED',
    ).length;

    const updatedCount = compiledDevices.filter(
      (r) => r.status === 'UPDATED',
    ).length;

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: `${createdCount > 0 ? createdCount + ' Devices created. ' : ''}${updatedCount > 0 ? updatedCount + ' Devices updated. ' : ''}${uncompiledDevices.length > 0 ? `Unable to add ${uncompiledDevices.length} Devices. ` : ''}${uncompiledDevices.length > 0 ? 'Report has been sent to you via email.' : ''}`,
    };
  }
}
