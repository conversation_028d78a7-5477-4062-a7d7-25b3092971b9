import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { Authority } from 'src/utils/decorators';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminDeviceService } from './admin-device.service';
import {
  CreateDeviceReqDTO,
  CreateDeviceResDTO,
  DeleteDeviceResDTO,
  GetAllDeviceResDTO,
  GetSingleDeviceResDTO,
  UpdateDeviceReqDTO,
  UpdateDeviceResDTO,
} from './dto';
import { ErrorResponse } from 'src/utils/responses';
import { AuthGuard } from 'src/middlewares';
import { getAllDeviceQueryInterface } from './interfaces';
import {
  AllowedImageExtensions,
  getMulterMediaOptions,
} from 'src/utils/multer';
import { FileInterceptor } from '@nestjs/platform-express';
import { getMulterCSVOptions } from 'src/utils/multer/multer.utils';
import { Request } from 'express';

@ApiTags('Admin-Devices')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('/admin/devices')
export class AdminDeviceController {
  constructor(private readonly adminDeviceService: AdminDeviceService) {}

  // Get All Device
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the Device .',
    type: GetAllDeviceResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    description: 'The page number for pagination (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @ApiQuery({
    name: 'page',
    description: 'The title for searching (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @Authority()
  @Get()
  async getAllDevices(@Query() filterQueries: getAllDeviceQueryInterface) {
    return this.adminDeviceService.getAllDevices(filterQueries);
  }

  // Get Single Device
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the Device .',
    type: GetSingleDeviceResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Get('/:DeviceId')
  async getSingleDevice(@Param('DeviceId') id: string) {
    return this.adminDeviceService.getSingleDevice(id);
  }

  // Create Device
  @ApiResponse({
    status: 201,
    description: 'Successfully created Device .',
    type: CreateDeviceResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @UseInterceptors(
    FileInterceptor(
      'thumbnailFile',
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  @Authority()
  @Post()
  async createDevice(
    @Body() createBody: CreateDeviceReqDTO,
    @UploadedFile() thumbnailFile: Express.Multer.File,
  ) {
    return this.adminDeviceService.createSingleDevice(
      createBody,
      thumbnailFile,
    );
  }

  // Update Device
  @ApiResponse({
    status: 200,
    description: 'Successfully updated the Device .',
    type: UpdateDeviceResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseInterceptors(
    FileInterceptor(
      'thumbnailFile',
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  @Authority()
  @Put('/:DeviceId')
  async updateDevice(
    @Param('DeviceId') id: string,
    @Body() updateBody: UpdateDeviceReqDTO,
    @UploadedFile() thumbnailFile: Express.Multer.File,
  ) {
    return this.adminDeviceService.updateDevice(id, updateBody, thumbnailFile);
  }

  // Delete Device
  @ApiResponse({
    status: 200,
    description: 'Successfully deleted the Device .',
    type: DeleteDeviceResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Delete('/:DeviceId')
  async deleteDevice(@Param('DeviceId') id: string) {
    return this.adminDeviceService.deleteDevice(id);
  }

  // Create Device
  @ApiResponse({
    status: 201,
    description: 'Successfully created Device .',
    type: CreateDeviceResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @UseInterceptors(
    FileInterceptor('csvFile', getMulterCSVOptions({ fileSize: 50 })),
  )
  @Authority()
  @Post('/bulk')
  async createBulkDevices(
    @Req() req: Request,
    @UploadedFile() csvFile: Express.Multer.File,
  ) {
    const user = req['user'];
    return this.adminDeviceService.createBulkDevices(csvFile, user);
  }
}
