import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsEnum, IsN<PERSON>ber, IsNotEmpty, Min } from 'class-validator';
import { DEVICE_NAMES, DEVICE_TYPES } from 'models/device';

export class CreateDeviceReqDTO {
  @ApiProperty({ enum: DEVICE_NAMES, description: 'Name of the device' })
  @IsEnum(DEVICE_NAMES)
  @IsNotEmpty()
  name: DEVICE_NAMES;

  @ApiProperty({ description: 'Description of the device' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ description: 'Device guide URL' })
  @IsString()
  @IsNotEmpty()
  deviceGuide: string;

  @ApiProperty({ description: 'Version of the device' })
  @Min(0)
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  version: number;

  @ApiProperty({ enum: DEVICE_TYPES, description: 'Type of the device' })
  @IsEnum(DEVICE_TYPES)
  @IsNotEmpty()
  type: DEVICE_TYPES;

  @ApiProperty({
    description: 'Serial IDs related to the device',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  serialIds: string;

  @ApiProperty({
    description: 'Thumbnail file for the device',
    type: 'string',
    format: 'binary',
  })
  thumbnailFile: Express.Multer.File;
}
