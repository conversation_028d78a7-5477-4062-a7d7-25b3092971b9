import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsUrl } from 'class-validator';

export class UpdateDeviceReqDTO {
  @ApiPropertyOptional({ description: 'Description of the device' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Device guide URL' })
  @IsOptional()
  @IsString()
  deviceGuide?: string;

  @ApiPropertyOptional({ description: 'Thumbnail image URL' })
  @IsOptional()
  @IsString()
  @IsUrl()
  thumbnailUrl?: string;

  @ApiPropertyOptional({
    description: 'List of serial IDs associated with the device',
    type: String,
  })
  @IsString()
  @IsOptional()
  serialIds?: string;
}
