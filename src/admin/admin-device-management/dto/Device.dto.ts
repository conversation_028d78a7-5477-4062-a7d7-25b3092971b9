import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsUrl, IsArray, IsDate } from 'class-validator';
import { Device, DEVICE_NAMES, DEVICE_TYPES } from 'models/device';

export class DeviceDTO {
  @ApiProperty({ description: 'Unique identifier for the device' })
  @IsString()
  id: string;

  @ApiProperty({ enum: DEVICE_NAMES, description: 'Name of the device' })
  @IsEnum(DEVICE_NAMES)
  name: DEVICE_NAMES;

  @ApiProperty({ description: 'Description of the device' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Device guide URL' })
  @IsString()
  deviceGuide: string;

  @ApiProperty({ description: 'Version of the device' })
  @IsString()
  version: string;

  @ApiProperty({ enum: DEVICE_TYPES, description: 'Type of the device' })
  @IsEnum(DEVICE_TYPES)
  type: DEVICE_TYPES;

  @ApiProperty({ description: 'Thumbnail image URL of the device' })
  @IsUrl()
  thumbnailUrl: string;

  @ApiProperty({
    description: 'Serial IDs related to the device',
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  serialIds: string[];

  @ApiProperty({ description: 'Creation timestamp of the device' })
  @IsDate()
  createdAt: Date;

  static transform(object: Device): DeviceDTO {
    const transformedObj = new DeviceDTO();
    transformedObj.id = object._id.toString();
    transformedObj.name = object.name;
    transformedObj.description = object.description;
    transformedObj.deviceGuide = object.deviceGuide;
    transformedObj.version = object.version;
    transformedObj.type = object.type;
    transformedObj.thumbnailUrl = object.thumbnailUrl;
    transformedObj.serialIds = object.serialIds;
    transformedObj.createdAt = (object as any).createdAt;
    return transformedObj;
  }
}
