import { Injectable } from '@nestjs/common';
import {
  CustomConfigService,
  CustomLogger,
  EmailService,
} from 'src/common/services';
import { AwsS3Service } from 'src/third-party/aws';

import * as stream from 'stream';
import * as csvParser from 'csv-parser';
import { v4 as uuid } from 'uuid';
import { Parser } from 'json2csv';
import { Device } from 'models/device';
import { User } from 'models/user';

@Injectable()
export class AdminCreateDeviceUtilsService {
  constructor(
    private readonly s3Service: AwsS3Service,
    private readonly logger: CustomLogger,
    private readonly emailService: EmailService,
  ) {}

  buildDeviceData(DeviceData: any): any {
    return {
      name: DeviceData.name,
      description: DeviceData.description,
      deviceGuide: DeviceData.deviceGuide,
      version: DeviceData.version,
      type: DeviceData.type,
      thumbnailUrl: DeviceData.thumbnailUrl,
      serialIds: Array.isArray(DeviceData.serialIds)
        ? DeviceData.serialIds
        : DeviceData.serialIds.split(','),
    };
  }

  async uploadThumbnail(thumbnailFile: Express.Multer.File): Promise<string> {
    return (await this.s3Service.uploadFile(thumbnailFile)).Location;
  }

  // ---------------------------------------------------------------------------
  extractDeviceDetails(row: any) {
    return {
      name: row.name,
      description: row.description,
      deviceGuide: row.deviceGuide,
      version: row.version,
      type: row.type,
      serialIds: row.serialIds.split(','),
      thumbnailUrl: row.thumbnailUrl,
    };
  }

  async sendReport(
    compiledDevices: { Device: Device; status: string }[],
    uncompiledDevices: { row: any; reason: string }[],
    user: User,
  ) {
    if (uncompiledDevices.length > 0) {
      const csvParser = new Parser({ fields: ['row', 'reason'] });
      const csvData = csvParser.parse(uncompiledDevices);
      const csvBuffer = Buffer.from(csvData);

      const csvFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: `uncompiled_Devices_${uuid()}.csv`,
        encoding: '7bit',
        mimetype: 'text/csv',
        buffer: csvBuffer,
        size: csvBuffer.length,
        stream: null,
        destination: '',
        filename: '',
        path: '',
      };

      const url = (await this.s3Service.uploadFile(csvFile)).Location;

      const { subject, emailBody, fromEmail, AdminCSVReportEmailTemplate } =
        CustomConfigService.PROPERTIES.BulkDevicesCreationReportEmail;

      const html = AdminCSVReportEmailTemplate.replace('$$reportUrl', url);

      await this.emailService.sendTextMail({
        fromEmail,
        toEmail: user.email,
        subject,
        textBody: emailBody,
        html,
      });
    }
  }

  async parseCSV(csvFile: Express.Multer.File): Promise<any[]> {
    const bufferStream = new stream.PassThrough();
    bufferStream.end(csvFile.buffer);

    return new Promise((resolve, reject) => {
      const rows: any[] = [];
      bufferStream
        .pipe(csvParser())
        .on('data', (row) => rows.push(row))
        .on('end', () => resolve(rows))
        .on('error', reject);
    });
  }
}
