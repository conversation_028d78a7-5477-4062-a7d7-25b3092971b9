import { Injectable, BadRequestException } from '@nestjs/common';
import { PIECE_QUANTITY_TYPE, STANDARD_QUANTITY_TYPE } from 'models/ingredient';
import { AwsS3Service } from 'src/third-party/aws';

@Injectable()
export class AdminIngredientUtilsService {
  constructor(private readonly s3Service: AwsS3Service) {}

  async uploadThumbnail(thumbnailFile: Express.Multer.File): Promise<string> {
    return (await this.s3Service.uploadFile(thumbnailFile)).Location;
  }

  validateNutritionByQuantity(nutritionList: any[]): void {
    const standardTypes = Object.values(STANDARD_QUANTITY_TYPE);
    const pieceTypes = Object.values(PIECE_QUANTITY_TYPE);

    const standardProvided = new Set<string>();
    const pieceProvided = new Set<string>();

    for (const item of nutritionList) {
      if (standardTypes.includes(item.quantity)) {
        standardProvided.add(item.quantity);
      } else if (pieceTypes.includes(item.quantity)) {
        pieceProvided.add(item.quantity);
      } else {
        throw new BadRequestException(
          `Invalid quantity type: ${item.quantity}`,
        );
      }

      const keys = ['protein', 'calories', 'fats', 'fiber', 'carbs'];
      for (const key of keys) {
        if (typeof item[key] !== 'number' || item[key] < 0) {
          throw new BadRequestException(
            `Invalid value for ${key} in quantity ${item.quantity}`,
          );
        }
      }
    }

    if (standardProvided.size > 0 && standardProvided.size < 3) {
      throw new BadRequestException(
        `All three standard types (${standardTypes.join(', ')}) are required.`,
      );
    }

    if (pieceProvided.size > 0 && pieceProvided.size < 4) {
      throw new BadRequestException(
        `All four piece types (${pieceTypes.join(', ')}) are required.`,
      );
    }
  }
}
