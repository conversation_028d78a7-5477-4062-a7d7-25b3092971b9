import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { Ingredient } from 'models/ingredient/ingredient.schema';
import { IngredientNutritionByQuantityDTO } from './ingredientNutritionByQuantity.dto';

export class IngredientDTO {
  @ApiProperty({ description: 'Unique identifier of the ingredient' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Ingredient name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Thumbnail URL of the ingredient',
    required: false,
  })
  @IsString()
  thumbnailUrl: string;

  @ApiProperty({
    description: 'Nutrition info based on quantity',
    type: [IngredientNutritionByQuantityDTO],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IngredientNutritionByQuantityDTO)
  ingredientNutritionByQuantity: IngredientNutritionByQuantityDTO[];

  createdAt: Date;
  updatedAt: Date;

  static transform(object: Ingredient): IngredientDTO {
    const transformed = new IngredientDTO();
    transformed.id = object._id.toString();
    transformed.name = object.name;
    transformed.thumbnailUrl = object.thumbnailUrl;
    transformed.ingredientNutritionByQuantity =
      object.ingredientNutritionByQuantity.map(
        IngredientNutritionByQuantityDTO.transform,
      );
    transformed.createdAt = object.createdAt;
    transformed.updatedAt = object.updatedAt;
    return transformed;
  }
}
