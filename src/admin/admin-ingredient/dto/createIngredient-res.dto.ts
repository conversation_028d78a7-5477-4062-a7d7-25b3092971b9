import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { IngredientDTO } from './ingredient.dto';

export class CreateIngredientResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Success message',
    example: 'Ingredient created successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Created ingredient details',
    type: IngredientDTO,
  })
  data: IngredientDTO;
}
