import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { IngredientDTO } from './ingredient.dto';

export class GetAllIngredientResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of ingredients available',
    example: 200,
  })
  total: number;

  @ApiProperty({
    description: 'Number of ingredients returned in the current request',
    example: 50,
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of ingredients retrieved in the request',
    type: [IngredientDTO],
  })
  data: IngredientDTO[];
}
