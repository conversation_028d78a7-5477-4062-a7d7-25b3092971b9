import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { IngredientNutritionByQuantityDTO } from './ingredientNutritionByQuantity.dto';

export class UpdateIngredientReqDTO {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ type: [IngredientNutritionByQuantityDTO] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IngredientNutritionByQuantityDTO)
  ingredientNutritionByQuantity?: IngredientNutritionByQuantityDTO[];

  @ApiPropertyOptional({
    type: [String],
    description: 'List of thumbnail URLs',
  })
  @IsOptional()
  @IsArray()
  thumbnailUrls?: string[];
}
