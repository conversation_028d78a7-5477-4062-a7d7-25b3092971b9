import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsN<PERSON><PERSON> } from 'class-validator';
import {
  PIECE_QUANTITY_TYPE,
  STANDARD_QUANTITY_TYPE,
  IngredientNutritionByQuantity,
} from 'models/ingredient/ingredient_nutrition_by_quantity.schema';
type NUTRITION_QUANTITY = STANDARD_QUANTITY_TYPE | PIECE_QUANTITY_TYPE;

export class IngredientNutritionByQuantityDTO {
  @ApiProperty({
    description: 'Quantity size',
    enum: [
      ...Object.values(STANDARD_QUANTITY_TYPE),
      ...Object.values(PIECE_QUANTITY_TYPE),
    ],
    example: STANDARD_QUANTITY_TYPE.MEDIUM,
  })
  @IsEnum(STANDARD_QUANTITY_TYPE || PIECE_QUANTITY_TYPE)
  @IsNotEmpty()
  quantity: NUTRITION_QUANTITY;

  @ApiProperty({ description: 'Amount of protein in grams', example: 25 })
  @IsNumber()
  @IsNotEmpty()
  protein: number;

  @ApiProperty({ description: 'Amount of calories in kcal', example: 200 })
  @IsNumber()
  @IsNotEmpty()
  calories: number;

  @ApiProperty({ description: 'Amount of fats in grams', example: 10 })
  @IsNumber()
  @IsNotEmpty()
  fats: number;

  @ApiProperty({ description: 'Amount of fiber in grams', example: 5 })
  @IsNumber()
  @IsNotEmpty()
  fiber: number;

  @ApiProperty({ description: 'Amount of carbohydrates in grams', example: 50 })
  @IsNumber()
  @IsNotEmpty()
  carbs: number;

  static transform(
    object: IngredientNutritionByQuantity,
  ): IngredientNutritionByQuantityDTO {
    const transformed = new IngredientNutritionByQuantityDTO();
    transformed.quantity = object.quantity;
    transformed.protein = object.protein;
    transformed.calories = object.calories;
    transformed.fats = object.fats;
    transformed.fiber = object.fiber;
    transformed.carbs = object.carbs;
    return transformed;
  }
}
