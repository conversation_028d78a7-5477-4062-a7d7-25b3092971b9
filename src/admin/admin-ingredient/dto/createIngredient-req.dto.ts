import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsNotEmpty, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { IngredientNutritionByQuantityDTO } from './ingredientNutritionByQuantity.dto';

export class CreateIngredientReqDTO {
  @ApiProperty({ description: 'Name of the ingredient', example: 'Tomato' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Thumbnail URL for the ingredient',
    example: 'https://example.com/images/tomato.jpg',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  thumbnailUrl: string;

  @ApiProperty({
    description: 'Nutritional info per quantity',
    type: [IngredientNutritionByQuantityDTO],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IngredientNutritionByQuantityDTO)
  ingredientNutritionByQuantity: IngredientNutritionByQuantityDTO[];
}
