import {
  Controller,
  Post,
  Body,
  UploadedFile,
  UseInterceptors,
  UseGuards,
  Get,
  Query,
  Param,
  Put,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Authority } from 'src/utils/decorators';
import {
  CreateIngredientReqDTO,
  CreateIngredientResDTO,
  DeleteIngredientResDTO,
  GetAllIngredientResDTO,
  AdminGetSingleIngredientResDTO,
  UpdateIngredientReqDTO,
  UpdateIngredientResDTO,
} from './dto';
import { IngredientService } from './admin-ingredient.service';
import { AuthGuard } from 'src/middlewares';
import {
  AllowedImageExtensions,
  getMulterMediaOptions,
} from 'src/utils/multer';
import { ParseJsonPipe } from 'src/utils/pipe/parse-json.pipe.';
import { getAllIngredientQueryInterface } from './interface';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { memoryStorage } from 'multer';
import { csvFileFilter } from 'src/common/file-filter/csvFileFilter';

@ApiTags('Admin-Ingredients')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('/admin/ingredients')
export class IngredientController {
  constructor(private readonly ingredientService: IngredientService) {}

  @ApiOperation({ summary: 'Create a new ingredient' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: CreateIngredientReqDTO })
  @ApiResponse({
    status: 201,
    description: 'Successfully created ingredient.',
    type: CreateIngredientResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @Authority()
  @Post()
  @UseInterceptors(
    FileInterceptor(
      'thumbnailFile',
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  async createIngredient(
    @Body('ingredientNutritionByQuantity', ParseJsonPipe)
    ingredientNutritionByQuantity: any,
    @Body()
    createBody: Omit<CreateIngredientReqDTO, 'ingredientNutritionByQuantity'>,
    @UploadedFile() thumbnailFile: Express.Multer.File,
  ) {
    const finalBody: CreateIngredientReqDTO = {
      ...createBody,
      ingredientNutritionByQuantity,
    };
    return this.ingredientService.createIngredient(finalBody, thumbnailFile);
  }

  @ApiOperation({ summary: 'Get all ingredients' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    example: '1',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search by title or name',
    example: 'tomato',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved ingredients.',
    type: GetAllIngredientResDTO,
  })
  @Authority()
  @Get()
  async getAllIngredients(
    @Query() filterQueries: getAllIngredientQueryInterface,
  ) {
    return this.ingredientService.getAllIngredients(filterQueries);
  }

  @ApiOperation({ summary: 'Get single ingredient by ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved ingredient.',
    type: AdminGetSingleIngredientResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Ingredient not found',
    type: ErrorResponse,
  })
  @Authority()
  @Get('/:ingredientId')
  async getSingleIngredient(@Param('ingredientId') id: string) {
    return this.ingredientService.getSingleIngredient(id);
  }

  @ApiOperation({ summary: 'Update an existing ingredient' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UpdateIngredientReqDTO })
  @ApiResponse({
    status: 200,
    description: 'Successfully updated ingredient.',
    type: UpdateIngredientResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Ingredient not found',
    type: ErrorResponse,
  })
  @Authority()
  @Put('/:ingredientId')
  @UseInterceptors(
    FileInterceptor(
      'thumbnailFile',
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  async updateIngredient(
    @Param('ingredientId') id: string,
    @Body('ingredientNutritionByQuantity', ParseJsonPipe)
    ingredientNutritionByQuantity: any,
    @Body()
    updateBody: Omit<UpdateIngredientReqDTO, 'ingredientNutritionByQuantity'>,
    @UploadedFile() thumbnailFile?: Express.Multer.File,
  ) {
    return this.ingredientService.updateIngredient(
      id,
      { ...updateBody, ingredientNutritionByQuantity },
      thumbnailFile,
    );
  }

  @ApiOperation({ summary: 'Delete an ingredient' })
  @ApiResponse({
    status: 200,
    description: 'Successfully deleted ingredient.',
    type: DeleteIngredientResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Ingredient not found',
    type: ErrorResponse,
  })
  @Authority()
  @Put('delete/:id')
  async removeIngredient(@Param('id') id: string) {
    return this.ingredientService.removeIngredient(id);
  }

  @ApiOperation({ summary: 'Bulk create ingredients from CSV' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        csvFile: { type: 'string', format: 'binary' },
      },
      required: ['csvFile'],
    },
  })
  @ApiResponse({ status: 201, description: 'Ingredients created' })
  @ApiResponse({ status: 400, description: 'Bad Request', type: ErrorResponse })
  @Authority()
  @Post('bulk')
  @UseInterceptors(
    FileInterceptor('csvFile', {
      storage: memoryStorage(),
      limits: { fileSize: 5 * 1024 * 1024 },
      fileFilter: csvFileFilter,
    }),
  )
  async createIngredientsByCSV(@UploadedFile() csvFile: Express.Multer.File) {
    return this.ingredientService.createIngredientsFromCSV(csvFile);
  }
}
