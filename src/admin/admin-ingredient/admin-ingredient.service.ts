import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Ingredient } from 'models/ingredient/ingredient.schema';
import { isValidObjectId, Model } from 'mongoose';
import {
  CreateIngredientReqDTO,
  CreateIngredientResDTO,
  DeleteIngredientResDTO,
  GetAllIngredientResDTO,
  AdminGetSingleIngredientResDTO,
  IngredientDTO,
  UpdateIngredientReqDTO,
  UpdateIngredientResDTO,
} from './dto';
import { AwsS3Service } from 'src/third-party/aws';
import { getAllIngredientQueryInterface } from './interface';
import { UtilsService } from 'src/common/services';
import { AdminIngredientUtilsService } from './admin-ingredient-utils.service';
import * as csvParser from 'csv-parser';
import { Readable } from 'stream';

@Injectable()
export class IngredientService {
  constructor(
    @InjectModel(Ingredient.name)
    private readonly ingredientModel: Model<Ingredient>,
    private readonly s3Service: AwsS3Service,
    private readonly utilsService: UtilsService,
    private readonly adminIngredientUtilsService: AdminIngredientUtilsService,
  ) {}

  async createIngredient(
    createBody: CreateIngredientReqDTO,
    thumbnailFile?: Express.Multer.File,
  ): Promise<CreateIngredientResDTO> {
    const { name, ingredientNutritionByQuantity } = createBody;

    // Validate nutrition entries
    this.adminIngredientUtilsService.validateNutritionByQuantity(
      ingredientNutritionByQuantity,
    );

    let thumbnailUrl = createBody.thumbnailUrl;
    if (thumbnailFile) {
      thumbnailUrl =
        await this.adminIngredientUtilsService.uploadThumbnail(thumbnailFile);
    }

    const ingredient = await this.ingredientModel.create({
      name,
      thumbnailUrl,
      ingredientNutritionByQuantity: ingredientNutritionByQuantity.map(
        (item) => ({
          ...item,
        }),
      ),
    });

    const resp = IngredientDTO.transform(ingredient);

    return {
      error: false,
      statusCode: 201,
      msg: 'Ingredient created successfully',
      data: resp,
    };
  }

  async getAllIngredients(
    queryFilters: getAllIngredientQueryInterface,
  ): Promise<GetAllIngredientResDTO> {
    const { page, name } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };

    if (name) {
      query.name = { $regex: new RegExp(name, 'i') };
    }

    const ingredients = await this.ingredientModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.ingredientModel.countDocuments(query);

    const ingredientResp = ingredients.map((item) =>
      IngredientDTO.transform(item),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: ingredientResp.length,
      data: ingredientResp,
    };
  }

  async getSingleIngredient(
    id: string,
  ): Promise<AdminGetSingleIngredientResDTO> {
    if (!isValidObjectId(id)) {
      throw new BadRequestException('Invalid ingredient ID format');
    }

    const ingredient = await this.ingredientModel.findById(id).exec();

    if (!ingredient || ingredient.isDeleted) {
      throw new NotFoundException('Ingredient not found');
    }

    const resp = IngredientDTO.transform(ingredient);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }

  async updateIngredient(
    id: string,
    updateBody: UpdateIngredientReqDTO,
    thumbnailFile?: Express.Multer.File,
  ): Promise<UpdateIngredientResDTO> {
    if (!isValidObjectId(id)) {
      throw new BadRequestException('Invalid ingredient ID');
    }

    const ingredient = await this.ingredientModel.findById(id);
    if (!ingredient || ingredient.isDeleted) {
      throw new NotFoundException('Ingredient not found');
    }

    // validate nutrition by quantity

    this.adminIngredientUtilsService.validateNutritionByQuantity(
      updateBody.ingredientNutritionByQuantity,
    );

    if (updateBody.name) ingredient.name = updateBody.name;
    if (updateBody.ingredientNutritionByQuantity) {
      ingredient.ingredientNutritionByQuantity.splice(
        0,
        ingredient.ingredientNutritionByQuantity.length,
      );
      updateBody.ingredientNutritionByQuantity.forEach((item) => {
        ingredient.ingredientNutritionByQuantity.push(item as any);
      });
    }
    if (thumbnailFile) {
      const imageUrl = await this.s3Service.uploadFile(thumbnailFile);
      ingredient.thumbnailUrl = imageUrl.Location;
    }

    await ingredient.save();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      updatedIngredient: IngredientDTO.transform(ingredient),
    };
  }

  async removeIngredient(id: string): Promise<DeleteIngredientResDTO> {
    const existingIngredient = await this.ingredientModel.findById(id).exec();

    if (!existingIngredient || existingIngredient.isDeleted) {
      throw new NotFoundException('Ingredient not found');
    }

    existingIngredient.isDeleted = true;
    await existingIngredient.save();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Ingredient deleted successfully',
    };
  }

  async createIngredientsFromCSV(
    csvFile: Express.Multer.File,
  ): Promise<CreateIngredientResDTO[]> {
    // const ingredients: CreateIngredientReqDTO[] = [];

    const stream = Readable.from(csvFile.buffer);

    return new Promise((resolve, reject) => {
      const results = [];

      stream
        .pipe(csvParser())
        .on('data', (row) => {
          try {
            const parsedNutrition = JSON.parse(
              row.ingredientNutritionByQuantity,
            );

            this.adminIngredientUtilsService.validateNutritionByQuantity(
              parsedNutrition,
            );

            results.push({
              name: row.name,
              thumbnailUrl: row.thumbnailUrl || null,
              ingredientNutritionByQuantity: parsedNutrition,
            });
          } catch (error) {
            reject(
              new BadRequestException(
                `CSV parsing/validation error for row: ${JSON.stringify(row)} => ${error.message}`,
              ),
            );
          }
        })
        .on('end', async () => {
          const createdIngredients =
            await this.ingredientModel.insertMany(results);

          resolve(
            createdIngredients.map((ingredient) => ({
              error: false,
              statusCode: 201,
              msg: 'Ingredient created successfully',
              data: IngredientDTO.transform(ingredient.toObject()),
            })),
          );
        })
        .on('error', (err) =>
          reject(new BadRequestException(`CSV parse error: ${err.message}`)),
        );
    });
  }
}
