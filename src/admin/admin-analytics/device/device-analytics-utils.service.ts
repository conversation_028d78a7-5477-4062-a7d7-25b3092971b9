import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { User } from 'models/user';
import { UserDeviceRecord } from 'models/user-records/Device-records';
import { Model } from 'mongoose';
import { GetAverageDeviceUsageResDto } from './dto/get-averega-device-usage-trends.dto';
import { AnalyticsDeviceUsageGoalDTO } from './dto/get-device-usage-goals-achieved.dto';

@Injectable()
export class DeviceAnalyticsUtilsService {
  constructor(
    @InjectModel(UserDeviceRecord.name)
    private readonly userDeviceRecordModel: Model<UserDeviceRecord>,

    @InjectModel(User.name)
    private readonly userModel: Model<User>,
  ) {}

  async getWeeklyDeviceAverageUsageTrends(): Promise<GetAverageDeviceUsageResDto> {
    const today = new Date();
    today.setUTCHours(23, 59, 59, 999);
    const lastWeek = new Date(today);
    lastWeek.setDate(today.getDate() - 7);
    lastWeek.setUTCHours(0, 0, 0, 0);

    const results = await this.userDeviceRecordModel.aggregate([
      {
        $match: {
          date: { $gte: lastWeek, $lt: today },
        },
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
          averageDeviceUsage: { $avg: { $ifNull: ['$totalUsageTime', 0] } },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);

    return GetAverageDeviceUsageResDto.transform(
      Array.from({ length: 7 }, (_, i) => {
        const date = new Date(lastWeek);
        date.setDate(date.getDate() + i + 1);
        const label = date.toISOString().slice(0, 10);
        const result = results.find((r) => r._id === label);
        return {
          label: result ? result._id : label,
          averageDeviceUsage: result ? result.averageDeviceUsage : 0,
        };
      }),
    );
  }

  async getMonthlyDeviceAverageUsageTrends(): Promise<GetAverageDeviceUsageResDto> {
    const today = new Date();
    today.setUTCHours(23, 59, 59, 999);

    const weekPeriods = [];
    for (let i = 0; i < 4; i++) {
      const endDate = new Date(today);
      endDate.setDate(today.getDate() - i * 7);
      endDate.setUTCHours(23, 59, 59, 999);

      const startDate = new Date(endDate);
      startDate.setDate(endDate.getDate() - 6);
      startDate.setUTCHours(0, 0, 0, 0);

      weekPeriods.push({ startDate, endDate });
    }

    const oldestDate = weekPeriods[weekPeriods.length - 1].startDate;

    const pipeline = [
      {
        $match: {
          date: { $gte: oldestDate, $lte: today },
        },
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
          averageDeviceUsage: { $avg: { $ifNull: ['$totalUsageTime', 0] } },
        },
      },
    ];

    const results = await this.userDeviceRecordModel.aggregate(pipeline);

    const trends = weekPeriods.map(({ startDate, endDate }) => {
      const weekRecords = results.filter(
        (record) =>
          record._id >= startDate.toISOString().slice(0, 10) &&
          record._id <= endDate.toISOString().slice(0, 10),
      );

      const totalUsage = weekRecords.reduce(
        (sum, record) => sum + record.averageDeviceUsage,
        0,
      );

      const averageUsage =
        weekRecords.length > 0 ? totalUsage / weekRecords.length : 0;

      return {
        label: `${startDate.toISOString().slice(0, 10)} to ${endDate.toISOString().slice(0, 10)}`,
        averageDeviceUsage: averageUsage,
      };
    });

    return GetAverageDeviceUsageResDto.transform(trends.reverse());
  }

  async calculateDeviceGoalAchieved(
    days: number,
  ): Promise<AnalyticsDeviceUsageGoalDTO> {
    const today = new Date();
    const startDate = new Date(today.getTime() - days * 24 * 60 * 60 * 1000);
    startDate.setUTCHours(0, 0, 0, 0);
    today.setUTCHours(23, 59, 59, 999);

    const targetDates = Array.from({ length: days }, (_, i) => {
      const d = new Date(startDate);
      d.setDate(d.getDate() + i + 1);
      return d.toISOString().slice(0, 10);
    });

    const pipeline = [
      {
        $match: {
          date: { $gte: startDate, $lt: today },
        },
      },

      {
        $addFields: {
          userObjectId: { $toObjectId: '$userId' },
        },
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
            userId: '$userObjectId',
          },
          totalUsageTime: { $sum: '$totalUsageTime' },
        },
      },
      {
        $addFields: {
          totalUsageTimeInHours: { $divide: ['$totalUsageTime', 3600] },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id.userId',
          foreignField: '_id',
          as: 'userDetails',
        },
      },
      {
        $unwind: '$userDetails',
      },
      {
        $match: {
          'userDetails.role': { $ne: 'admin' },
          'userDetails.isAccountCompleted': true,
          $or: [
            { 'userDetails.deviceUsageLimit': { $in: [null, undefined] } },
            { 'userDetails.deviceUsageLimit': { $exists: false } },
            {
              $expr: {
                $lte: [
                  '$userDetails.deviceUsageLimit',
                  '$totalUsageTimeInHours',
                ],
              },
            },
          ],
        },
      },
      {
        $project: {
          _id: 1,
        },
      },
      { $group: { _id: '$_id.userId', dates: { $push: '$_id.date' } } },
    ];

    const results = await this.userDeviceRecordModel.aggregate(pipeline);
    const users = await this.userModel.find({
      isAccountCompleted: true,
      role: { $ne: 'admin' },
    });

    let goalAchieved = 0;

    for (const user of results) {
      const deviceDates = user.dates.sort();
      const matchesAll = targetDates.every((targetDate) =>
        deviceDates.includes(targetDate),
      );
      if (matchesAll) {
        goalAchieved++;
      }
    }

    return AnalyticsDeviceUsageGoalDTO.transform({
      goalAchieved,
      totalUsers: users.length,
    });
  }
}
