import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { DeviceAnalyticsService } from './device-analytics.service';
import { AuthGuard } from 'src/middlewares';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  DEVICE_USAGE_PERIOD,
  GetAverageDeviceUsageResDto,
} from './dto/get-averega-device-usage-trends.dto';
import { ErrorResponse } from 'src/utils/responses';
import {
  AnalyticsDeviceUsageGoalDTO,
  DEVICE_GOAL_USAGE_PERIOD,
} from './dto/get-device-usage-goals-achieved.dto';
import { Authority } from 'src/utils/decorators';

@ApiTags('Admin-Analytics')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin/analytics/device')
export class DeviceAnalyticsController {
  constructor(
    private readonly deviceAnalyticsService: DeviceAnalyticsService,
  ) {}

  @ApiOperation({
    summary: 'Get average device usage (weekly or monthly)',
  })
  @ApiParam({
    name: 'period',
    enum: DEVICE_USAGE_PERIOD,
    required: true,
    description: 'Time period to filter the device usage data',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved average device usage',
    type: GetAverageDeviceUsageResDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid type provided',
    type: ErrorResponse,
  })
  @Authority()
  @Get('/average-device-usage/:period')
  getAverageDeviceUsage(@Param('period') period: DEVICE_USAGE_PERIOD) {
    return this.deviceAnalyticsService.getAverageDeviceUsage(period);
  }

  @ApiOperation({
    summary:
      'Get no of users who achieved their device usage goal continuously (daily, weekly, monthly)',
  })
  @ApiParam({
    name: 'period',
    enum: DEVICE_GOAL_USAGE_PERIOD,
    required: true,
    description: 'Time period to filter the device usage data',
  })
  @ApiResponse({
    status: 200,
    description:
      'Successfully retrieved no of users who achieved their device usage goal continuously',
    type: AnalyticsDeviceUsageGoalDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid type provided',
    type: ErrorResponse,
  })
  @Authority()
  @Get('device-usage-goal-achieved/:period')
  getDeviceUsageGoalAchieved(
    @Param('period') period: DEVICE_GOAL_USAGE_PERIOD,
  ) {
    return this.deviceAnalyticsService.getDeviceUsageGoalAchieved(period);
  }
}
