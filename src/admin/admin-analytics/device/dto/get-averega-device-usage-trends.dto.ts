import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export enum DEVICE_USAGE_PERIOD {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export class AverageDeviceTrend {
  @ApiProperty({ example: '2022-01-01' })
  label: string;

  @ApiProperty({ example: 10 })
  averageDeviceUsage: number;
}

export class GetAverageDeviceUsageResDto extends BaseResponse {
  @ApiProperty({
    example: 200,
    description:
      'Http response code. 400-Bad Request, 403 - Forbidden Exception, 404 - Not Found, 500 - Internal Server Exception',
  })
  statusCode: number;
  @ApiProperty({
    example: false,
    description: 'every time value is `false` for success response',
  })
  error: boolean;
  @ApiProperty({
    example: 'Average device usage analytics fetched successfully',
    description: 'message',
  })
  msg: string;
  @ApiProperty({
    type: AverageDeviceTrend,
    isArray: true,
  })
  data: AverageDeviceTrend[];
  @ApiProperty({
    type: Number,
    example: 10,
    description: 'Total number of records',
  })
  total: number;

  static transform(data: any[]): GetAverageDeviceUsageResDto {
    const transformedObj = new GetAverageDeviceUsageResDto();

    transformedObj.data = data.map((item) => ({
      label: item.label.toString(),
      averageDeviceUsage: item.averageDeviceUsage || 0,
    }));

    transformedObj.statusCode = 200;
    transformedObj.error = false;
    transformedObj.msg = 'Average device usage analytics fetched successfully';
    transformedObj.total = data.length;

    return transformedObj;
  }
}
