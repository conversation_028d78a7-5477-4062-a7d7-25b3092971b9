import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';

export enum DEVICE_GOAL_USAGE_PERIOD {
  Daily = '24h',
  Weekly = '7d',
  Monthly = '30d',
}

export class DeviceGoalAchieved {
  @ApiProperty({
    description: 'Number of users who achieved the device usage goal',
    example: 0.5,
  })
  goalAchieved: number;

  @ApiProperty({
    description: 'Total number of users',
    example: 100,
  })
  totalUsers: number;
}

export class AnalyticsDeviceUsageGoalDTO extends BaseResponse {
  @ApiProperty({
    example: 200,
    description:
      'Http response code. 400-Bad Request, 403 - Forbidden Exception, 404 - Not Found, 500 - Internal Server Exception',
  })
  statusCode: number;
  @ApiProperty({
    example: false,
    description: 'every time value is `false` for success response',
  })
  error: boolean;
  @ApiProperty({
    example: 'Device usage goals achieved analytics fetched successfully',
    description: 'message',
  })
  msg: string;
  @ApiProperty({
    example: DeviceGoalAchieved,
  })
  data: DeviceGoalAchieved;

  static transform(data: any): AnalyticsDeviceUsageGoalDTO {
    const transformedObj = new AnalyticsDeviceUsageGoalDTO();
    transformedObj.data = {
      goalAchieved: data.goalAchieved || 0,
      totalUsers: data.totalUsers || 0,
    };
    transformedObj.statusCode = 200;
    transformedObj.error = false;
    transformedObj.msg =
      'Device usage goals achieved analytics fetched successfully';
    return transformedObj;
  }
}
