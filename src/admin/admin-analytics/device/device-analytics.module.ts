import { Modu<PERSON> } from '@nestjs/common';
import { DeviceAnalyticsService } from './device-analytics.service';
import { DeviceAnalyticsController } from './device-analytics.controller';
import { DeviceAnalyticsUtilsService } from './device-analytics-utils.service';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'models/user';
import {
  UserDeviceRecord,
  UserDeviceRecordSchema,
} from 'models/user-records/Device-records';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: UserDeviceRecord.name, schema: UserDeviceRecordSchema },
    ]),
    AuthModule,
    CommonModule,
    RepoModule,
  ],
  controllers: [DeviceAnalyticsController],
  providers: [DeviceAnalyticsService, DeviceAnalyticsUtilsService],
})
export class DeviceAnalyticsModule {}
