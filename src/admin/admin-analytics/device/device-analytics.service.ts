import { Injectable } from '@nestjs/common';
import { DeviceAnalyticsUtilsService } from './device-analytics-utils.service';
import { DEVICE_USAGE_PERIOD } from './dto/get-averega-device-usage-trends.dto';
import { DEVICE_GOAL_USAGE_PERIOD } from './dto/get-device-usage-goals-achieved.dto';

@Injectable()
export class DeviceAnalyticsService {
  constructor(
    private readonly deviceAnalyticsUtilsService: DeviceAnalyticsUtilsService,
  ) {}
  getAverageDeviceUsage(period: DEVICE_USAGE_PERIOD) {
    if (period == DEVICE_USAGE_PERIOD.WEEKLY) {
      return this.deviceAnalyticsUtilsService.getWeeklyDeviceAverageUsageTrends();
    } else if (period == DEVICE_USAGE_PERIOD.MONTHLY) {
      return this.deviceAnalyticsUtilsService.getMonthlyDeviceAverageUsageTrends();
    } else throw new Error('Invalid period provided');
  }

  getDeviceUsageGoalAchieved(period: DEVICE_GOAL_USAGE_PERIOD) {
    if (period == DEVICE_GOAL_USAGE_PERIOD.Daily) {
      return this.deviceAnalyticsUtilsService.calculateDeviceGoalAchieved(1);
    } else if (period == DEVICE_GOAL_USAGE_PERIOD.Weekly) {
      return this.deviceAnalyticsUtilsService.calculateDeviceGoalAchieved(7);
    } else if (period == DEVICE_GOAL_USAGE_PERIOD.Monthly) {
      return this.deviceAnalyticsUtilsService.calculateDeviceGoalAchieved(30);
    } else throw new Error('Invalid period provided');
  }
}
