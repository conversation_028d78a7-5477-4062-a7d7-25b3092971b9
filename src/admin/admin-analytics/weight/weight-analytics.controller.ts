import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { WeightAnalyticsService } from './weight-analytics.service';
import { AuthGuard } from 'src/middlewares';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  getWeightStatusSummaryResDto,
  WEIGHT_FILTER,
} from './dto/get-weight-status-summary.dto';
import { Authority } from 'src/utils/decorators';
import { ErrorResponse } from 'src/utils/responses';

@ApiTags('Admin-Analytics')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin/analytics/weight')
export class WeightAnalyticsController {
  constructor(private readonly weightService: WeightAnalyticsService) {}

  @ApiOperation({
    summary:
      'Get no of users who are maintaining, gaining or losing weight continuously (daily, weekly, monthly)',
  })
  @ApiParam({
    name: 'period',
    enum: WEIGHT_FILTER,
    required: true,
    description: 'Time period to filter the weight status summary data',
  })
  @ApiResponse({
    status: 200,
    description:
      'Successfully retrieved no of users who are maintaining, gaining or losing weight continuously',
    type: getWeightStatusSummaryResDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid type provided',
    type: ErrorResponse,
  })
  @Authority()
  @Get('weight-status-summary/:period')
  getWeightStatusSummary(@Param('period') period: WEIGHT_FILTER) {
    return this.weightService.getWeightStatusSummary(period);
  }
}
