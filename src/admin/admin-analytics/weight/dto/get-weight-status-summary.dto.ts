import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export enum WEIGHT_FILTER {
  Weekly = '7d',
  Monthly = '30d',
}

export class WeightStatusSummary {
  @ApiProperty({ example: 20 })
  maintaining: number;

  @ApiProperty({ example: 10 })
  gaining: number;

  @ApiProperty({ example: 20 })
  losing: number;
}

export class getWeightStatusSummaryResDto extends BaseResponse {
  @ApiProperty({
    example: 200,
    description:
      'Http response code. 400-Bad Request, 403 - Forbidden Exception, 404 - Not Found, 500 - Internal Server Exception',
  })
  statusCode: number;
  @ApiProperty({
    example: false,
    description: 'every time value is `false` for success response',
  })
  error: boolean;
  @ApiProperty({
    example: 'Weight status summary analytics fetched successfully',
    description: 'message',
  })
  msg: string;
  @ApiProperty({
    type: WeightStatusSummary,
    example: WeightStatusSummary,
  })
  data: WeightStatusSummary;

  static transform(data: any): getWeightStatusSummaryResDto {
    const transformedObj = new getWeightStatusSummaryResDto();
    transformedObj.data = {
      maintaining: data.maintaining || 0,
      gaining: data.gaining || 0,
      losing: data.losing || 0,
    };

    transformedObj.statusCode = 200;
    transformedObj.error = false;
    transformedObj.msg = 'Weight status summary analytics fetched successfully';

    return transformedObj;
  }
}
