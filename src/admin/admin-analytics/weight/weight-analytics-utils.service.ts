import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { User } from 'models/user';
import { UserWeightRecords } from 'models/user-records/Weight-Records';
import { Model } from 'mongoose';
import { getWeightStatusSummaryResDto } from './dto/get-weight-status-summary.dto';

@Injectable()
export class weightAnalyticsUtilsService {
  constructor(
    @InjectModel(UserWeightRecords.name)
    private readonly weightModel: Model<UserWeightRecords>,

    @InjectModel(User.name)
    private readonly userModel: Model<User>,
  ) {}

  async getRecordNDaysRecordSummary(
    days: number,
  ): Promise<getWeightStatusSummaryResDto> {
    const endDate = new Date();
    endDate.setUTCHours(23, 59, 59, 999);
    const startDate = new Date(endDate);
    startDate.setDate(endDate.getDate() - days);
    startDate.setUTCHours(0, 0, 0, 0);

    const weightRecords = await this.weightModel.aggregate([
      {
        $match: {
          date: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $sort: { date: 1 },
      },
      {
        $group: {
          _id: '$userId',
          firstWeight: { $first: '$weight' },
          lastWeight: { $last: '$weight' },
        },
      },
    ]);

    const totalUsers = await this.userModel.countDocuments({
      isAccountCompleted: true,
      role: { $ne: 'admin' },
    });

    let gaining = 0;
    let losing = 0;
    // let maintaining = 0;

    for (const record of weightRecords) {
      const { firstWeight, lastWeight } = record;

      if (lastWeight > firstWeight) {
        gaining++;
      } else if (lastWeight < firstWeight) {
        losing++;
      }
      //   else {
      //     maintaining++;
      //   }
    }

    return getWeightStatusSummaryResDto.transform({
      gaining,
      losing,
      maintaining: totalUsers - (gaining + losing),
    });
  }
}
