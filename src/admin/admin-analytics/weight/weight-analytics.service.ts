import { Injectable } from '@nestjs/common';
import { WEIGHT_FILTER } from './dto/get-weight-status-summary.dto';
import { weightAnalyticsUtilsService } from './weight-analytics-utils.service';

@Injectable()
export class WeightAnalyticsService {
  constructor(private readonly weightService: weightAnalyticsUtilsService) {}
  getWeightStatusSummary(period: WEIGHT_FILTER) {
    if (period === WEIGHT_FILTER.Weekly) {
      return this.weightService.getRecordNDaysRecordSummary(7);
    } else if (period === WEIGHT_FILTER.Monthly) {
      return this.weightService.getRecordNDaysRecordSummary(30);
    } else {
      throw new Error('Invalid period');
    }
  }
}
