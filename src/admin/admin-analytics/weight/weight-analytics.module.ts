import { Modu<PERSON> } from '@nestjs/common';
import { WeightAnalyticsService } from './weight-analytics.service';
import { WeightAnalyticsController } from './weight-analytics.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { User, UserSchema } from 'models/user';
import {
  UserWeightRecords,
  UserWeightRecordsSchema,
} from 'models/user-records/Weight-Records';
import { weightAnalyticsUtilsService } from './weight-analytics-utils.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserWeightRecords.name, schema: UserWeightRecordsSchema },
      { name: User.name, schema: UserSchema },
    ]),
    AuthModule,
    CommonModule,
    RepoModule,
  ],
  controllers: [WeightAnalyticsController],
  providers: [WeightAnalyticsService, weightAnalyticsUtilsService],
})
export class WeightAnalyticModule {}
