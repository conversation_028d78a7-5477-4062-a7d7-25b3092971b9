import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'models/user';
import {
  UserSleepRecords,
  UserSleepRecordsSchema,
} from 'models/user-records/Sleep-Records';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { SleepController } from './sleep/sleep-analytics.controller';
import { SleepAnalyticsService } from './sleep/sleep-analytics.service';
import { SleepAnalyticsUtilsService } from './sleep/sleep-analytics-utils.service';
import {
  UserJourneyPerDay,
  UserJourneyPerDaySchema,
} from 'models/user-journey';
import {
  UserDeviceConnections,
  UserDeviceConnectionsSchema,
} from 'models/device';
import {
  UserDeviceRecord,
  UserDeviceRecordSchema,
} from 'models/user-records/Device-records';
import { AdminAnalyticsController } from './user/admin-analytics.controller';
import { AdminAnalyticsService } from './user/admin-analytics.service';
import { AdminAnalyticsUtilsService } from './user/admin-analytics-utils.service';
import { AdminMoodAnalyticsController } from './mood/admin-mood.controller';
import { AdminMoodAnalyticsService } from './mood/admin-mood.service';
import { AdminMoodAnalyticsUtilsService } from './mood/admin-mood-utils.service';
import {
  DailyMoodAverage,
  DailyMoodSchema,
  MonthlyMoodAverage,
  MonthlyMoodSchema,
} from 'models/user-records/Mood-Records';
import { AdminNutritionAnalyticsController } from './nutrition/nutrition.controller';
import { AdminNutritionAnalyticsService } from './nutrition/nutrition.service';
import { AdminNutritionAnalyticsUtilsService } from './nutrition/nutrition-utils.service';
import {
  MealDailyAverageRecords,
  MealDailyAverageRecordsSchema,
  UserMealRecords,
  UserMealRecordsSchema,
} from 'models/user-records/Meal-Records';
import { AdminActivityModule } from './activity/admin-activity.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserSleepRecords.name, schema: UserSleepRecordsSchema },
      { name: User.name, schema: UserSchema },
      { name: UserJourneyPerDay.name, schema: UserJourneyPerDaySchema },
      { name: UserDeviceConnections.name, schema: UserDeviceConnectionsSchema },
      { name: UserDeviceRecord.name, schema: UserDeviceRecordSchema },
      { name: DailyMoodAverage.name, schema: DailyMoodSchema },
      { name: MonthlyMoodAverage.name, schema: MonthlyMoodSchema },
      { name: UserMealRecords.name, schema: UserMealRecordsSchema },
      {
        name: MealDailyAverageRecords.name,
        schema: MealDailyAverageRecordsSchema,
      },
    ]),
    AuthModule,
    CommonModule,
    RepoModule,
    AdminActivityModule,
  ],
  controllers: [
    SleepController,
    AdminAnalyticsController,
    AdminMoodAnalyticsController,
    AdminNutritionAnalyticsController,
  ],
  providers: [
    SleepAnalyticsService,
    SleepAnalyticsUtilsService,
    AdminAnalyticsService,
    AdminAnalyticsUtilsService,
    AdminMoodAnalyticsService,
    AdminMoodAnalyticsUtilsService,
    AdminNutritionAnalyticsService,
    AdminNutritionAnalyticsUtilsService,
  ],
})
export class AdminAnalyticsModule {}
