import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { AuthGuard } from 'src/middlewares';
import { Authority } from 'src/utils/decorators';
import { GetMacroNutrientDTO, GetNutritionDistributionDTO } from './dto';
import { AdminNutritionAnalyticsService } from './nutrition.service';
import { GetNutritionAnalyticsQueryInterface } from './interface';

@ApiTags('User-Nutrition')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin/analytics')
export class AdminNutritionAnalyticsController {
  constructor(
    private readonly nutritionService: AdminNutritionAnalyticsService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'User nutrition records retrieved successfully.',
    type: GetNutritionDistributionDTO,
  })
  @Authority()
  @Get('/nutrition')
  async getNutritionRecordsAnalytics(
    @Query() queryFilters: GetNutritionAnalyticsQueryInterface,
  ): Promise<GetNutritionDistributionDTO> {
    return this.nutritionService.getUserNutritionAnalytics(queryFilters);
  }

  @Authority()
  @Get('/mealType')
  async getUserMealTypeDistribution(
    @Query() queryFilters: GetNutritionAnalyticsQueryInterface,
  ) {
    return this.nutritionService.getUserMealTypeDistribution(queryFilters);
  }

  @Authority()
  @Get('/popularMeal')
  async getPopularMeal(
    @Query() queryFilters: GetNutritionAnalyticsQueryInterface,
  ) {
    return this.nutritionService.getPopularMeal(queryFilters);
  }

  @Authority()
  @Get('/macronutrition')
  async getMacroNutrientAnalytics(
    @Query() queryFilters: GetNutritionAnalyticsQueryInterface,
  ): Promise<GetMacroNutrientDTO> {
    return this.nutritionService.getMacroNutrientAnalytics(queryFilters);
  }
}
