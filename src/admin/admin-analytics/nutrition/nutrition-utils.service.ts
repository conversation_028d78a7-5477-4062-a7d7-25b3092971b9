import { Injectable, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { subDays, format, startOfDay, endOfDay, addDays } from 'date-fns';

import {
  GetMacroNutrientDTO,
  MacroNutrientDTO,
  MealPopularityResponseDto,
  NutritionDistributionDTO,
} from './dto';
import { GetNutritionDistributionDTO } from './dto/getNutritionDistribution.dto';
import {
  MEAL_NAMES,
  MealDailyAverageRecords,
  UserMealRecords,
} from 'models/user-records/Meal-Records';
import { Recipes } from 'models/recipe';

@Injectable()
export class AdminNutritionAnalyticsUtilsService {
  constructor(
    @InjectModel(UserMealRecords.name)
    private readonly userMealRecordModel: Model<UserMealRecords>,
    @InjectModel(Recipes.name)
    private readonly recipeModel: Model<Recipes>,
    @InjectModel(MealDailyAverageRecords.name)
    private readonly mealDailyAvgModel: Model<MealDailyAverageRecords>,
  ) {}

  async getLast7DaysUserCounts(): Promise<GetNutritionDistributionDTO> {
    const now = new Date();
    now.setHours(0, 0, 0, 0);

    const last7Days: string[] = [];
    for (let i = 6; i >= 0; i--) {
      const date = subDays(now, i);
      const dateStr = format(date, 'yyyy-MM-dd');
      last7Days.push(dateStr);
    }

    const startDate = startOfDay(subDays(now, 6));
    const endDate = endOfDay(now);

    // Fetch meal records based on `date` field
    const records = await this.userMealRecordModel
      .find({
        date: {
          $gte: startDate,
          $lte: endDate,
        },
      })
      .select('userId date')
      .lean();

    // Initialize daily user counts
    const dailyUserCounts: Record<string, Set<string>> = {};
    last7Days.forEach((dateStr) => {
      dailyUserCounts[dateStr] = new Set();
    });

    // Count unique users for each date
    records.forEach((record) => {
      const recordDate = format(new Date(record.date), 'yyyy-MM-dd');
      if (dailyUserCounts[recordDate]) {
        dailyUserCounts[recordDate].add(record.userId.toString());
      }
    });

    // Build the response DTO list
    const responseRecords = last7Days.map((dateStr) => {
      return NutritionDistributionDTO.transform({
        period: dateStr,
        noOfUser: dailyUserCounts[dateStr].size,
      });
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: responseRecords,
    };
  }

  async getMonthlyNutritionDistribution(): Promise<GetNutritionDistributionDTO> {
    const now = new Date();
    now.setHours(23, 59, 59, 999);

    const endDate = endOfDay(now);
    const startDate = startOfDay(subDays(endDate, 34)); // 35 days including today

    const records = await this.userMealRecordModel
      .find({
        date: {
          $gte: startDate,
          $lte: endDate,
        },
      })
      .select('userId date')
      .lean();

    const responseRecords = [];

    for (let i = 0; i < 5; i++) {
      const weekStart = startOfDay(addDays(startDate, i * 7));
      let weekEnd = endOfDay(addDays(weekStart, 6));

      // Last week ends today
      if (i === 4) {
        weekEnd = endDate;
      }

      const weekRecords = records.filter((record) => {
        const recordDate = new Date(record.date);
        return recordDate >= weekStart && recordDate <= weekEnd;
      });

      // Count unique users in this week
      const uniqueUsers = new Set<string>();
      weekRecords.forEach((record) => {
        uniqueUsers.add(record.userId.toString());
      });

      const periodLabel = `${format(weekStart, 'yyyy-MM-dd')} to ${format(
        weekEnd,
        'yyyy-MM-dd',
      )}`;

      responseRecords.push({
        period: periodLabel,
        noOfUser: uniqueUsers.size,
      });
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: responseRecords,
    };
  }

  // -------analytics based on mealType--------

  private getAllMealTypes(): string[] {
    return Object.values(MEAL_NAMES);
  }

  async getWeeklyMealTypeDistribution(): Promise<GetNutritionDistributionDTO> {
    const now = new Date();
    const endDate = endOfDay(now);
    const startDate = startOfDay(subDays(endDate, 6));

    const records = await this.userMealRecordModel
      .find({
        date: { $gte: startDate, $lte: endDate },
      })
      .select('userId mealName date')
      .lean();

    const mealTypes = this.getAllMealTypes();

    const dailyData = [];

    for (let i = 0; i < 7; i++) {
      const currentDate = startOfDay(addDays(startDate, i));
      const nextDate = endOfDay(currentDate);

      const dayRecords = records.filter((r) => {
        const recordDate = new Date(r.date);
        return recordDate >= currentDate && recordDate <= nextDate;
      });

      const mealTypeCounts: Record<string, Set<string>> = {};
      mealTypes.forEach((type) => {
        mealTypeCounts[type] = new Set();
      });

      dayRecords.forEach((record) => {
        if (mealTypes.includes(record.mealName)) {
          mealTypeCounts[record.mealName].add(record.userId.toString());
        }
      });

      const mealTypeUserCounts: Record<string, number> = {};
      mealTypes.forEach((type) => {
        mealTypeUserCounts[type] = mealTypeCounts[type].size;
      });

      dailyData.push({
        period: format(currentDate, 'yyyy-MM-dd'),
        mealTypeUserCounts,
      });
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: dailyData,
    };
  }

  async getMonthlyMealTypeDistribution(): Promise<GetNutritionDistributionDTO> {
    const now = new Date();
    const endDate = endOfDay(now);
    const startDate = startOfDay(subDays(endDate, 34)); // last 35 days

    const records = await this.userMealRecordModel
      .find({
        date: { $gte: startDate, $lte: endDate },
      })
      .select('userId mealName date')
      .lean();

    const mealTypes = this.getAllMealTypes();

    const responseRecords = [];

    for (let i = 0; i < 5; i++) {
      const weekStart = startOfDay(addDays(startDate, i * 7));
      let weekEnd = endOfDay(addDays(weekStart, 6));

      if (i === 4) {
        weekEnd = endDate;
      }

      const weekRecords = records.filter((r) => {
        const recordDate = new Date(r.date);
        return recordDate >= weekStart && recordDate <= weekEnd;
      });

      const mealTypeCounts: Record<string, Set<string>> = {};
      mealTypes.forEach((type) => {
        mealTypeCounts[type] = new Set();
      });

      weekRecords.forEach((record) => {
        if (mealTypes.includes(record.mealName)) {
          mealTypeCounts[record.mealName].add(record.userId.toString());
        }
      });

      const mealTypeUserCounts: Record<string, number> = {};
      mealTypes.forEach((type) => {
        mealTypeUserCounts[type] = mealTypeCounts[type].size;
      });

      const periodLabel = `${format(weekStart, 'yyyy-MM-dd')} to ${format(
        weekEnd,
        'yyyy-MM-dd',
      )}`;

      responseRecords.push({
        period: periodLabel,
        mealTypeUserCounts,
      });
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: responseRecords,
    };
  }

  //---------most popular meal----------

  private async getPopularMeal(startDate: Date, endDate: Date) {
    // Fetch all meal records in the date range
    const mealRecords = await this.userMealRecordModel
      .find({
        date: { $gte: startDate, $lte: endDate },
        isDeleted: false,
      })
      .select('meals') // Only fetch the meals array
      .lean();

    // Flatten all recipeIds from meals
    const recipeCountMap: Record<string, number> = {};
    mealRecords.forEach((record) => {
      if (record.meals && record.meals.length) {
        record.meals.forEach((meal) => {
          const recipeId = meal.recipeId?.toString();
          if (recipeId) {
            recipeCountMap[recipeId] = (recipeCountMap[recipeId] || 0) + 1;
          }
        });
      }
    });

    if (Object.keys(recipeCountMap).length === 0) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        data: [],
      };
    }

    // Sort recipes by count and get top 5
    const sortedRecipes = Object.entries(recipeCountMap)
      .sort(([, countA], [, countB]) => countB - countA)
      .slice(0, 5);

    const recipeIds = sortedRecipes.map(([id]) => new Types.ObjectId(id));

    // Fetch recipe names
    const popularRecipes = await this.recipeModel
      .find({ _id: { $in: recipeIds } })
      .select('title')
      .lean();

    const recipeTitleMap = popularRecipes.reduce(
      (map, recipe) => {
        map[recipe._id.toString()] = recipe.title;
        return map;
      },
      {} as Record<string, string>,
    );

    const response: MealPopularityResponseDto[] = sortedRecipes
      .map(([recipeId, count]) => ({
        recipeId,
        recipeName: recipeTitleMap[recipeId],
        count,
      }))
      .filter((r) => r.recipeName); // Ensure recipe was found

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: response,
    };
  }

  async getWeeklyPopularMeal() {
    const now = new Date();
    const endDate = endOfDay(now);
    const startDate = startOfDay(subDays(endDate, 6)); // 7 days total
    return this.getPopularMeal(startDate, endDate);
  }

  async getMonthlyPopularMeal() {
    const now = new Date();
    const endDate = endOfDay(now);
    const startDate = startOfDay(subDays(endDate, 29)); // 30 days total
    return this.getPopularMeal(startDate, endDate);
  }

  // macro distribution analytics

  async getWeeklyMacroNutrientStats(): Promise<GetMacroNutrientDTO> {
    const now = new Date();
    const endDate = endOfDay(now);
    const startDate = startOfDay(subDays(endDate, 6)); // last 7 days including today

    const records = await this.mealDailyAvgModel
      .find({
        date: { $gte: startDate, $lte: endDate },
      })
      .select('date userId calories protein carbs fats fiber')
      .lean();

    const dailyStats: MacroNutrientDTO[] = [];

    for (let i = 0; i < 7; i++) {
      const dayStart = startOfDay(addDays(startDate, i));
      const dayEnd = endOfDay(dayStart);
      const dayLabel = format(dayStart, 'yyyy-MM-dd');

      const dayRecords = records.filter(
        (r) => new Date(r.date) >= dayStart && new Date(r.date) <= dayEnd,
      );

      const userCount = new Set(dayRecords.map((r) => r.userId.toString()))
        .size;

      let totalCalories = 0;
      let totalProtein = 0;
      let totalCarbs = 0;
      let totalFats = 0;
      let totalFiber = 0;

      dayRecords.forEach((rec) => {
        totalCalories += rec.calories || 0;
        totalProtein += rec.protein || 0;
        totalCarbs += rec.carbs || 0;
        totalFats += rec.fats || 0;
        totalFiber += rec.fiber || 0;
      });

      const avgCalories = userCount > 0 ? totalCalories / userCount : 0;

      // Total macro weight for percentage calculation
      const totalMacro = totalProtein + totalCarbs + totalFats + totalFiber;

      const calcPercent = (val: number) =>
        totalMacro > 0 ? parseFloat(((val / totalMacro) * 100).toFixed(2)) : 0;

      dailyStats.push(
        MacroNutrientDTO.transform({
          period: dayLabel,
          calories: parseFloat(avgCalories.toFixed(2)),
          protein: calcPercent(totalProtein),
          carbs: calcPercent(totalCarbs),
          fats: calcPercent(totalFats),
          fiber: calcPercent(totalFiber),
        }),
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: dailyStats,
    };
  }

  async getMonthlyMacroNutrientStats(): Promise<GetMacroNutrientDTO> {
    const now = new Date();
    const endDate = endOfDay(now);
    const startDate = startOfDay(subDays(endDate, 29)); // Last 30 days

    const records = await this.mealDailyAvgModel
      .find({
        date: { $gte: startDate, $lte: endDate },
      })
      .select('date calories protein carbs fats fiber userId')
      .lean();

    const weeklyStats = [];

    for (let i = 0; i < 5; i++) {
      const weekStart = startOfDay(addDays(startDate, i * 7));
      let weekEnd = endOfDay(addDays(weekStart, 6));
      if (weekEnd > endDate) {
        weekEnd = endDate;
      }

      const weekRecords = records.filter((r) => {
        const recordDate = new Date(r.date);
        return recordDate >= weekStart && recordDate <= weekEnd;
      });

      const uniqueUserIds = new Set<string>();
      let totalCalories = 0;
      let totalProtein = 0;
      let totalCarbs = 0;
      let totalFats = 0;
      let totalFiber = 0;

      weekRecords.forEach((r) => {
        totalCalories += r.calories || 0;
        totalProtein += r.protein || 0;
        totalCarbs += r.carbs || 0;
        totalFats += r.fats || 0;
        totalFiber += r.fiber || 0;
        uniqueUserIds.add(r.userId.toString());
      });

      const userCount = uniqueUserIds.size || 1; // prevent division by zero
      const avgCalories = totalCalories / userCount;

      const totalMacros = totalProtein + totalCarbs + totalFats + totalFiber;
      const calcPercent = (val: number) =>
        totalMacros > 0
          ? parseFloat(((val / totalMacros) * 100).toFixed(2))
          : 0;

      const periodLabel = `${format(weekStart, 'yyyy-MM-dd')} to ${format(
        weekEnd,
        'yyyy-MM-dd',
      )}`;

      weeklyStats.push(
        MacroNutrientDTO.transform({
          period: periodLabel,
          calories: parseFloat(avgCalories.toFixed(2)),
          protein: calcPercent(totalProtein),
          carbs: calcPercent(totalCarbs),
          fats: calcPercent(totalFats),
          fiber: calcPercent(totalFiber),
        }),
      );
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: weeklyStats,
    };
  }
}
