import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber } from 'class-validator';

export class MacroNutrientDTO {
  @ApiProperty({
    description: 'Time period (e.g., week 1 April)',
  })
  @IsString()
  @IsNotEmpty()
  period: string;

  @ApiProperty({
    description: 'Total calories for the period',
    type: Number,
  })
  @IsNumber()
  calories: number;

  @ApiProperty({
    description: 'Total carbohydrates  for the period',
    type: Number,
  })
  @IsNumber()
  carbs: number;

  @ApiProperty({
    description: 'Total protein  for the period',
    type: Number,
  })
  @IsNumber()
  protein: number;

  @ApiProperty({
    description: 'Total fats  for the period',
    type: Number,
  })
  @IsNumber()
  fats: number;

  @ApiProperty({
    description: 'Total fiber  for the period',
    type: Number,
  })
  @IsNumber()
  fiber: number;

  static transform(object: {
    period: string;
    calories: number;
    carbs: number;
    protein: number;
    fats: number;
    fiber: number;
  }): MacroNutrientDTO {
    const transformedObj = new MacroNutrientDTO();
    transformedObj.period = object.period;
    transformedObj.calories = object.calories;
    transformedObj.carbs = object.carbs;
    transformedObj.protein = object.protein;
    transformedObj.fats = object.fats;
    transformedObj.fiber = object.fiber;

    return transformedObj;
  }
}
