import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class MealPopularityResponseDto {
  @ApiProperty({
    description: 'Most popular recipe name',
  })
  @IsString()
  @IsNotEmpty()
  recipeName: string;

  @ApiProperty({
    description: 'Most popular recipe ID',
  })
  @IsString()
  @IsNotEmpty()
  recipeId: string;

  @ApiProperty()
  count: number;
}
