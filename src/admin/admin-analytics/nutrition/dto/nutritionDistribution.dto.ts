import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsObject,
} from 'class-validator';

export class NutritionDistributionDTO {
  @ApiProperty({
    description: 'Time period (e.g., week 1 April)',
  })
  @IsString()
  @IsNotEmpty()
  period: string;

  @ApiProperty({
    description: 'Counts of each meal type for the period',
    type: Object,
  })
  @IsObject()
  @IsOptional()
  mealType?: Record<string, number>;

  @ApiProperty({
    description: 'Number of users involved in the period',
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  noOfUser: number;

  static transform(object: {
    period: string;
    mealType?: Record<string, number>;
    noOfUser: number;
  }): NutritionDistributionDTO {
    const transformedObj = new NutritionDistributionDTO();
    transformedObj.period = object.period;
    transformedObj.mealType = object.mealType;
    transformedObj.noOfUser = object.noOfUser;

    return transformedObj;
  }
}
