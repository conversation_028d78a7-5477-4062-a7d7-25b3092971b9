import { BadRequestException, Injectable } from '@nestjs/common';
import { GetMacroNutrientDTO, GetNutritionDistributionDTO } from './dto';
import {
  GetNutritionAnalyticsQueryInterface,
  NutritionAnalyticsRecordFilter,
} from './interface/getNutritionQuery.interface';
import { AdminNutritionAnalyticsUtilsService } from './nutrition-utils.service';

@Injectable()
export class AdminNutritionAnalyticsService {
  constructor(
    private readonly adminNutritionUtilsService: AdminNutritionAnalyticsUtilsService,
  ) {}

  async getUserNutritionAnalytics(
    queryFilters: GetNutritionAnalyticsQueryInterface,
  ): Promise<GetNutritionDistributionDTO> {
    const { filter } = queryFilters;

    switch (filter) {
      case NutritionAnalyticsRecordFilter.WEEKLY:
        return this.adminNutritionUtilsService.getLast7DaysUserCounts();
      case NutritionAnalyticsRecordFilter.MONTHLY:
        return this.adminNutritionUtilsService.getMonthlyNutritionDistribution();

      default:
        throw new BadRequestException('Invalid record category provided');
    }
  }

  async getUserMealTypeDistribution(
    queryFilters: GetNutritionAnalyticsQueryInterface,
  ): Promise<GetNutritionDistributionDTO> {
    const { filter } = queryFilters;
    switch (filter) {
      case NutritionAnalyticsRecordFilter.WEEKLY:
        return this.adminNutritionUtilsService.getWeeklyMealTypeDistribution();
      case NutritionAnalyticsRecordFilter.MONTHLY:
        return this.adminNutritionUtilsService.getMonthlyMealTypeDistribution();
      default:
        throw new BadRequestException('Invalid record category provided');
    }
  }

  async getPopularMeal(queryFilters: GetNutritionAnalyticsQueryInterface) {
    const { filter } = queryFilters;
    switch (filter) {
      case NutritionAnalyticsRecordFilter.WEEKLY:
        return this.adminNutritionUtilsService.getWeeklyPopularMeal();
      case NutritionAnalyticsRecordFilter.MONTHLY:
        return this.adminNutritionUtilsService.getMonthlyPopularMeal();
      default:
        throw new BadRequestException('Invalid record category provided');
    }
  }

  // macro distribution analytics

  async getMacroNutrientAnalytics(
    queryFilters: GetNutritionAnalyticsQueryInterface,
  ): Promise<GetMacroNutrientDTO> {
    const { filter } = queryFilters;

    switch (filter) {
      case NutritionAnalyticsRecordFilter.WEEKLY:
        return this.adminNutritionUtilsService.getWeeklyMacroNutrientStats();

      case NutritionAnalyticsRecordFilter.MONTHLY:
        return this.adminNutritionUtilsService.getMonthlyMacroNutrientStats();
      default:
        throw new BadRequestException('Unsupported filter');
    }
  }
}
