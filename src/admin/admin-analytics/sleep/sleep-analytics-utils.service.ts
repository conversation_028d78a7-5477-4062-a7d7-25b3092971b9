import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UserSleepRecords } from 'models/user-records/Sleep-Records';
import { User } from 'models/user';
import { GetSleepTrendsResDto } from './dto/get-sleep-trends.dto';
import { GetSleepGoalAchievedResDto } from './dto/get-goals-achieved.dto';
import { GetRecommendedSleepGoalAchievedResDto } from './dto/get-recommended-sleep-goal-achieved.dto';

@Injectable()
export class SleepAnalyticsUtilsService {
  constructor(
    @InjectModel(UserSleepRecords.name)
    private readonly sleepModel: Model<UserSleepRecords>,

    @InjectModel(User.name)
    private readonly userModel: Model<User>,
  ) {}

  async getWeeklySleepTrends(): Promise<GetSleepTrendsResDto> {
    const today = new Date();
    const startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    startDate.setUTCHours(0, 0, 0, 0);
    today.setUTCHours(23, 59, 59, 999);

    const pipeline = [
      {
        $match: {
          date: { $gte: startDate, $lte: today },
        },
      },
      {
        $project: {
          dateStr: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
          numOfHours: 1,
        },
      },
      {
        $group: {
          _id: '$dateStr',
          averageSleep: { $avg: '$numOfHours' },
        },
      },
    ];

    const results = await this.sleepModel.aggregate(pipeline);

    return GetSleepTrendsResDto.transform(
      Array.from({ length: 7 }, (_, i) => {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i + 1);
        const label = date.toISOString().slice(0, 10);
        const result = results.find((r) => r._id === label);
        return {
          label: result ? result._id : label,
          averageSleep: result ? result.averageSleep : 0,
        };
      }),
    );
  }

  async getMonthlySleepTrends(): Promise<GetSleepTrendsResDto> {
    const today = new Date();
    today.setUTCHours(23, 59, 59, 999);

    const weekPeriods = [];
    for (let i = 0; i < 4; i++) {
      const endDate = new Date(today);
      endDate.setDate(today.getDate() - i * 7);
      endDate.setUTCHours(23, 59, 59, 999);

      const startDate = new Date(endDate);
      startDate.setDate(endDate.getDate() - 6);
      startDate.setUTCHours(0, 0, 0, 0);

      weekPeriods.push({ startDate, endDate });
    }

    const oldestDate = weekPeriods[weekPeriods.length - 1].startDate;

    const pipeline = [
      {
        $match: {
          date: { $gte: oldestDate, $lte: today },
        },
      },
      {
        $project: {
          date: 1,
          numOfHours: 1,
        },
      },
    ];

    const sleepRecords = await this.sleepModel.aggregate(pipeline);

    // Process the data into week periods
    const results = weekPeriods.map(({ startDate, endDate }) => {
      const weekRecords = sleepRecords.filter(
        (record) => record.date >= startDate && record.date <= endDate,
      );
      const totalHours = weekRecords.reduce(
        (sum, record) => sum + record.numOfHours,
        0,
      );
      const averageSleep =
        weekRecords.length > 0 ? totalHours / weekRecords.length : 0;

      return {
        label: `${startDate.toISOString().slice(0, 10)} to ${endDate.toISOString().slice(0, 10)}`,
        averageSleep: averageSleep,
        numEntries: weekRecords.length,
      };
    });

    return GetSleepTrendsResDto.transform(results.reverse());
  }

  async calculateSleepGoalAchieved(
    days: number,
  ): Promise<GetSleepGoalAchievedResDto> {
    const today = new Date();
    const startDate = new Date(today.getTime() - days * 24 * 60 * 60 * 1000);
    startDate.setUTCHours(0, 0, 0, 0);
    today.setUTCHours(23, 59, 59, 999);

    const targetDates = Array.from({ length: days }, (_, i) => {
      const d = new Date(startDate);
      d.setDate(d.getDate() + i + 1);
      return d.toISOString().slice(0, 10);
    });

    const pipeline = [
      {
        $match: {
          date: { $gte: startDate, $lte: today },
        },
      },
      {
        $addFields: {
          userObjectId: { $toObjectId: '$userId' },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userObjectId',
          foreignField: '_id',
          as: 'user',
        },
      },
      {
        $unwind: '$user',
      },
      {
        $match: {
          'user.isAccountCompleted': true,
        },
      },
      {
        $addFields: {
          sleepGoal: {
            $arrayElemAt: [
              {
                $map: {
                  input: {
                    $filter: {
                      input: '$user.goals',
                      cond: { $eq: ['$$this.goal_type', 'sleep'] },
                    },
                  },
                  as: 'goal',
                  in: { $toDouble: '$$goal.selected_goal' },
                },
              },
              0,
            ],
          },
        },
      },
      {
        $project: {
          userId: 1,
          dateStr: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
          metGoal: {
            $and: [
              { $ne: ['$sleepGoal', null] },
              { $gte: ['$numOfHours', '$sleepGoal'] },
            ],
          },
        },
      },
      {
        $match: {
          metGoal: true,
        },
      },
      {
        $group: {
          _id: '$userId',
          sleepDates: { $addToSet: '$dateStr' },
        },
      },
    ];

    const results = await this.sleepModel.aggregate(pipeline);

    const totalUsers = await this.userModel.countDocuments({
      isAccountCompleted: true,
    });

    let goalAchieved = 0;

    for (const user of results) {
      const userDates = user.sleepDates.sort();
      const matchesAll = targetDates.every((targetDate) =>
        userDates.includes(targetDate),
      );
      if (matchesAll) {
        goalAchieved++;
      }
    }

    return GetSleepGoalAchievedResDto.transform({
      goalAchieved,
      totalUsers,
    });
  }

  async calculateRecommendedSleepAchieved(
    days: number,
  ): Promise<GetRecommendedSleepGoalAchievedResDto> {
    const today = new Date();
    const startDate = new Date(today.getTime() - days * 24 * 60 * 60 * 1000);
    startDate.setUTCHours(0, 0, 0, 0);
    today.setUTCHours(23, 59, 59, 999);

    const targetDates = Array.from({ length: days }, (_, i) => {
      const d = new Date(startDate);
      d.setDate(d.getDate() + i + 1);
      return d.toISOString().slice(0, 10);
    });

    const pipeline = [
      {
        $match: {
          date: { $gte: startDate, $lte: today },
          numOfHours: { $gte: +process.env.RECOMMENDED_SLEEP_HOURS },
        },
      },
      {
        $project: {
          userId: 1,
          dateStr: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
        },
      },
      {
        $group: {
          _id: '$userId',
          sleepDates: { $addToSet: '$dateStr' },
        },
      },
    ];

    const results = await this.sleepModel.aggregate(pipeline);
    const totalUsers = await this.userModel.countDocuments({
      isAccountCompleted: true,
      role: { $ne: 'admin' },
    });

    let goalAchieved = 0;

    for (const user of results) {
      const userDates = user.sleepDates.sort();
      const matchesAll = targetDates.every((targetDate) =>
        userDates.includes(targetDate),
      );
      if (matchesAll) {
        goalAchieved++;
      }
    }

    return GetRecommendedSleepGoalAchievedResDto.transform({
      goalAchieved,
      totalUsers,
    });
  }
}
