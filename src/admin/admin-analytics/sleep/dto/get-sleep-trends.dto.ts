import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export enum USER_TREND_TYPE {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export class CombinedSleepTrendLine {
  @ApiProperty({ example: '2025-05-02' })
  label: string;

  @ApiProperty({ example: 20 })
  averageSleep: number;
}

export class GetSleepTrendsResDto extends BaseResponse {
  @ApiProperty({
    example: 200,
    description:
      'Http response code. 400-Bad Request, 403 - Forbidden Exception, 404 - Not Found, 500 - Internal Server Exception',
  })
  statusCode: number;
  @ApiProperty({
    example: false,
    description: 'every time value is `false` for success response',
  })
  error: boolean;
  @ApiProperty({
    example: 'Sleep trends analytics fetched successfully',
    description: 'message',
  })
  msg: string;
  @ApiProperty({
    type: CombinedSleepTrendLine,
    isArray: true,
  })
  data: CombinedSleepTrendLine[];
  @ApiProperty({
    type: Number,
    example: 1,
    description: 'Total number of records',
  })
  total: number;

  static transform(data: any[]): GetSleepTrendsResDto {
    const transformedObj = new GetSleepTrendsResDto();

    transformedObj.data = data.map((item) => ({
      label: item.label.toString(),
      averageSleep: item.averageSleep || 0,
    }));
    transformedObj.statusCode = 200;
    transformedObj.error = false;
    transformedObj.msg = 'Sleep trends analytics fetched successfully';
    transformedObj.total = data.length;

    return transformedObj;
  }
}
