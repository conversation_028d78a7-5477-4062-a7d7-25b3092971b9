import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export enum SLEEP_FILTER {
  Daily = '24h',
  Weekly = '7d',
  Monthly = '30d',
}

export class SleepGoalAchieved {
  @ApiProperty({ example: 20 })
  goalAchieved: number;

  @ApiProperty({ example: 20 })
  totalUsers: number;
}

export class GetSleepGoalAchievedResDto extends BaseResponse {
  @ApiProperty({
    example: 200,
    description:
      'Http response code. 400-Bad Request, 403 - Forbidden Exception, 404 - Not Found, 500 - Internal Server Exception',
  })
  statusCode: number;
  @ApiProperty({
    example: false,
    description: 'every time value is `false` for success response',
  })
  error: boolean;
  @ApiProperty({
    example: 'Sleep goal achieved analytics fetched successfully',
    description: 'message',
  })
  msg: string;
  @ApiProperty({
    type: SleepGoalAchieved,
    example: SleepGoalAchieved,
  })
  data: SleepGoalAchieved;

  static transform(data: any): GetSleepGoalAchievedResDto {
    const transformedObj = new GetSleepGoalAchievedResDto();
    transformedObj.data = {
      goalAchieved: data.goalAchieved || 0,
      totalUsers: data.totalUsers || 0,
    };

    transformedObj.statusCode = 200;
    transformedObj.error = false;
    transformedObj.msg = 'Sleep goal achieved analytics fetched successfully';

    return transformedObj;
  }
}
