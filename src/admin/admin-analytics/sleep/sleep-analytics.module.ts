import { Modu<PERSON> } from '@nestjs/common';
import { SleepAnalyticsService } from './sleep-analytics.service';
import { SleepController } from './sleep-analytics.controller';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import {
  UserSleepRecords,
  UserSleepRecordsSchema,
} from 'models/user-records/Sleep-Records';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'models/user';
import { SleepAnalyticsUtilsService } from './sleep-analytics-utils.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserSleepRecords.name, schema: UserSleepRecordsSchema },
      { name: User.name, schema: UserSchema },
    ]),
    AuthModule,
    CommonModule,
    RepoModule,
  ],
  controllers: [SleepController],
  providers: [SleepAnalyticsService, SleepAnalyticsUtilsService],
})
export class SleepAnalyticsModule {}
