import { Injectable } from '@nestjs/common';
import { USER_TREND_TYPE } from './dto/get-sleep-trends.dto';
import { SLEEP_FILTER } from './dto/get-goals-achieved.dto';
import { SleepAnalyticsUtilsService } from './sleep-analytics-utils.service';

@Injectable()
export class SleepAnalyticsService {
  constructor(
    private readonly sleepAnalyticsUtilsService: SleepAnalyticsUtilsService,
  ) {}

  getSleepTrends(period: USER_TREND_TYPE) {
    if (period === USER_TREND_TYPE.WEEKLY) {
      return this.sleepAnalyticsUtilsService.getWeeklySleepTrends();
    } else if (period === USER_TREND_TYPE.MONTHLY) {
      return this.sleepAnalyticsUtilsService.getMonthlySleepTrends();
    } else {
      throw new Error('Invalid period');
    }
  }
  getSleepGoalAchieved(period: SLEEP_FILTER) {
    if (period === SLEEP_FILTER.Daily) {
      return this.sleepAnalyticsUtilsService.calculateSleepGoalAchieved(1);
    } else if (period === SLEEP_FILTER.Weekly) {
      return this.sleepAnalyticsUtilsService.calculateSleepGoalAchieved(7);
    } else if (period === SLEEP_FILTER.Monthly) {
      return this.sleepAnalyticsUtilsService.calculateSleepGoalAchieved(30);
    } else {
      throw new Error('Invalid period');
    }
  }
  getRecommendedSleepAchieved(period: SLEEP_FILTER) {
    if (period === SLEEP_FILTER.Daily) {
      return this.sleepAnalyticsUtilsService.calculateRecommendedSleepAchieved(
        1,
      );
    } else if (period === SLEEP_FILTER.Weekly) {
      return this.sleepAnalyticsUtilsService.calculateRecommendedSleepAchieved(
        7,
      );
    } else if (period === SLEEP_FILTER.Monthly) {
      return this.sleepAnalyticsUtilsService.calculateRecommendedSleepAchieved(
        30,
      );
    } else {
      throw new Error('Invalid period');
    }
  }
}
