import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { SleepAnalyticsService } from './sleep-analytics.service';
import { AuthGuard } from 'src/middlewares';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  GetSleepTrendsResDto,
  USER_TREND_TYPE,
} from './dto/get-sleep-trends.dto';
import { ErrorResponse } from 'src/utils/responses';
import { Authority } from 'src/utils/decorators';
import {
  GetSleepGoalAchievedResDto,
  SLEEP_FILTER,
} from './dto/get-goals-achieved.dto';
import {
  GetRecommendedSleepGoalAchievedResDto,
  RECOMMENDED_SLEEP_FILTER,
} from './dto/get-recommended-sleep-goal-achieved.dto';

@ApiTags('Admin-Analytics')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin/analytics/sleep')
export class SleepController {
  constructor(private readonly sleepService: SleepAnalyticsService) {}

  @ApiOperation({
    summary: 'Get average user sleep trends (weekly or monthly)',
  })
  @ApiParam({
    name: 'period',
    enum: USER_TREND_TYPE,
    required: true,
    description: 'Time period to filter the sleep trends data',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved average user sleep trends',
    type: GetSleepTrendsResDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid type provided',
    type: ErrorResponse,
  })
  @Authority()
  @Get('trends/:period')
  getSleepTrends(@Param('period') period: USER_TREND_TYPE) {
    return this.sleepService.getSleepTrends(period);
  }

  @ApiOperation({
    summary:
      'Get no of users who achieved their sleep goal continuously (daily, weekly, monthly)',
  })
  @ApiParam({
    name: 'period',
    enum: SLEEP_FILTER,
    required: true,
    description: 'Time period to filter the sleep goal data',
  })
  @ApiResponse({
    status: 200,
    description:
      'Successfully retrieved no of users who achieved their sleep goal continuously',
    type: GetSleepGoalAchievedResDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid type provided',
    type: ErrorResponse,
  })
  @Authority()
  @Get('goals_achieved/:period')
  getSleepGoalAchieved(@Param('period') period: SLEEP_FILTER) {
    return this.sleepService.getSleepGoalAchieved(period);
  }

  @ApiOperation({
    summary:
      'Get no of users who achieved their recommended sleep goal continuously (daily, weekly, monthly)',
  })
  @ApiParam({
    name: 'period',
    enum: RECOMMENDED_SLEEP_FILTER,
    required: true,
    description: 'Time period to filter the sleep goal data',
  })
  @ApiResponse({
    status: 200,
    description:
      'Successfully retrieved no of users who achieved their recommended sleep goal continuously',
    type: GetRecommendedSleepGoalAchievedResDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid type provided',
    type: ErrorResponse,
  })
  @Authority()
  @Get('recommended_sleep_achieved/:period')
  getRecommendedSleepAchieved(@Param('period') period: SLEEP_FILTER) {
    return this.sleepService.getRecommendedSleepAchieved(period);
  }
}
