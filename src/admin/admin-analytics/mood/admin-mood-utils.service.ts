import { Injectable, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { DailyMoodAverage } from 'models/user-records/Mood-Records';

import { Model } from 'mongoose';
import { format, addDays, subDays } from 'date-fns';
import { MoodDistributionDTO } from './dto';

@Injectable()
export class AdminMoodAnalyticsUtilsService {
  constructor(
    @InjectModel(DailyMoodAverage.name)
    private readonly dailyMoodAverageModel: Model<DailyMoodAverage>,
  ) {}

  private readonly moodWeights = {
    happy: 5,
    'moderately happy': 4,
    sad: 3,
    irritated: 2,
    anxious: 1,
  };

  getScoreLabelMappings() {
    const moodScoreToLabel = Object.fromEntries(
      Object.entries(this.moodWeights).map(([label, score]) => [score, label]),
    );

    return { moodScoreToLabel };
  }

  async getGlobalDailyMoodDistributions() {
    const now = new Date();
    now.setHours(0, 0, 0, 0); // Start of today

    const startDate = new Date(now);
    const endDate = addDays(startDate, 1); // End of today

    // Initialize mood counts
    const moodCounts: Record<string, number> = {
      happy: 0,
      'moderately happy': 0,
      irritated: 0,
      sad: 0,
      anxious: 0,
    };

    const validMoods = [
      'happy',
      'moderately happy',
      'irritated',
      'sad',
      'anxious',
    ];

    const records = await this.dailyMoodAverageModel
      .find({
        createdAt: {
          $gte: startDate,
          $lt: endDate,
        },
      })
      .lean();

    let totalUserCount = 0;

    const { moodScoreToLabel } = this.getScoreLabelMappings();

    records.forEach((record) => {
      const moodType = moodScoreToLabel[record.moodTypeScore]?.toLowerCase();
      if (validMoods.includes(moodType)) {
        moodCounts[moodType] += 1;
        totalUserCount += 1;
      }
    });

    // Calculate percentages
    const moodPercentages: Record<string, number> = {};
    for (const mood in moodCounts) {
      moodPercentages[mood] =
        totalUserCount > 0
          ? +((moodCounts[mood] / totalUserCount) * 100).toFixed(2)
          : 0;
    }

    const todayStr = format(now, 'yyyy-MM-dd');
    const resp = MoodDistributionDTO.transform({
      period: todayStr,
      moodCounts,
      moodPercentages,
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Global Daily Mood Distribution Fetched Successfully',
      total: totalUserCount,
      data: [resp],
    };
  }

  async getGlobalWeeklyMoodDistributions() {
    const now = new Date();
    now.setHours(0, 0, 0, 0); // Start of today

    const last7Days: string[] = [];
    const groupedMoodCounts: Record<string, Record<string, number>> = {};

    for (let i = 6; i >= 0; i--) {
      const date = subDays(now, i);
      const dateStr = format(date, 'yyyy-MM-dd');
      last7Days.push(dateStr);
      groupedMoodCounts[dateStr] = {
        happy: 0,
        'moderately happy': 0,
        irritated: 0,
        sad: 0,
        anxious: 0,
      };
    }

    const startDate = subDays(now, 6);
    const endDate = addDays(now, 1);

    const records = await this.dailyMoodAverageModel
      .find({
        createdAt: {
          $gte: startDate,
          $lt: endDate,
        },
      })
      .lean();

    const validMoods = [
      'happy',
      'moderately happy',
      'irritated',
      'sad',
      'anxious',
    ];

    // Count moods per day
    const totalUserCounts: Record<string, number> = {};
    const { moodScoreToLabel } = this.getScoreLabelMappings();
    records.forEach((record) => {
      const date = new Date(record.year, record.month - 1, record.date);
      const dateStr = format(date, 'yyyy-MM-dd');

      const moodType = moodScoreToLabel[record.moodTypeScore]?.toLowerCase();
      if (validMoods.includes(moodType)) {
        if (groupedMoodCounts[dateStr]) {
          groupedMoodCounts[dateStr][moodType] += 1;
          totalUserCounts[dateStr] = (totalUserCounts[dateStr] || 0) + 1;
        }
      }
    });

    // Prepare final response
    const responseRecords = last7Days.map((dateStr) => {
      const counts = groupedMoodCounts[dateStr];
      const total = totalUserCounts[dateStr] || 0;

      const percentages: Record<string, number> = {};
      for (const mood in counts) {
        percentages[mood] =
          total > 0 ? +((counts[mood] / total) * 100).toFixed(2) : 0;
      }

      return MoodDistributionDTO.transform({
        period: dateStr,
        moodCounts: counts,
        moodPercentages: percentages,
      });
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Global Weekly Mood Distributions Fetched Successfully',
      total: responseRecords.length,
      data: responseRecords,
    };
  }

  async getGlobalMonthlyMoodDistributions() {
    const now = new Date();
    now.setHours(23, 59, 59, 999);

    const endDateLocal = new Date(now);
    const startDateLocal = subDays(endDateLocal, 34); // 35 days ago

    const records = await this.dailyMoodAverageModel
      .find({
        createdAt: {
          $gte: startDateLocal,
          $lte: endDateLocal,
        },
      })
      .lean();

    const validMoods = [
      'happy',
      'moderately happy',
      'irritated',
      'sad',
      'anxious',
    ];
    const responseRecords = [];

    for (let i = 0; i < 5; i++) {
      const weekStartLocal = addDays(startDateLocal, i * 7);
      let weekEndLocal = addDays(weekStartLocal, 6);

      // Last week ends at today
      if (i === 4) {
        weekEndLocal = endDateLocal;
      }

      const weekRecords = records.filter((r) => {
        const createdAt = new Date(r.createdAt);
        return createdAt >= weekStartLocal && createdAt <= weekEndLocal;
      });

      // Initialize mood counts
      const moodCounts: Record<string, number> = {
        happy: 0,
        'moderately happy': 0,
        irritated: 0,
        sad: 0,
        anxious: 0,
      };

      let totalUserCount = 0;

      const { moodScoreToLabel } = this.getScoreLabelMappings();
      weekRecords.forEach((record) => {
        const moodType = moodScoreToLabel[record.moodTypeScore]?.toLowerCase();
        if (validMoods.includes(moodType)) {
          moodCounts[moodType] += 1;
          totalUserCount += 1;
        }
      });

      // Calculate percentages
      const moodPercentages: Record<string, number> = {};
      for (const mood in moodCounts) {
        moodPercentages[mood] =
          totalUserCount > 0
            ? +((moodCounts[mood] / totalUserCount) * 100).toFixed(2)
            : 0;
      }

      const periodLabel = `${format(weekStartLocal, 'yyyy-MM-dd')} to ${format(weekEndLocal, 'yyyy-MM-dd')}`;

      responseRecords.push({
        period: periodLabel,
        moodCounts,
        moodPercentages,
      });
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Global Monthly Mood Distributions Fetched Successfully',
      total: responseRecords.length,
      data: responseRecords,
    };
  }
}
