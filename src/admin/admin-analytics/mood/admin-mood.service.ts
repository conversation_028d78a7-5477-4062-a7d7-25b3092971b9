import { BadRequestException, Injectable } from '@nestjs/common';
import { GetMoodDistributionDTO } from './dto';

import { AdminMoodAnalyticsUtilsService } from './admin-mood-utils.service';
import {
  AdminAnalyticsRecordFilter,
  GetAdminAnalyticsQueryInterface,
} from './interface';

@Injectable()
export class AdminMoodAnalyticsService {
  constructor(
    private readonly adminMoodAnalyticsUtilsService: AdminMoodAnalyticsUtilsService,
  ) {}

  async getUserMoodAnalytics(
    queryFilters: GetAdminAnalyticsQueryInterface,
  ): Promise<GetMoodDistributionDTO> {
    const { filter } = queryFilters;

    const currentDate = new Date();
    currentDate.setHours(23, 59, 59, 999);

    switch (filter) {
      case AdminAnalyticsRecordFilter.DAILY:
        return this.adminMoodAnalyticsUtilsService.getGlobalDailyMoodDistributions();
      case AdminAnalyticsRecordFilter.WEEKLY:
        return this.adminMoodAnalyticsUtilsService.getGlobalWeeklyMoodDistributions();
      case AdminAnalyticsRecordFilter.MONTHLY:
        return this.adminMoodAnalyticsUtilsService.getGlobalMonthlyMoodDistributions();
      default:
        throw new BadRequestException('Invalid record category provided');
    }
  }
}
