import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { MoodDistributionDTO } from './moodDistribution.dto';

export class GetMoodDistributionDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of mood records',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'message',
    example: 'Global Daily Mood Distribution Fetched Successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'List of mood records',
    type: [MoodDistributionDTO],
  })
  data: MoodDistributionDTO[];
}
