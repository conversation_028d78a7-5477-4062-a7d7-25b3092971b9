import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsObject } from 'class-validator';

export class MoodDistributionDTO {
  @ApiProperty({
    description: 'Time period (e.g., week 1 April)',
  })
  @IsString()
  @IsNotEmpty()
  period: string;

  @ApiProperty({
    description: 'Counts of each mood type for the period',
    type: Object,
  })
  @IsObject()
  @IsOptional()
  moodCounts?: Record<string, number>;

  @ApiProperty({
    description: 'Percentages of each mood type for the period',
    type: Object,
  })
  @IsObject()
  @IsOptional()
  moodPercentages?: Record<string, number>;

  static transform(object: {
    period: string;
    moodCounts?: Record<string, number>;
    moodPercentages?: Record<string, number>;
  }): MoodDistributionDTO {
    const transformedObj = new MoodDistributionDTO();
    transformedObj.period = object.period;
    transformedObj.moodCounts = object.moodCounts;
    transformedObj.moodPercentages = object.moodPercentages;

    return transformedObj;
  }
}
