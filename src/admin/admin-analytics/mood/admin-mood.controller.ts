import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { AuthGuard } from 'src/middlewares';

import { Authority } from 'src/utils/decorators';
import { AdminMoodAnalyticsService } from './admin-mood.service';
import { GetMoodDistributionDTO } from './dto';
import { GetAdminAnalyticsQueryInterface } from './interface';

@ApiTags('User-Mood')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class AdminMoodAnalyticsController {
  constructor(private readonly moodService: AdminMoodAnalyticsService) {}

  @ApiResponse({
    status: 200,
    description: 'User mood records retrieved successfully.',
    type: GetMoodDistributionDTO,
  })
  @Authority()
  @Get('admin/analytics/mood')
  async getMoodRecordsAnalytics(
    @Query() queryFilters: GetAdminAnalyticsQueryInterface,
  ): Promise<GetMoodDistributionDTO> {
    return this.moodService.getUserMoodAnalytics(queryFilters);
  }
}
