import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export enum DEVICE_USAGE_PERIOD {
  CURRENT_DAY = '24h',
  LAST_SEVEN_DAYS = '7d',
  LAST_THIRTY_DAYS = '30d',
}

export class getDeviceUsageStatsResDto extends BaseResponse {
  @ApiProperty({
    example: 50,
    description:
      'Number of active users using the device in the given time period',
  })
  activelyUsingDevice: number;

  @ApiProperty({
    example: 100,
    description: 'Total Number of users connected with the device',
  })
  totalConnectedUsers: number;
}
