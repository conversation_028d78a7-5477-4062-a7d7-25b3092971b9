import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export enum USER_TREND_TYPE {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export class CombinedTrendLine {
  @ApiProperty({ example: '2025-05-02' })
  label: string;

  @ApiProperty({ example: 20 })
  activeUsers: number;

  @ApiProperty({ example: 12 })
  registeredUsers: number;
}

export class GetUserEngagementTrendsResDto extends BaseResponse {
  @ApiProperty({ type: [CombinedTrendLine] })
  data: CombinedTrendLine[];

  @ApiProperty({ example: 1200 })
  totalUsers: number;
}