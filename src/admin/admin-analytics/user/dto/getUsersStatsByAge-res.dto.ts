export enum ACTIVITY_PERIOD {
  CURRENT_DAY = '24h',
  LAST_SEVEN_DAYS = '7d',
  LAST_THIRTY_DAYS = '30d',
}

export enum AGE_GROUP_LABEL {
  CHILD = 'child',
  TEEN = 'teen',
  YOUNG_ADULT = 'youngAdult',
  ADULT = 'adult',
  SENIOR = 'senior',
}

export class AgeGroupStats {
  activeUsers: number;
  registeredUsers: number;
}

export class UserAgeGroupStatsResDto {
  [AGE_GROUP_LABEL.CHILD]: AgeGroupStats;
  [AGE_GROUP_LABEL.TEEN]: AgeGroupStats;
  [AGE_GROUP_LABEL.YOUNG_ADULT]: AgeGroupStats;
  [AGE_GROUP_LABEL.ADULT]: AgeGroupStats;
  [AGE_GROUP_LABEL.SENIOR]: AgeGroupStats;
}
