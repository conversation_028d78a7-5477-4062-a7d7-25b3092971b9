import { Injectable, BadRequestException } from '@nestjs/common';

import {
  GetUserEngagementTrendsResDto,
  CombinedTrendLine,
  USER_TREND_TYPE,
} from './dto/getActiveUsers-res.dto';
import {
  DEVICE_USAGE_PERIOD,
  getDeviceUsageStatsResDto,
} from './dto/getDeviceUsageStats-res.dto';
import {
  UserAgeGroupStatsResDto,
  ACTIVITY_PERIOD,
} from './dto/getUsersStatsByAge-res.dto';
import { AdminAnalyticsUtilsService } from './admin-analytics-utils.service';

@Injectable()
export class AdminAnalyticsService {
  constructor(private readonly analyticsUtils: AdminAnalyticsUtilsService) {}

  async getUserEngagementTrends(
    type: USER_TREND_TYPE,
  ): Promise<GetUserEngagementTrendsResDto> {
    let activeData: CombinedTrendLine[];
    let registeredData: CombinedTrendLine[];

    if (type === USER_TREND_TYPE.WEEKLY) {
      [activeData, registeredData] = await Promise.all([
        this.analyticsUtils.getWeeklyActiveUsers(),
        this.analyticsUtils.getWeeklyRegisteredUsers(),
      ]);
    } else if (type === USER_TREND_TYPE.MONTHLY) {
      [activeData, registeredData] = await Promise.all([
        this.analyticsUtils.getMonthlyActiveUsers(),
        this.analyticsUtils.getMonthlyRegisteredUsers(),
      ]);
    } else {
      throw new BadRequestException('Invalid type. Use "weekly" or "monthly".');
    }

    const data: CombinedTrendLine[] = activeData.map((active, i) => ({
      label: active.label,
      activeUsers: active.activeUsers,
      registeredUsers: registeredData[i]?.registeredUsers || 0,
    }));

    const totalUsers = await this.analyticsUtils.getTotalRegisteredUsers();

    return {
      error: false,
      statusCode: 200,
      data,
      totalUsers,
    };
  }

  async getDeviceUsageStats(
    period: string,
  ): Promise<getDeviceUsageStatsResDto> {
    switch (period) {
      case DEVICE_USAGE_PERIOD.CURRENT_DAY:
        return this.analyticsUtils.calculateDeviceUsageCurrentDay();
      case DEVICE_USAGE_PERIOD.LAST_SEVEN_DAYS:
        return this.analyticsUtils.calculateDeviceUsageForNDays(7);
      case DEVICE_USAGE_PERIOD.LAST_THIRTY_DAYS:
        return this.analyticsUtils.calculateDeviceUsageForNDays(30);
      default:
        throw new BadRequestException(
          `Invalid period. Allowed values: ${Object.values(DEVICE_USAGE_PERIOD).join(', ')}`,
        );
    }
  }

  async getUserAgeGroupStats(
    period: ACTIVITY_PERIOD,
  ): Promise<UserAgeGroupStatsResDto> {
    return this.analyticsUtils.calculateUserAgeGroupStats(period);
  }
}
