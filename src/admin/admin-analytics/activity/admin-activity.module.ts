import { Module } from '@nestjs/common';
import { AdminActivityService } from './admin-activity.service';
import { AdminActivityController } from './admin-activity.controller';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AdminActivityUtilsService } from './admin-activity-utils.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [HttpModule, AuthModule, CommonModule, RepoModule, ConfigModule],
  controllers: [AdminActivityController],
  providers: [AdminActivityService, AdminActivityUtilsService],
})
export class AdminActivityModule {}
