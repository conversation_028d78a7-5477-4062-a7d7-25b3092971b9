import { Injectable } from '@nestjs/common';

import { AdminActivityUtilsService } from './admin-activity-utils.service';
import { AVERAGE_CALORIES_BURNED_PERIOD } from './dto/averageCaloriesBurned.dto';
import { POPULAR_ACTIVITIES_PERIOD } from './dto';

@Injectable()
export class AdminActivityService {
  constructor(
    private readonly adminActivityUtilsService: AdminActivityUtilsService,
  ) {}
  getAverageCaloriesBurnedTrends(period: AVERAGE_CALORIES_BURNED_PERIOD) {
    return this.adminActivityUtilsService.getAverageCaloriesBurnedTrends(
      period,
    );
  }

  getPopularActivities(period: POPULAR_ACTIVITIES_PERIOD) {
    return this.adminActivityUtilsService.getPopularActivities(period);
  }
}
