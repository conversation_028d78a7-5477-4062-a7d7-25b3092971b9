import { Injectable } from '@nestjs/common';
import { MicroserviceHttpClientService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';

import {
  AverageCaloriesBurnedDTO,
  GetAverageCaloriesBurnedResDTO,
  GetPopularActivitiesResDTO,
  POPULAR_ACTIVITIES_PERIOD,
  PopularActivitiesDTO,
} from './dto';
import { AVERAGE_CALORIES_BURNED_PERIOD } from './dto/averageCaloriesBurned.dto';

@Injectable()
export class AdminActivityUtilsService {
  private readonly apiKey: string;
  private readonly MICROSERVICE_URL: string;

  constructor(
    private readonly microserviceClient: MicroserviceHttpClientService,
    private readonly configService: ConfigService,
  ) {
    this.MICROSERVICE_URL = `${this.configService.get<string>('HEALTH_MICROSERVICE_URL')}`;
    this.apiKey = this.configService.get<string>('HEALTH_MICROSERVICE_API_KEY');
  }

  async getAverageCaloriesBurnedTrends(
    period: AVERAGE_CALORIES_BURNED_PERIOD,
  ): Promise<GetAverageCaloriesBurnedResDTO> {
    const data = await this.microserviceClient.get(
      this.apiKey,
      `${this.MICROSERVICE_URL}/analytics/burned_calories?filter=${period}`,
    );

    return {
      error: false,
      statusCode: 200,
      data: (data?.resp?.data || []).map((item) =>
        AverageCaloriesBurnedDTO.transform(item),
      ),
    };
  }

  async getPopularActivities(
    period: POPULAR_ACTIVITIES_PERIOD,
  ): Promise<GetPopularActivitiesResDTO> {
    const data = await this.microserviceClient.get(
      this.apiKey,
      `${this.MICROSERVICE_URL}/analytics/popular_activities?filter=${period}`,
    );

    if (data.error) {
      throw new Error(
        data?.resp?.data?.message || 'Something went wrong, Try again later...',
      );
    }

    return {
      error: false,
      statusCode: 200,
      data: (data?.resp?.data || []).map((item) =>
        PopularActivitiesDTO.transform(item),
      ),
    };
  }
}
