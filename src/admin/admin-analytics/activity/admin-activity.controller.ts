import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AdminActivityService } from './admin-activity.service';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Authority } from 'src/utils/decorators';
import { AVERAGE_CALORIES_BURNED_PERIOD } from './dto/averageCaloriesBurned.dto';
import {
  GetAverageCaloriesBurnedResDTO,
  POPULAR_ACTIVITIES_PERIOD,
} from './dto';

@ApiTags('Admin-Analytics')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin/analytics')
export class AdminActivityController {
  constructor(private readonly activityService: AdminActivityService) {}

  @Authority()
  @Get('/burned_calories')
  async getAverageCaloriesBurned(
    @Query('filter') query: AVERAGE_CALORIES_BURNED_PERIOD,
  ): Promise<GetAverageCaloriesBurnedResDTO> {
    return this.activityService.getAverageCaloriesBurnedTrends(query);
  }

  @Authority()
  @Get('popular_activities')
  getPopularActivities(@Query('filter') period: POPULAR_ACTIVITIES_PERIOD) {
    return this.activityService.getPopularActivities(period);
  }
}
