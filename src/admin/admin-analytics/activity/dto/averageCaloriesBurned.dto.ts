import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsNumber, Min } from 'class-validator';
import { Transform } from 'class-transformer';

export enum AVERAGE_CALORIES_BURNED_PERIOD {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export class AverageCaloriesBurnedDTO {
  @ApiProperty({ description: 'Time period of the record (e.g., 2025-06-10)' })
  @IsDateString()
  @IsNotEmpty()
  period: string;

  @ApiProperty({ description: 'Average calories burned for the time period' })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  averageCaloriesBurned: number;

  static transform(object: any): AverageCaloriesBurnedDTO {
    const transformedObj = new AverageCaloriesBurnedDTO();
    transformedObj.period = object.period;
    transformedObj.averageCaloriesBurned = object.averageCaloriesBurned;
    return transformedObj;
  }
}
