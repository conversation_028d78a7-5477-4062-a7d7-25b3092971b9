import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, Min, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export enum POPULAR_ACTIVITIES_PERIOD {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export class PopularActivitiesDTO {
  @ApiProperty({ description: 'Activity name' })
  @IsString()
  @IsNotEmpty()
  activityName: string;

  @ApiProperty({ description: 'Number of times the activity was performed' })
  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  count: number;

  static transform(object: any): PopularActivitiesDTO {
    const transformedObj = new PopularActivitiesDTO();
    transformedObj.activityName = object.activityName;
    transformedObj.count = Number(object.count);
    return transformedObj;
  }
}
