import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { AverageCaloriesBurnedDTO } from './averageCaloriesBurned.dto';

export class GetAverageCaloriesBurnedResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Average calories burned records for a specific period',
    type: [AverageCaloriesBurnedDTO],
  })
  data: AverageCaloriesBurnedDTO[];
}
