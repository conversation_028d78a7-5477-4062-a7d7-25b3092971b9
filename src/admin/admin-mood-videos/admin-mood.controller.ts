import {
  Controller,
  Get,
  Query,
  Param,
  Post,
  Body,
  Put,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { Authority } from 'src/utils/decorators';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminMoodService } from './admin-mood.service';
import {
  GetAllMoodMediaResDTO,
  GetSingleMoodMediaResDTO,
  CreateMoodMediaResDTO,
  DeleteMoodMediaResDTO,
  UpdateMoodMediaResDTO,
  CreateMoodMediaReqDTO,
  UpdateMoodMediaReqDTO,
} from './dto';
import { ErrorResponse } from 'src/utils/responses';
import { AuthGuard } from 'src/middlewares';
import { getAllMoodMediaQueryInterface } from './interfaces';

@ApiTags('Admin-Mood-Videos')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('/admin/mood_videos')
export class AdminMoodController {
  constructor(private readonly adminMoodService: AdminMoodService) {}

  // Get All Mood Media
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the mood media.',
    type: GetAllMoodMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    description: 'The page number for pagination (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @ApiQuery({
    name: 'page',
    description: 'The title for searching (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @Authority()
  @Get()
  async getAllMoodMedias(
    @Query() filterQueries: getAllMoodMediaQueryInterface,
  ) {
    return this.adminMoodService.getAllMoodMedia(filterQueries);
  }

  // Get Single Mood Media
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the mood media.',
    type: GetSingleMoodMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Get('/:moodMediaId')
  async getSingleMoodMedia(@Param('moodMediaId') id: string) {
    return this.adminMoodService.getSingleMoodMedia(id);
  }

  // Create Mood Media
  @ApiResponse({
    status: 201,
    description: 'Successfully created mood media.',
    type: CreateMoodMediaResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @Authority()
  @Post()
  async createMoodMedia(@Body() createBody: CreateMoodMediaReqDTO) {
    return this.adminMoodService.createMoodMedia(createBody);
  }

  // Update Mood Media
  @ApiResponse({
    status: 200,
    description: 'Successfully updated the mood media.',
    type: UpdateMoodMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Put('/:moodMediaId')
  async updateMoodMedia(
    @Param('moodMediaId') id: string,
    @Body() updateBody: UpdateMoodMediaReqDTO,
  ) {
    return this.adminMoodService.updateMoodMedia(id, updateBody);
  }

  // Delete Mood Media
  @ApiResponse({
    status: 200,
    description: 'Successfully deleted the mood media.',
    type: DeleteMoodMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Delete('/:moodMediaId')
  async deleteMoodMedia(@Param('moodMediaId') id: string) {
    return this.adminMoodService.deleteMoodMedia(id);
  }
}
