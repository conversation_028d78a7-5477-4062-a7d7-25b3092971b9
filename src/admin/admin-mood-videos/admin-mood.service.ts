import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { MoodMedia } from 'models/mood';
import { Model } from 'mongoose';
import { UtilsService } from 'src/common/services';
import { getAllMoodMediaQueryInterface } from './interfaces';
import {
  GetAllMoodMediaResDTO,
  MoodMediaDTO,
  CreateMoodMediaResDTO,
  DeleteMoodMediaResDTO,
  GetSingleMoodMediaResDTO,
  UpdateMoodMediaResDTO,
  CreateMoodMediaReqDTO,
  UpdateMoodMediaReqDTO,
} from './dto';

@Injectable()
export class AdminMoodService {
  constructor(
    @InjectModel(MoodMedia.name) private moodMediaModel: Model<MoodMedia>,
    private readonly utilsService: UtilsService,
  ) {}

  async getAllMoodMedia(
    queryFilters: getAllMoodMediaQueryInterface,
  ): Promise<GetAllMoodMediaResDTO> {
    const { page, title, moodType, hungerLevel } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };

    if (title) {
      query.title = { $regex: new RegExp(title, 'i') };
    }

    if (moodType) {
      query.moodType = { $eq: moodType };
    }

    if (hungerLevel) {
      query.hungerLevel = { $eq: hungerLevel };
    }

    const moodMedias = await this.moodMediaModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.moodMediaModel.countDocuments(query);

    const moodMediaResp = moodMedias.map((item) =>
      MoodMediaDTO.transform(item),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: moodMediaResp.length,
      moodMedias: moodMediaResp,
    };
  }

  async getSingleMoodMedia(id: string): Promise<GetSingleMoodMediaResDTO> {
    const moodMedia = await this.moodMediaModel.findById(id).exec();
    if (!moodMedia || moodMedia.isDeleted) {
      throw new NotFoundException('Mood Video not found');
    }

    const resp = MoodMediaDTO.transform(moodMedia);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      moodMedia: resp,
    };
  }

  async createMoodMedia(
    moodMediaData: CreateMoodMediaReqDTO,
  ): Promise<CreateMoodMediaResDTO> {
    const { title, hungerLevel, moodType, videoUrl, description, isPublished } =
      moodMediaData;

    const createData: any = {
      title,
      hungerLevel,
      moodType,
      description,
      isPublished,
      videoUrl,
    };

    const newMoodMedia = await this.moodMediaModel.create(createData);

    const resp = MoodMediaDTO.transform(newMoodMedia);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Mood Video created successfully !!',
      moodMedia: resp,
    };
  }

  async updateMoodMedia(
    id: string,
    updateBody: UpdateMoodMediaReqDTO,
  ): Promise<UpdateMoodMediaResDTO> {
    const { title, description, isPublished } = updateBody;

    const media = await this.moodMediaModel.findById(id);

    if (!media || media.isDeleted) {
      throw new NotFoundException('Mood Video not found');
    }

    const updateData: any = {};

    if (title) updateData.title = title;
    if (description) updateData.description = description;
    if (isPublished !== undefined) updateData.isPublished = isPublished;

    const updatedMoodMedia = await this.moodMediaModel
      .findByIdAndUpdate(id, updateData, { new: true, runValidators: true })
      .exec();

    const resp = MoodMediaDTO.transform(updatedMoodMedia);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Updated Mood Video Successfully !!',
      moodMedia: resp,
    };
  }

  async deleteMoodMedia(id: string): Promise<DeleteMoodMediaResDTO> {
    const media = await this.moodMediaModel.findById(id);

    if (!media || media.isDeleted) {
      throw new NotFoundException('Mood Video not found');
    }

    const deletedMedia = await this.moodMediaModel
      .findByIdAndUpdate(
        id,
        { isDeleted: true },
        { new: true, runValidators: true },
      )
      .exec();

    const resp = MoodMediaDTO.transform(deletedMedia);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Mood Video deleted successfully',
      moodMedia: resp,
    };
  }
}
