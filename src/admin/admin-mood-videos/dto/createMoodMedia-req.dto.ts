import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsString,
  ValidateIf,
} from 'class-validator';
import { HUNGER_LEVELS, MOOD_TYPES } from 'models/user-records/Mood-Records';

export class CreateMoodMediaReqDTO {
  @ApiProperty({
    description: 'Title of the mood media',
    example: 'Happy Moments',
  })
  @IsString({ message: 'Title must be a string' })
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Description of the mood media',
    example: 'A collection of happy moments captured in time.',
  })
  @IsString({ message: 'Description must be a string' })
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Publication status of the mood media',
    example: true,
  })
  @IsBoolean({ message: 'isPublished must be a boolean' })
  @IsNotEmpty()
  @Transform(({ value }) => value === 'true' || value === true)
  isPublished: boolean;

  @ApiProperty({
    description: 'Type of mood',
    enum: MOOD_TYPES,
    example: MOOD_TYPES.HAPPY,
  })
  @IsEnum(MOOD_TYPES, { message: 'Invalid mood type' })
  @IsNotEmpty()
  moodType: MOOD_TYPES;

  @ApiProperty({
    description: 'Level of hunger',
    enum: HUNGER_LEVELS,
    example: HUNGER_LEVELS.HIGH,
  })
  @IsEnum(HUNGER_LEVELS, { message: 'Invalid hunger level' })
  @IsNotEmpty()
  hungerLevel: HUNGER_LEVELS;

  @ApiPropertyOptional({
    description: 'URL of the video (if applicable)',
    example: 'https://example.com/video.mp4',
  })
  @ValidateIf((obj) => obj.isEmbeddedUrl === true) // Only validate if isEmbeddedUrl is true
  @IsString({ message: 'videoUrl must be a string' })
  @IsNotEmpty()
  videoUrl: string;
}
