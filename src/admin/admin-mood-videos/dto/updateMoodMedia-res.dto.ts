import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { MoodMediaDTO } from './moodMedia.dto';

export class UpdateMoodMediaResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Response message indicating the result of the update operation',
    example: 'Mood media updated successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Updated mood media object',
    type: MoodMediaDTO,
  })
  moodMedia: MoodMediaDTO;
}
