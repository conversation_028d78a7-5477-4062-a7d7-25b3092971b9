import {
  IsBoolean,
  IsEnum,
  IsString,
  IsUrl,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { MoodMedia } from 'models/mood';
import { HUNGER_LEVELS, MOOD_TYPES } from 'models/user-records/Mood-Records';
import { Types } from 'mongoose';

export class MoodMediaDTO {
  @ApiProperty({
    description: 'Id of the mood media',
    example: '1',
  })
  @IsNotEmpty({ message: 'Id is required' })
  id: Types.ObjectId;

  @ApiProperty({
    description: 'Title of the mood media',
    example: 'Relaxing Music',
  })
  @IsString({ message: 'Title must be a string' })
  @IsNotEmpty({ message: 'Title is required' })
  title: string;

  @ApiProperty({
    description: 'Description of the mood media',
    example: 'A collection of happy moments captured in time.',
  })
  @IsString({ message: 'Description must be a string' })
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Publication status of the mood media',
    example: true,
  })
  @IsBoolean({ message: 'isPublished must be a boolean' })
  @IsNotEmpty()
  isPublished: boolean;

  @ApiProperty({
    description: 'Indicates if the media is deleted',
    example: false,
  })
  @IsBoolean({ message: 'isDeleted must be a boolean value' })
  isDeleted: boolean;

  @ApiProperty({
    description: 'Indicates if the media is an embedded URL',
    example: true,
  })
  @IsBoolean({ message: 'isEmbeddedUrl must be a boolean value' })
  isEmbeddedUrl: boolean;

  @ApiProperty({
    description: 'Thumbnail URL',
    example: 'https://example.com/thumbnail.jpg',
  })
  @IsUrl({}, { message: 'Thumbnail URL must be a valid URL' })
  @IsNotEmpty({ message: 'Thumbnail URL is required' })
  thumbnailUrl: string;

  @ApiProperty({
    description: 'Video URL',
    example: 'https://example.com/video.mp4',
  })
  @IsUrl({}, { message: 'Video URL must be a valid URL' })
  @IsNotEmpty({ message: 'Video URL is required' })
  videoUrl: string;

  @ApiProperty({
    description: 'Hunger level',
    enum: HUNGER_LEVELS,
    example: HUNGER_LEVELS.MILD,
  })
  @IsEnum(HUNGER_LEVELS, { message: 'Hunger level must be a valid enum value' })
  hungerLevel: HUNGER_LEVELS;

  @ApiProperty({
    description: 'Mood type',
    enum: MOOD_TYPES,
    example: MOOD_TYPES.HAPPY,
  })
  @IsEnum(MOOD_TYPES, { message: 'Mood type must be a valid enum value' })
  moodType: MOOD_TYPES;

  createdAt: Date;

  static transform(object: MoodMedia): MoodMediaDTO {
    const transformedObj = new MoodMediaDTO();
    transformedObj.id = object.id;
    transformedObj.title = object.title;
    transformedObj.description = object.description;
    transformedObj.isPublished = object.isPublished;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.videoUrl = object.videoUrl;
    transformedObj.hungerLevel = object.hungerLevel as HUNGER_LEVELS;
    transformedObj.moodType = object.moodType as MOOD_TYPES;
    transformedObj.createdAt = (object as any).createdAt;

    return transformedObj;
  }
}
