import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { MoodMediaDTO } from './moodMedia.dto';

export class GetAllMoodMediaResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of mood media items available',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Number of mood media items returned in the current request',
    example: 20,
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of mood media items retrieved in the request',
    type: [MoodMediaDTO],
  })
  moodMedias: MoodMediaDTO[];
}
