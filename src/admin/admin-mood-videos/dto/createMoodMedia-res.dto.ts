import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { MoodMediaDTO } from './moodMedia.dto';

export class CreateMoodMediaResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Mood media created successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Mood media object containing details of the created media',
    type: MoodMediaDTO,
  })
  moodMedia: MoodMediaDTO;
}
