import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { MoodMediaDTO } from './moodMedia.dto';

export class DeleteMoodMediaResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Response message indicating the result of the delete operation',
    example: 'Mood media deleted successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Mood media object that was deleted',
    type: MoodMediaDTO,
  })
  moodMedia: MoodMediaDTO;
}
