import { Injectable, NotFoundException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ContactUsQuery } from 'models/contact-us-query';
import { GetAllContactUsQueryQueryInterface } from './interfaces';
import {
  GetAllContactUsQueryResDTO,
  GetSingleContactUsQueryResDTO,
  UpdateContactUsQueryReqDTO,
  UpdateContactUsQueryResDTO,
} from './dto';
import { EncryptionService, UtilsService } from 'src/common/services';
import { UserRepoService } from 'src/repo/user-repo.service';
import { ContactUsQueryDTO } from 'src/user/contact_us/dto';
import { FileUploadService } from 'src/user/file-upload/file-upload.service';

@Injectable()
export class AdminContactUsQueryService {
  constructor(
    @InjectModel(ContactUsQuery.name)
    private readonly contactUsQueryModel: Model<ContactUsQuery>,

    private readonly utilsService: UtilsService,
    private readonly userRepo: UserRepoService,
    private readonly encryptionService: EncryptionService,
    private readonly fileUploadService: FileUploadService,
  ) {}

  // Get all contact us queries
  async getAllQueries(
    filterQueriesData: GetAllContactUsQueryQueryInterface,
  ): Promise<GetAllContactUsQueryResDTO> {
    const { page, email, status } = filterQueriesData;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const filterObj: any = {};

    if (email) {
      const user = await this.userRepo.findUserByEmail(email);

      filterObj.userId = { $eq: user?._id || '' };
    }

    if (status) {
      filterObj.status = { $eq: status };
    }

    const queries = await this.contactUsQueryModel
      .find(filterObj)
      .populate('userId')
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.contactUsQueryModel
      .countDocuments(filterObj)
      .exec();

    const resp = await Promise.all(
      queries.map(async (query) => {
        const { userId, ...rest } = query.toObject();
        const item = { ...rest, user: userId };

        (item.user as any).email = this.encryptionService.decrypt(
          (item.user as any).email,
        );

        // Process attachments with file URLs and expiry checking
        if (item.attachments && item.attachments.length > 0) {
          item.attachments = await this.processAttachmentsWithFileUrls(
            item.attachments,
          );
        }

        return ContactUsQueryDTO.transform(item);
      }),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      nbHits: resp.length,
      total: total,
      queries: resp,
    };
  }

  // Get a single contact us query by its ID
  async getSingleQuery(
    queryId: string,
  ): Promise<GetSingleContactUsQueryResDTO> {
    const query = await this.contactUsQueryModel
      .findById(queryId)
      .populate('userId')
      .exec();

    if (!query) {
      throw new NotFoundException(`Query with ID ${queryId} not found.`);
    }

    const { userId, ...rest } = query.toObject();
    const item = { ...rest, user: userId };

    (item.user as any).email = this.encryptionService.decrypt(
      (item.user as any).email,
    );

    // Process attachments with file URLs and expiry checking
    if (item.attachments && item.attachments.length > 0) {
      item.attachments = await this.processAttachmentsWithFileUrls(
        item.attachments,
      );
    }

    const resp = ContactUsQueryDTO.transform(item);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      query: resp,
    };
  }

  // Update the status of a contact us query
  async updateQuery(
    queryId: string,
    updateData: UpdateContactUsQueryReqDTO,
  ): Promise<UpdateContactUsQueryResDTO> {
    const { status } = updateData;

    const query = await this.contactUsQueryModel
      .findById(queryId)
      .populate('userId')
      .exec();

    if (!query) {
      throw new NotFoundException(`Query with ID ${queryId} not found.`);
    }

    // Update only the status field
    query.status = status;
    await query.save();

    const { userId, ...rest } = query.toObject();
    const item = { ...rest, user: userId };

    (item.user as any).email = this.encryptionService.decrypt(
      (item.user as any).email,
    );

    const resp = ContactUsQueryDTO.transform(item);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      query: resp,
    };
  }

  /**
   * Process attachments to include file URLs with expiry checking and loading states
   * @param attachments Array of attachment objects with fileId
   * @returns Array of attachments with URL, expiry status, and loading state info
   */
  private async processAttachmentsWithFileUrls(
    attachments: any[],
  ): Promise<any[]> {
    return Promise.all(
      attachments.map(async (attachment) => {
        if (!attachment.fileId) {
          return {
            ...attachment,
            url: null,
            isExpired: true,
            loadingState: 'error',
            error: 'No file ID found',
          };
        }

        try {
          // Check if file URL is expired and get/refresh URL
          const fileUrlData = await this.getFileUrlWithExpiryCheck(
            attachment.fileId,
          );

          return {
            ...attachment,
            url: fileUrlData.url,
            isExpired: fileUrlData.isExpired,
            loadingState: fileUrlData.loadingState,
            expiryAt: fileUrlData.expiryAt,
            wasRefreshed: fileUrlData.wasRefreshed,
          };
        } catch {
          return {
            ...attachment,
            url: null,
            isExpired: true,
            loadingState: 'error',
            error: 'File not accessible',
          };
        }
      }),
    );
  }

  /**
   * Get file URL with expiry checking and automatic refresh
   * @param fileId File ID to get URL for
   * @returns Object with URL, expiry status, and loading state
   */
  private async getFileUrlWithExpiryCheck(fileId: string): Promise<{
    url: string;
    isExpired: boolean;
    loadingState: 'loaded' | 'refreshed' | 'error';
    expiryAt: Date;
    wasRefreshed: boolean;
  }> {
    try {
      // Use FileUploadService to get valid URL (automatically handles expiry)
      const url = await this.fileUploadService.getValidS3Url(fileId);

      // Get file details to check if it was refreshed
      const fileRecord =
        await this.fileUploadService['fileUploadModel'].findById(fileId);

      if (!fileRecord) {
        throw new Error('File record not found');
      }

      const now = new Date();
      const wasRecentlyUpdated =
        fileRecord.updatedAt &&
        now.getTime() - fileRecord.updatedAt.getTime() < 5000; // Within last 5 seconds

      return {
        url,
        isExpired: false,
        loadingState: wasRecentlyUpdated ? 'refreshed' : 'loaded',
        expiryAt: fileRecord.expiryAt,
        wasRefreshed: wasRecentlyUpdated,
      };
    } catch (error) {
      throw new Error(`Failed to get file URL: ${error.message}`);
    }
  }

  /**
   * Get file URL endpoint for frontend to call when file is expired
   * @param fileId File ID to refresh URL for
   * @returns Refreshed file URL with metadata
   */
  async refreshFileUrl(fileId: string): Promise<{
    url: string;
    expiryAt: Date;
    refreshedAt: Date;
  }> {
    try {
      const url = await this.fileUploadService.getValidS3Url(fileId);
      const fileRecord =
        await this.fileUploadService['fileUploadModel'].findById(fileId);

      return {
        url,
        expiryAt: fileRecord.expiryAt,
        refreshedAt: new Date(),
      };
    } catch (error) {
      throw new NotFoundException(
        `Failed to refresh file URL: ${error.message}`,
      );
    }
  }
}
