import { QUERY_STATUS } from 'models/contact-us-query';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateContactUsQueryReqDTO {
  @ApiProperty({
    description: 'The status of the contact query',
    enum: QUERY_STATUS,
    example: QUERY_STATUS.PENDING, // or any default value from the enum
  })
  @IsEnum(QUERY_STATUS)
  @IsNotEmpty({ message: 'status is required' })
  status: QUERY_STATUS;
}
