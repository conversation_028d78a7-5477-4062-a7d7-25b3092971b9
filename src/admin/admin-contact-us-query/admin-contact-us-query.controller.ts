import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import { AdminContactUsQueryService } from './admin-contact-us-query.service';
import {
  GetAllContactUsQueryResDTO,
  GetSingleContactUsQueryResDTO,
  UpdateContactUsQueryReqDTO,
  UpdateContactUsQueryResDTO,
} from './dto';
import { ErrorResponse } from 'src/utils/responses';
import { Authority } from 'src/utils/decorators';
import { GetAllContactUsQueryQueryInterface } from './interfaces';

@ApiTags('Admin-Contact-Us-Query')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('/admin/contact')
export class AdminContactUsQueryController {
  constructor(
    private readonly adminContactUsQueryService: AdminContactUsQueryService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the contact query.',
    type: GetAllContactUsQueryResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    description: 'The page number for pagination (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @ApiQuery({
    name: 'email',
    description: 'The email for searching (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @ApiQuery({
    name: 'status',
    description: 'The status for filtering (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @Authority()
  @Get()
  async GetAllQueries(
    @Query() filterQueries: GetAllContactUsQueryQueryInterface,
  ) {
    return this.adminContactUsQueryService.getAllQueries(filterQueries);
  }

  // Get Single contact queryquery
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the contact query.',
    type: GetSingleContactUsQueryResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Get('/:queryId')
  async GetSingleQuery(@Param('queryId') id: string) {
    return this.adminContactUsQueryService.getSingleQuery(id);
  }

  // Create contact queryquery
  @ApiResponse({
    status: 201,
    description: 'Successfully updated contact query.',
    type: UpdateContactUsQueryResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @Authority()
  @Put('/:queryId')
  async UpdateQuery(
    @Body() updateData: UpdateContactUsQueryReqDTO,
    @Param('queryId') id: string,
  ) {
    return this.adminContactUsQueryService.updateQuery(id, updateData);
  }

  // Refresh file URL for expired files
  @ApiResponse({
    status: 200,
    description: 'Successfully refreshed file URL.',
    schema: {
      type: 'object',
      properties: {
        error: { type: 'boolean', example: false },
        statusCode: { type: 'number', example: 200 },
        url: { type: 'string', example: 'https://s3.amazonaws.com/...' },
        expiryAt: { type: 'string', format: 'date-time' },
        refreshedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
    type: ErrorResponse,
  })
  @Authority()
  @Get('/file/:fileId/refresh')
  async refreshFileUrl(@Param('fileId') fileId: string) {
    const result = await this.adminContactUsQueryService.refreshFileUrl(fileId);
    return {
      error: false,
      statusCode: 200,
      ...result,
    };
  }
}
