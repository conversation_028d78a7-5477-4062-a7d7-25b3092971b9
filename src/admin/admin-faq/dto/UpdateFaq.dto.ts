import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, MinLength } from 'class-validator';
import { BaseResponse } from 'src/utils/responses';
import { Faq } from 'models/faq';

export class UpdateFaqReqDTO {
  @ApiPropertyOptional({
    description: 'Updated category',
    example: 'Technical Support',
  })
  @IsString()
  @IsOptional()
  @MinLength(3, { message: 'Question must be at least 3 characters long' })
  question?: string;

  @ApiPropertyOptional({
    description: 'Updated description',
    example: 'New help description',
  })
  @IsString()
  @IsOptional()
  @MinLength(3, { message: 'answer must be at least 3 characters long' })
  answer?: string;

  @ApiPropertyOptional({
    description: 'Update deletion status',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;
}

export class UpdateFaqResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Faq article updated successfully',
  })
  msg: string;

  @ApiProperty({ description: 'Updated help article details', type: Faq })
  data: Faq;
}
