import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { FaqDTO } from './faq.dto';

export class GetAllFaqResDTO extends BaseResponse {
  @ApiProperty()
  error: boolean;

  @ApiProperty()
  statusCode: number;

  @ApiProperty({
    description: 'Total number of help center entries',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Number of help center entries in the current response',
    example: 10,
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of help center entries',
    type: [FaqDTO],
  })
  data: FaqDTO[];
}
