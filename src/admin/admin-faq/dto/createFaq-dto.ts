import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';
import { Faq } from 'models/faq';
import { BaseResponse } from 'src/utils/responses';

export class CreateFaqReqDTO {
  @ApiProperty({
    description: 'Title of the help center article',
    example: 'How to reset password',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3, { message: 'Question must be at least 3 characters long' })
  question: string;

  @ApiProperty({
    description: 'Detailed description of the article',
    example: 'Follow these steps to reset your password...',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3, { message: 'answer must be at least 3 characters long' })
  answer: string;

  @ApiProperty({ description: 'Deletion status', example: false })
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;
}

export class CreateFaqResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Help article created successfully',
  })
  msg: string;

  @ApiProperty({ description: 'Created help article details', type: Faq })
  data: Faq;
}
