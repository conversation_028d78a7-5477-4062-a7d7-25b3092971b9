import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Faq } from 'models/faq';
import {
  CreateFaqReqDTO,
  CreateFaqResDTO,
  GetAllFaqResDTO,
  UpdateFaqResDTO,
  UpdateFaqReqDTO,
  DeleteFaqResDTO,
  FaqDTO,
  GetSingleFaqResDTO,
} from './dto';
import { getAllFaqQueryInterface } from './interfaces';
import { UtilsService } from 'src/common/services';

@Injectable()
export class AdminFaqService {
  constructor(
    @InjectModel(Faq.name) private faqModel: Model<Faq>,
    private readonly utilsService: UtilsService,
  ) {}

  async createFaq(faqData: CreateFaqReqDTO): Promise<CreateFaqResDTO> {
    const trimmedQuestion = faqData.question?.trim();
    const trimmedAnswer = faqData.answer?.trim();

    if (!trimmedQuestion || trimmedQuestion.length < 3) {
      throw new BadRequestException(
        'FAQ question must be at least 3 non-space characters long',
      );
    }

    if (!trimmedAnswer || trimmedAnswer.length < 3) {
      throw new BadRequestException(
        'FAQ answer must be at least 3 non-space characters long',
      );
    }

    const newFaq = new this.faqModel({
      ...faqData,
      isDeleted: faqData.isDeleted ?? false,
    });

    await newFaq.save();

    return {
      error: false,
      statusCode: HttpStatus.CREATED,
      msg: 'FAQ created successfully!',
      data: newFaq,
    };
  }

  async getAllFaqs(
    queryFilters: getAllFaqQueryInterface,
  ): Promise<GetAllFaqResDTO> {
    const { page, title } = queryFilters;
    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };
    if (title) query.question = { $regex: new RegExp(title, 'i') };

    const faqEntries = await this.faqModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.faqModel.countDocuments(query);

    const resp = faqEntries.map((item) => FaqDTO.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: faqEntries.length,
      data: resp,
    };
  }

  async getFaqById(id: string): Promise<GetSingleFaqResDTO> {
    const faq = await this.faqModel.findById(id).exec();
    if (!faq || faq.isDeleted) {
      throw new NotFoundException('FAQ entry not found');
    }
    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: FaqDTO.transform(faq),
    };
  }
  async updateFaq(
    id: string,
    updateData: UpdateFaqReqDTO,
  ): Promise<UpdateFaqResDTO> {
    const existingFaq = await this.faqModel.findById(id).exec();

    if (!existingFaq) {
      throw new NotFoundException('FAQ entry not found');
    }

    if (existingFaq.isDeleted) {
      throw new BadRequestException('Cannot update a deleted FAQ entry');
    }

    if (updateData.question) {
      const trimmedQuestion = updateData.question.trim();
      if (trimmedQuestion.length < 3) {
        throw new BadRequestException(
          'FAQ question must be at least 3 non-space characters long',
        );
      }
    }

    if (updateData.answer) {
      const trimmedAnswer = updateData.answer.trim();
      if (trimmedAnswer.length < 3) {
        throw new BadRequestException(
          'FAQ answer must be at least 3 non-space characters long',
        );
      }
    }

    const updatedFaq = await this.faqModel
      .findByIdAndUpdate(id, updateData, { new: true, runValidators: true })
      .exec();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'FAQ article updated successfully',
      data: updatedFaq,
    };
  }

  async removeFaq(id: string): Promise<DeleteFaqResDTO> {
    // Find the document first
    const existingFaq = await this.faqModel.findById(id).exec();

    if (!existingFaq) {
      throw new NotFoundException('Faq entry not found');
    }

    if (existingFaq.isDeleted) {
      throw new BadRequestException('Faq entry is already deleted');
    }

    // Update only if it exists and is not already deleted
    await this.faqModel.findByIdAndUpdate(
      id,
      { isDeleted: true },
      { new: true },
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Faq article marked as deleted successfully',
    };
  }
}
