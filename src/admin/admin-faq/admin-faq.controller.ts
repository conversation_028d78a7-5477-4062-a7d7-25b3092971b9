import {
  Body,
  Controller,
  Post,
  HttpStatus,
  Query,
  Get,
  Put,
  Param,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiResponse } from '@nestjs/swagger';
import { AdminFaqService } from './admin-faq.service';
import {
  CreateFaqReqDTO,
  CreateFaqResDTO,
  DeleteFaqResDTO,
  GetAllFaqResDTO,
  GetSingleFaqResDTO,
  UpdateFaqReqDTO,
  UpdateFaqResDTO,
} from './dto';
import { Authority } from 'src/utils/decorators';
import { getAllFaqQueryInterface } from './interfaces';
import { AuthGuard } from 'src/middlewares';

@ApiTags('Admin - FAQ')
@Controller('admin/faq')
@UseGuards(AuthGuard)
export class AdminFaqController {
  constructor(private readonly adminFaqService: AdminFaqService) {}

  @ApiResponse({ status: HttpStatus.CREATED, type: CreateFaqResDTO })
  @Authority()
  @Post()
  async createFaq(@Body() faqData: CreateFaqReqDTO): Promise<CreateFaqResDTO> {
    return this.adminFaqService.createFaq(faqData);
  }

  @ApiResponse({ status: HttpStatus.OK, type: GetAllFaqResDTO })
  @Authority()
  @Get()
  async getAllFaqs(
    @Query() queryFilters: getAllFaqQueryInterface,
  ): Promise<GetAllFaqResDTO> {
    return this.adminFaqService.getAllFaqs(queryFilters);
  }

  @Authority()
  @Get(':id')
  async getFaqById(@Param('id') id: string): Promise<GetSingleFaqResDTO> {
    return this.adminFaqService.getFaqById(id);
  }

  @ApiResponse({ status: HttpStatus.OK, type: UpdateFaqResDTO })
  @Authority()
  @Put(':id')
  async updateFaq(
    @Param('id') id: string,
    @Body() updateData: UpdateFaqReqDTO,
  ): Promise<UpdateFaqResDTO> {
    return this.adminFaqService.updateFaq(id, updateData);
  }

  @ApiResponse({ status: HttpStatus.OK, type: DeleteFaqResDTO })
  @Authority()
  @Put('delete/:id')
  async deleteFaq(@Param('id') id: string): Promise<DeleteFaqResDTO> {
    return this.adminFaqService.removeFaq(id);
  }
}
