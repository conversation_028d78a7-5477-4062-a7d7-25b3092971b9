import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AdminOtpGuard implements CanActivate {
  constructor(private readonly jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = request.headers['admin-otp-token'];

    if (!token) throw new UnauthorizedException('OTP access token missing');

    try {
      const decoded = this.jwtService.verify(token);
      if (decoded.type !== 'admin-user-details-access') {
        throw new UnauthorizedException('Invalid token type');
      }
      request.user = { sub: decoded.sub };
      return true;
    } catch {
      throw new UnauthorizedException('Invalid or expired OTP token');
    }
  }
}
