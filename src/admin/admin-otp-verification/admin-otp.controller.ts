import { Body, Controller, Post, Req, UseGuards, Get } from '@nestjs/common';
import { AuthGuard } from 'src/middlewares';
import { AdminOtpService } from './admin-otp.service';
import { User } from 'models/user';
// import { VerifyOtpRequestDto, VerifyOtpResponseDto } from './dto';
import { OtpAccessGuard } from 'src/middlewares/otpAccessGaurd.middleware';

@Controller('admin-access')
export class AdminOtpController {
  constructor(private readonly adminOtpService: AdminOtpService) {}

  @UseGuards(AuthGuard)
  @Post('send-otp')
  async sendOtp(@Req() req): Promise<{ message: string }> {
    await this.adminOtpService.sendOtp(req.user);
    return { message: 'OTP sent to your email' };
  }

  @UseGuards(AuthGuard)
  @Post('verify-otp')
  async verifyOtp(@Req() req: { user: User }, @Body() body: { otp: string }) {
    return this.adminOtpService.verifyOtp(req.user, body.otp);
  }

  @Get('secure-users')
  @UseGuards(AuthGuard, OtpAccessGuard)
  getSecureUserList(@Req() req) {
    return {
      error: false,
      statusCode: 200,
      msg: 'Access granted to secure admin route with valid OTP and JWT',
      user: req.user,
    };
  }
}
