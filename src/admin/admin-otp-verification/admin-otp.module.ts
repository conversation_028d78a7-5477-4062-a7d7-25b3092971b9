import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminOtpController } from './admin-otp.controller';
import { AdminOtpService } from './admin-otp.service';
import { ConfigModule } from '@nestjs/config';
import {
  AdminOtpToken,
  AdminOtpTokenSchema,
  RouteAccessToken,
  RouteAccessTokenSchema,
} from 'models/auth';
import { EmailService } from 'src/common/services/email.service';
import { AwsSesService } from 'src/third-party/aws';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { AdminOtpUtilsService } from './admin-otp.utils.service';

@Module({
  imports: [
    ConfigModule,
    RepoModule,
    AuthModule,
    JwtModule.register({}),
    CommonModule,
    MongooseModule.forFeature([
      { name: AdminOtpToken.name, schema: AdminOtpTokenSchema },
      { name: RouteAccessToken.name, schema: RouteAccessTokenSchema },
    ]),
  ],
  controllers: [AdminOtpController],
  providers: [
    AdminOtpService,
    EmailService,
    AwsSesService,
    AdminOtpUtilsService,
  ],
})
export class AdminOtpModule {}
