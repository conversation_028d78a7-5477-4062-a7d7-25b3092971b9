import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { RouteAccessToken } from 'models/auth';
import { User } from 'models/user';
import { JwtService } from '@nestjs/jwt';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AdminOtpUtilsService {
  constructor(
    @InjectModel(RouteAccessToken.name)
    private routeAccessTokenModel: Model<RouteAccessToken>,
    private readonly jwtService: JwtService,
  ) {}
  async generateRouteAccessToken(user: User): Promise<{
    token: string;
    expiryInMs: Date;
  }> {
    const payload = {
      email: user.email,
      user: {
        id: user._id,
        name: user.name,
      },
    };

    const userId = this.ensureObjectId(user._id as string | Types.ObjectId);

    const jwtToken = this.jwtService.sign(payload, {
      expiresIn: process.env.ROUTE_ACCESS_TOKEN_LIFETIME || '1h',
      subject: user.email,
      algorithm: 'HS512',
      secret: process.env.ROUTE_ACCESS_TOKEN_SECRET as string,
    });

    const expiryInMs = new Date(
      Date.now() +
        Number(process.env.ROUTE_ACCESS_TOKEN_EXPIRY || 60 * 60 * 1000), // default 1h
    );

    await this.routeAccessTokenModel.create({
      token: jwtToken,
      expiry: expiryInMs,
      userId,
      isExpired: false,
    });

    return { token: jwtToken, expiryInMs };
  }

  ensureObjectId(id: string | Types.ObjectId): Types.ObjectId {
    return typeof id === 'string' ? new Types.ObjectId(id) : id;
  }
}
