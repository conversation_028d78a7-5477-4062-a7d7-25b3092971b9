import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Activity, ACTIVITY_TAGS } from 'models/activity';
import { Model } from 'mongoose';
import { UtilsService } from 'src/common/services';
import {
  ActivityDTO,
  CreateActivityMediaReqDTO,
  CreateActivityMediaResDTO,
  DeleteActivityMediaResDTO,
  GetAllActivityMediaResDTO,
  GetAllActivityMediaSubTagResDTO,
  GetSingleActivityMediaResDTO,
  UpdateActivityMediaReqDTO,
  UpdateActivityMediaResDTO,
} from './dto';
import { getAllActivityMediaQueryInterface } from './interfaces';
import { TagSubTagMap } from 'models/activity';

@Injectable()
export class AdminActivityService {
  constructor(
    @InjectModel(Activity.name) private activityMediaModel: Model<Activity>,
    private readonly utilsService: UtilsService,
  ) {}

  // Get all Activity Video
  async getAllActivitySubTags(
    tag: string,
  ): Promise<GetAllActivityMediaSubTagResDTO> {
    // Dynamically get subTags based on tag
    const subTags: string[] = TagSubTagMap[tag as ACTIVITY_TAGS] || [];

    return {
      error: false,
      statusCode: HttpStatus.OK,
      tag,
      subTags, // Include the dynamically retrieved subTags
    };
  }

  // Get all Activity Video
  async getAllActivityMedia(
    queryFilters: getAllActivityMediaQueryInterface,
  ): Promise<GetAllActivityMediaResDTO> {
    const { page, title, tag } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };

    if (title) {
      query.title = { $regex: new RegExp(title, 'i') };
    }

    if (tag) {
      query.tag = { $eq: tag };
    }

    const activityMedias = await this.activityMediaModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.activityMediaModel.countDocuments(query);

    const activityMediaResp = activityMedias.map((item) =>
      ActivityDTO.transform(item),
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: activityMediaResp.length,
      ActivityMedias: activityMediaResp,
    };
  }

  // Get a single Activity Video
  async getSingleActivityMedia(
    id: string,
  ): Promise<GetSingleActivityMediaResDTO> {
    const activityMedia = await this.activityMediaModel.findById(id).exec();

    if (!activityMedia || activityMedia.isDeleted) {
      throw new NotFoundException('Activity Video not found');
    }

    const resp = ActivityDTO.transform(activityMedia);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      ActivityMedia: resp,
    };
  }

  // Create Activity Video
  async createActivityMedia(
    activityMediaData: CreateActivityMediaReqDTO,
  ): Promise<CreateActivityMediaResDTO> {
    const {
      title,
      description,
      videoUrl,
      completionTime,
      tag,
      subTags,
      isPublished,
      calorieEstimate,
    } = activityMediaData;

    const newActivityMedia = await this.activityMediaModel.create({
      title,
      description,
      videoUrl,
      tag,
      subTags,
      isPublished,
      calorieEstimate,
      completionTime,
    });

    const resp = ActivityDTO.transform(newActivityMedia);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Activity Video created successfully !!',
      ActivityMedia: resp,
    };
  }

  // Update Activity Video
  async updateActivityMedia(
    id: string,
    updateBody: UpdateActivityMediaReqDTO,
  ): Promise<UpdateActivityMediaResDTO> {
    const media = await this.activityMediaModel.findById(id);

    if (!media || media.isDeleted) {
      throw new NotFoundException('Activity Video does not exist with this ID');
    }

    const {
      title,
      description,
      subTags,
      isPublished,
      calorieEstimate,
      completionTime,
    } = updateBody;

    const updateData: any = {};

    if (title) updateData.title = title;
    if (description) updateData.description = description;
    if (isPublished !== undefined) updateData.isPublished = isPublished;
    if (calorieEstimate !== undefined)
      updateData.calorieEstimate = calorieEstimate;
    if (completionTime !== undefined)
      updateData.completionTime = completionTime;

    // Validate and update subTags (tag is NOT updatable)
    if (subTags) {
      const validSubTags = TagSubTagMap[media.tag as ACTIVITY_TAGS] || [];

      const invalidSubTags = subTags.filter(
        (subTag) => !validSubTags.includes(subTag),
      );

      if (invalidSubTags.length > 0) {
        throw new BadRequestException(
          `Invalid subTags for tag "${media.tag}". Allowed: ${validSubTags.join(', ')}`,
        );
      }

      updateData.subTags = subTags;
    }

    const updatedActivityMedia = await this.activityMediaModel
      .findByIdAndUpdate(id, updateData, { new: true, runValidators: true })
      .exec();

    if (!updatedActivityMedia) {
      throw new NotFoundException('Activity Video not found');
    }

    const resp = ActivityDTO.transform(updatedActivityMedia);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Updated Activity Video Successfully!',
      ActivityMedia: resp,
    };
  }

  // Delete Activity Video
  async deleteActivityMedia(id: string): Promise<DeleteActivityMediaResDTO> {
    const media = await this.activityMediaModel.findById(id);

    if (!media || media.isDeleted === true) {
      throw new NotFoundException('Activity Recomendation Video not found');
    }

    const deletedMedia = await this.activityMediaModel
      .findByIdAndUpdate(
        id,
        { isDeleted: true },
        { new: true, runValidators: true },
      )
      .exec();

    const resp = ActivityDTO.transform(deletedMedia);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Activity Recomendation Video deleted successfully',
      ActivityMedia: resp,
    };
  }
}
