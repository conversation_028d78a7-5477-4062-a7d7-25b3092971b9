import {
  Controller,
  Get,
  Query,
  Param,
  Post,
  Body,
  Put,
  Delete,
  UseGuards,
  BadRequestException,
  ParseEnumPipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminActivityService } from './admin-activity.service';
import { ErrorResponse } from 'src/utils/responses';
import { Authority } from 'src/utils/decorators';
import { AuthGuard } from 'src/middlewares';
import {
  GetAllActivityMediaResDTO,
  CreateActivityMediaResDTO,
  DeleteActivityMediaResDTO,
  GetSingleActivityMediaResDTO,
  UpdateActivityMediaResDTO,
  CreateActivityMediaReqDTO,
  UpdateActivityMediaReqDTO,
  GetAllActivityMediaSubTagResDTO,
} from './dto';
import { getAllActivityMediaQueryInterface } from './interfaces';
import { ACTIVITY_TAGS } from 'models/activity';

@ApiTags('Admin-Activity-Videos')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('/admin')
export class AdminActivityController {
  constructor(private readonly adminActivityService: AdminActivityService) {}

  // Get All Activity Media subTags
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the activity media subtags.',
    type: GetAllActivityMediaSubTagResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'tag',
    description: 'The tag number for pagination (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @Authority()
  @Get('/activity_videos_subtags')
  async GetAllActivityMediaSubTags(
    @Query(
      'tag',
      new ParseEnumPipe(ACTIVITY_TAGS, {
        exceptionFactory: () =>
          new BadRequestException('Invalid activity tag value'),
      }),
    )
    tag: ACTIVITY_TAGS,
  ) {
    return this.adminActivityService.getAllActivitySubTags(tag);
  }

  // Get All Activity Media
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the activity media.',
    type: GetAllActivityMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    description: 'The page number for pagination (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @ApiQuery({
    name: 'title',
    description: 'The title of video for searching...',
    required: false,
    type: String,
    example: '1',
  })
  @Authority()
  @Get('activity_videos')
  async getAllActivityMedias(
    @Query() filterQueries: getAllActivityMediaQueryInterface,
  ) {
    return this.adminActivityService.getAllActivityMedia(filterQueries);
  }

  // Get Single Activity Media
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the activity media.',
    type: GetSingleActivityMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Get('activity_videos/:id')
  async getSingleActivityMedia(@Param('id') id: string) {
    return this.adminActivityService.getSingleActivityMedia(id);
  }

  // Create Activity Media
  @ApiResponse({
    status: 201,
    description: 'Successfully created the activity media.',
    type: CreateActivityMediaResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @Authority()
  @Post('activity_videos')
  async createActivityMedia(@Body() createBody: CreateActivityMediaReqDTO) {
    return this.adminActivityService.createActivityMedia(createBody);
  }

  // Update Activity Media
  @ApiResponse({
    status: 200,
    description: 'Successfully updated the activity media.',
    type: UpdateActivityMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Put('activity_videos/:id')
  async updateActivityMedia(
    @Param('id') id: string,
    @Body() updateBody: UpdateActivityMediaReqDTO,
  ) {
    return this.adminActivityService.updateActivityMedia(id, updateBody);
  }

  // Delete Activity Media
  @ApiResponse({
    status: 200,
    description: 'Successfully deleted the activity media.',
    type: DeleteActivityMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Delete('activity_videos/:id')
  async deleteActivityMedia(@Param('id') id: string) {
    return this.adminActivityService.deleteActivityMedia(id);
  }
}
