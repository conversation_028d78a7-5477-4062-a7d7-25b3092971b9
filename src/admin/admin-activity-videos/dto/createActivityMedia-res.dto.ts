import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { ActivityDTO } from './ActivityMedia.dto';

export class CreateActivityMediaResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Activity media created successfully',
  })
  msg: string;

  @ApiProperty({
    description:
      'Activity media object containing details of the created media',
    type: ActivityDTO,
  })
  ActivityMedia: ActivityDTO;
}
