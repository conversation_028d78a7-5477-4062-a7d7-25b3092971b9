import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  IsUrl,
  IsNotEmpty,
  IsBoolean,
  IsEnum,
  ArrayMinSize,
  IsInt,
  Min,
  IsNumber,
  Validate,
} from 'class-validator';
import { ACTIVITY_TAGS } from 'models/activity';
import { SubTagValidator } from 'src/utils/validators/subtag.validator';

export class CreateActivityMediaReqDTO {
  @ApiProperty({
    description: 'The title of the activity.',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'The description of the activity.',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The expected completion time for the activity. (in minutes)',
    type: Number,
  })
  @IsNumber()
  @IsNotEmpty()
  completionTime: number;

  @ApiProperty({
    description:
      'The tag related to the activity (e.g., wellness, workouts, etc.).',
    type: String,
    enum: ACTIVITY_TAGS,
    example: ACTIVITY_TAGS.WELLNESS,
  })
  @IsEnum(ACTIVITY_TAGS)
  @IsNotEmpty()
  tag: ACTIVITY_TAGS;

  @ApiProperty({
    description: 'The sub-tags related to the activity (dynamically mapped).',
    type: [String],
    example: ['meditation', 'yoga'],
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  @Validate(SubTagValidator)
  subTags: string[];

  @ApiProperty({
    description: 'The estimated calories burned during the activity.',
    type: Number,
    example: 300,
  })
  @Min(0)
  @IsInt()
  @IsNotEmpty()
  calorieEstimate: number;

  @ApiProperty({
    description: 'The URL of the activity video.',
    type: String,
    example: 'https://example.com/video.mp4',
  })
  @IsString()
  @IsUrl()
  @IsNotEmpty()
  videoUrl: string;

  @ApiProperty({
    description: 'Whether the activity is published or not.',
    type: Boolean,
    example: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  isPublished: boolean;
}
