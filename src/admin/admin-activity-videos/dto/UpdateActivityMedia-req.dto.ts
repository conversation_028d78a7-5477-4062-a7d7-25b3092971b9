import {
  IsOptional,
  IsString,
  IsArray,
  ArrayMinSize,
  Min,
  IsInt,
  IsBoolean,
  IsNumber,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateActivityMediaReqDTO {
  @ApiProperty({
    description: 'The title of the activity.',
    type: String,
  })
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty({
    description: 'The description of the activity.',
    type: String,
  })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    description: 'The expected completion time for the activity. (in minutes)',
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  completionTime: number;

  @ApiProperty({
    description: 'The sub-tags related to the activity (dynamically mapped).',
    type: [String],
    example: ['meditation', 'yoga'],
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @IsOptional()
  subTags: string[];

  @ApiProperty({
    description: 'The estimated calories burned during the activity.',
    type: Number,
    example: 300,
  })
  @Min(0)
  @IsInt()
  @IsOptional()
  calorieEstimate: number;

  @ApiProperty({
    description: 'Whether the activity is published or not.',
    type: Boolean,
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isPublished: boolean;
}
