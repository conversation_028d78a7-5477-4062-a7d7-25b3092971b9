import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  IsEnum,
  IsInt,
  IsUrl,
  IsBoolean,
  ArrayMinSize,
  IsNotEmpty,
  IsNumber,
} from 'class-validator';
import {
  Activity,
  ACTIVITY_TAGS,
  TagSubTagMap,
} from 'models/activity/activity.schema';

export class ActivityDTO {
  @ApiProperty({ description: 'The id of the activity.', type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: 'The title of the activity.', type: String })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'The description of the activity.',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The expected completion time for the activity (in minutes).',
    type: Number,
  })
  @IsNumber()
  completionTime: number;

  @ApiProperty({
    description:
      'The tag related to the activity (e.g., wellness, workouts, etc.).',
    type: String,
    enum: ACTIVITY_TAGS,
    example: ACTIVITY_TAGS.WELLNESS,
  })
  @IsEnum(ACTIVITY_TAGS)
  tag: ACTIVITY_TAGS;

  @ApiProperty({
    description: 'The sub-tags related to the activity (dynamically mapped).',
    type: [String],
    example: ['meditation', 'yoga'],
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  subTags: string[];

  @ApiProperty({
    description: 'The estimated calories burned during the activity.',
    type: Number,
    example: 300,
  })
  @IsInt()
  calorieEstimate: number;

  @ApiProperty({
    description: 'The URL of the activity video.',
    type: String,
    example: 'https://example.com/video.mp4',
  })
  @IsString()
  @IsUrl()
  videoUrl: string;

  @ApiProperty({
    description: 'Whether the activity is published or not.',
    type: Boolean,
    example: true,
  })
  @IsBoolean()
  isPublished: boolean;

  @ApiProperty({
    description: 'Whether the activity is deleted or not.',
    type: Boolean,
    example: false,
  })
  @IsBoolean()
  isDeleted: boolean;

  createdAt: Date;

  static transform(object: Activity): ActivityDTO {
    const transformedObj = new ActivityDTO();

    transformedObj.id = object._id.toString();
    transformedObj.title = object.title;
    transformedObj.description = object.description;
    transformedObj.completionTime = object.completionTime;
    transformedObj.tag = object.tag;
    transformedObj.subTags = object.subTags.filter((subTag) =>
      (TagSubTagMap[object.tag] || []).includes(subTag),
    );
    transformedObj.calorieEstimate = object.calorieEstimate;
    transformedObj.videoUrl = object.videoUrl;
    transformedObj.isPublished = object.isPublished;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.createdAt = (object as any).createdAt;

    return transformedObj;
  }
}
