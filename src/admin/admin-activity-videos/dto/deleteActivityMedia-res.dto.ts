import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { ActivityDTO } from './ActivityMedia.dto';

export class DeleteActivityMediaResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Response message indicating the result of the delete operation',
    example: 'Activity media deleted successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Activity media object that was deleted',
    type: ActivityDTO,
  })
  ActivityMedia: ActivityDTO;
}
