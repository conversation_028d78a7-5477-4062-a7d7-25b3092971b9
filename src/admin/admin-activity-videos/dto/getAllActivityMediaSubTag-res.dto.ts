import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, ArrayNotEmpty } from 'class-validator';
import { BaseResponse } from 'src/utils/responses';

export class GetAllActivityMediaSubTagResDTO extends BaseResponse {
  @ApiProperty({ description: 'The main tag', example: 'Sports' })
  @IsString()
  tag: string;

  @ApiProperty({
    description: 'List of sub-tags',
    example: ['Football', 'Basketball'],
  })
  @IsArray()
  @ArrayNotEmpty()
  subTags: string[];
}
