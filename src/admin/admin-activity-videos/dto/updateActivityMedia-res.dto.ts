import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { ActivityDTO } from './ActivityMedia.dto';

export class UpdateActivityMediaResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Response message indicating the result of the update operation',
    example: 'Activity media updated successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Updated Activity media object',
    type: ActivityDTO,
  })
  ActivityMedia: ActivityDTO;
}
