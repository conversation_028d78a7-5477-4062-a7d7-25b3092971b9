import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { ActivityDTO } from './ActivityMedia.dto';

export class GetAllActivityMediaResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of Activity media items available',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description:
      'Number of Activity media items returned in the current request',
    example: 20,
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of Activity media items retrieved in the request',
    type: [ActivityDTO],
  })
  ActivityMedias: ActivityDTO[];
}
