import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';
import { HelpTopicDTO } from './helpTopic.dto';
import { BaseResponse } from 'src/utils/responses';
import { HelpDTO } from './help.dto';
import { CATEGORY_TYPES } from 'models/help/help.schema';
import { Type } from 'class-transformer';

export class UpdateHelpReqDTO {
  @ApiPropertyOptional({
    description: 'Updated category',
    example: 'Technical Support',
  })
  @IsString()
  @IsOptional()
  category?: CATEGORY_TYPES;

  @ApiPropertyOptional({ description: 'Updated topics', type: [HelpTopicDTO] })
  @IsArray()
  @IsOptional()
  @Type(() => HelpTopicDTO)
  topics?: HelpTopicDTO[];

  @ApiPropertyOptional({
    description: 'Update deletion status',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;
}

export class UpdateHelpResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Help article updated successfully',
  })
  msg: string;

  @ApiProperty({ description: 'Updated help article details', type: HelpDTO })
  data: HelpDTO;
}
