import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { Help } from 'models/help';
import { HelpTopicDTO } from './helpTopic.dto';
import { CATEGORY_TYPES } from 'models/help/help.schema';

export class HelpDTO {
  @ApiProperty({ description: 'Help ID', example: '123' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Category of the help',
    example: 'Account Issues',
  })
  @IsString()
  @IsNotEmpty()
  category: CATEGORY_TYPES;

  @ApiProperty({ description: 'List of help topics', type: [HelpTopicDTO] })
  @IsArray()
  @IsOptional()
  topics?: HelpTopicDTO[];

  @ApiProperty({
    description: 'Indicates if the help is deleted',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  static transform(help: Help): HelpDTO {
    return {
      id: help._id.toString(),
      category: help.category,

      topics: help.topics?.map((topic) => ({
        id: topic.id,
        title: topic.title,
        description: topic.description,
      })),
      isDeleted: help.isDeleted,
      createdAt: new Date(),
    };
  }
}
