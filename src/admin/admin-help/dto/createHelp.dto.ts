import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';
import { HelpTopicDTO } from './helpTopic.dto';
import { BaseResponse } from 'src/utils/responses';
import { HelpDTO } from './help.dto';
import { CATEGORY_TYPES } from 'models/help/help.schema';
import { Type } from 'class-transformer';

export class CreateHelpReqDTO {
  @ApiProperty({ description: 'Category of help', example: 'Account Issues' })
  @IsString()
  @IsNotEmpty()
  category: CATEGORY_TYPES;

  @ApiProperty({
    description: 'Topics related to the help category',
    type: [HelpTopicDTO],
  })
  @IsArray()
  @IsNotEmpty() // Ensure topics array is required
  @Type(() => HelpTopicDTO) // Important for nested validation
  topics: HelpTopicDTO[];
}

export class CreateHelpResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Help article created successfully',
  })
  msg: string;

  @ApiProperty({ description: 'Created help article details', type: HelpDTO })
  data: HelpDTO;
}
