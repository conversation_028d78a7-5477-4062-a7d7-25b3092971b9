import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MinLength } from 'class-validator';

export class HelpTopicDTO {
  @ApiProperty({
    description: 'Title of the topic',
    example: 'Reset Password',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3, { message: 'title must be at least 3 characters long' })
  title: string;

  @ApiProperty({
    description: 'Description of the topic',
    example: 'Steps to reset password',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3, { message: 'description must be at least 3 characters long' })
  description: string;
}
