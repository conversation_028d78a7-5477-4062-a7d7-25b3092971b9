import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';

import { GetAllHelpQueryInterface } from './interface';
import {
  CreateHelpReqDTO,
  GetAllHelpResDTO,
  GetSingleHelpResDTO,
  UpdateHelpReqDTO,
} from './dto';
import { CreateHelpResDTO } from './dto/createHelp.dto';
import { Authority } from 'src/utils/decorators';
import { AdminHelpService } from './admin-help.service';
import { AuthGuard } from 'src/middlewares';

@ApiTags('Admin Help')
@Controller('/admin/help')
@UseGuards(AuthGuard)
export class AdminHelpController {
  constructor(private readonly helpService: AdminHelpService) {}

  @Authority()
  @Get()
  @ApiOperation({ summary: 'Get all help entries' })
  @ApiResponse({ status: HttpStatus.OK, type: GetAllHelpResDTO })
  async getAllHelp(
    @Query() queryFilters: GetAllHelpQueryInterface,
  ): Promise<GetAllHelpResDTO> {
    return this.helpService.getAllHelp(queryFilters);
  }

  @Authority()
  @Get(':id')
  @ApiOperation({ summary: 'Get a single help entry by ID' })
  @ApiParam({ name: 'id', type: String })
  @ApiResponse({ status: HttpStatus.OK, type: GetSingleHelpResDTO })
  async getHelpById(@Param('id') id: string): Promise<GetSingleHelpResDTO> {
    return this.helpService.getHelpById(id);
  }

  @Authority()
  @Post()
  @ApiOperation({ summary: 'Create a new help entry' })
  @ApiResponse({ status: HttpStatus.CREATED, type: CreateHelpResDTO })
  async createHelp(
    @Body() helpData: CreateHelpReqDTO,
  ): Promise<CreateHelpResDTO> {
    return this.helpService.createHelp(helpData);
  }

  @Authority()
  @Put(':id')
  @ApiOperation({ summary: 'Update a help entry by ID' })
  @ApiParam({ name: 'id', type: String })
  @ApiResponse({ status: HttpStatus.OK })
  async updateHelp(
    @Param('id') id: string,
    @Body() updateData: UpdateHelpReqDTO,
  ) {
    return await this.helpService.updateHelp(id, updateData);
  }

  @Authority()
  @ApiOperation({ summary: 'Mark a help entry as deleted' })
  @ApiParam({ name: 'id', type: String })
  @ApiResponse({ status: HttpStatus.OK })
  @Put('delete/:id')
  async markHelpAsDeleted(@Param('id') id: string) {
    return await this.helpService.removeHelp(id);
  }
}
