import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Help } from 'models/help';
import { UtilsService } from 'src/common/services';
import { GetAllHelpQueryInterface } from './interface';
import {
  CreateHelpReqDTO,
  DeleteHelpResDTO,
  GetAllHelpResDTO,
  GetSingleHelpResDTO,
  HelpDTO,
  UpdateHelpReqDTO,
} from './dto';
import { CreateHelpResDTO } from './dto/createHelp.dto';
import { UpdateHelpResDTO } from './dto/updateHelp.dto';

@Injectable()
export class AdminHelpService {
  constructor(
    @InjectModel(Help.name) private helpModel: Model<Help>,
    private readonly utilsService: UtilsService,
  ) {}

  async createHelp(helpData: CreateHelpReqDTO): Promise<CreateHelpResDTO> {
    // Validate that topics array is not empty
    if (!helpData.topics || helpData.topics.length === 0) {
      throw new HttpException(
        {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Topics are required and cannot be empty',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // Validate each topic has title and description
    for (const topic of helpData.topics) {
      const trimmedTitle = topic.title?.trim();
      const trimmedDescription = topic.description?.trim();

      if (!trimmedTitle || trimmedTitle.length < 3) {
        throw new HttpException(
          {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: 'Topic title must be at least 3 non-space characters long',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!trimmedDescription || trimmedDescription.length < 3) {
        throw new HttpException(
          {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message:
              'Topic description must be at least 3 non-space characters long',
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const newHelp = new this.helpModel({
      ...helpData,
    });

    await newHelp.save();

    return {
      error: false,
      statusCode: HttpStatus.CREATED,
      msg: 'Help article created successfully',
      data: HelpDTO.transform(newHelp),
    };
  }
  async getAllHelp(
    queryFilters: GetAllHelpQueryInterface,
  ): Promise<GetAllHelpResDTO> {
    const { page, category, title, description, search } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };

    // Ensure category is a valid string before applying regex
    if (typeof category === 'string' && category.trim().length > 0) {
      query.category = { $regex: new RegExp(category.trim(), 'i') };
    }

    // Build array of conditions for $or operator
    const orConditions = [];

    // Ensure title is a valid string before applying regex
    if (typeof title === 'string') {
      const trimmedTitle = title.trim();

      if (trimmedTitle.length > 0) {
        try {
          orConditions.push({
            'topics.title': { $regex: new RegExp(trimmedTitle, 'i') },
          });
        } catch (error) {
          console.error('Regex Error for title:', error);
        }
      }
    }

    // Add description search capability
    if (typeof description === 'string') {
      const trimmedDescription = description.trim();
      if (trimmedDescription.length > 0) {
        try {
          orConditions.push({
            'topics.description': {
              $regex: new RegExp(trimmedDescription, 'i'),
            },
          });
        } catch (error) {
          console.error('Regex Error for description:', error);
        }
      }
    }

    // Add general search across multiple fields
    if (typeof search === 'string') {
      const trimmedSearch = search.trim();
      if (trimmedSearch.length > 0) {
        try {
          const regexPattern = new RegExp(trimmedSearch, 'i');
          orConditions.push(
            { category: { $regex: regexPattern } },
            { 'topics.title': { $regex: regexPattern } },
            { 'topics.description': { $regex: regexPattern } },
          );
        } catch (error) {
          console.error('Regex Error for search:', error);
        }
      }
    }

    // Add the $or conditions to the query if any exist
    if (orConditions.length > 0) {
      if (orConditions.length === 1) {
        // If only one condition, no need for $or
        Object.assign(query, orConditions[0]);
      } else {
        query.$or = orConditions;
      }
    }

    const helpEntries = await this.helpModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.helpModel.countDocuments(query);
    const helpResponses = helpEntries.map((item) => HelpDTO.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: helpResponses.length,
      data: helpResponses,
    };
  }

  async getHelpById(id: string): Promise<GetSingleHelpResDTO> {
    const help = await this.helpModel.findById(id).exec();
    if (!help || help.isDeleted) {
      throw new NotFoundException('Help entry not found');
    }
    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: HelpDTO.transform(help),
    };
  }

  async updateHelp(
    id: string,
    updateData: UpdateHelpReqDTO,
  ): Promise<UpdateHelpResDTO> {
    const existingHelp = await this.helpModel.findById(id).exec();

    if (!existingHelp) {
      throw new NotFoundException('Help entry not found');
    }

    if (existingHelp.isDeleted) {
      throw new BadRequestException('Cannot update a deleted help entry');
    }

    // Validate topics array if provided in updateData
    if (updateData.topics?.length) {
      for (const topic of updateData.topics) {
        const trimmedTitle = topic.title?.trim();
        const trimmedDescription = topic.description?.trim();

        if (!trimmedTitle || trimmedTitle.length < 3) {
          throw new BadRequestException(
            'Topic title must be at least 3 non-space characters long',
          );
        }

        if (!trimmedDescription || trimmedDescription.length < 3) {
          throw new BadRequestException(
            'Topic description must be at least 3 non-space characters long',
          );
        }
      }
    }

    const updatedHelp = await this.helpModel
      .findByIdAndUpdate(id, updateData, { new: true, runValidators: true })
      .exec();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Help article updated successfully',
      data: HelpDTO.transform(updatedHelp),
    };
  }

  async removeHelp(id: string): Promise<DeleteHelpResDTO> {
    const existingHelp = await this.helpModel.findById(id).exec();

    if (!existingHelp) {
      throw new NotFoundException('Help entry not found');
    }

    if (existingHelp.isDeleted) {
      throw new BadRequestException('Help entry is already deleted');
    }

    existingHelp.isDeleted = true;
    await existingHelp.save();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Help article marked as deleted successfully',
    };
  }
}
