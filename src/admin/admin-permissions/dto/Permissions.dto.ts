import { User } from 'models/user';
import { ROLE_VALUES } from 'models/user/user.schema';
import {
  AdminUser_PermissionDTO,
  PermissionCategory,
} from './AdminUserPermissions.dto';

export class PermissionsDTO {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
  role: string;
  permissions: AdminUser_PermissionDTO[];

  static transform(object: User): PermissionsDTO {
    const transformedObj: PermissionsDTO = new PermissionsDTO();

    // Map simple properties
    transformedObj.id = object.id;
    transformedObj.name = object.name;
    transformedObj.email = object.email;
    transformedObj.createdAt = new Date();
    transformedObj.updatedAt = new Date();

    const isAdmin = object.role === ROLE_VALUES.ADMIN;

    if (isAdmin) {
      transformedObj.role = object.role;

      transformedObj.permissions = [
        this.getDashboardPermission('0'),
        this.getUserListPermission('1'),
        ...this.getAdminPermissions(),
        this.getHelpListPermission('9'),
        this.getFaqListPermission('10'),
        this.getContactQueriesListPermission('11'),
      ];
    }

    return transformedObj;
  }

  static getAdminPermissions(): AdminUser_PermissionDTO[] {
    const groupedPermissions: {
      [key in PermissionCategory]?: AdminUser_PermissionDTO;
    } = {};

    HR_Permissions_Routes.forEach((category, index) => {
      if (!groupedPermissions[category]) {
        groupedPermissions[category] = this.createCategoryPermission(
          category,
          index,
        );

        groupedPermissions[category].children =
          this.getChildrenRouteData(category);
      }
    });

    return Object.values(groupedPermissions);
  }

  static createCategoryPermission(
    category: PermissionCategory,
    index: number,
  ): AdminUser_PermissionDTO {
    return {
      id: `${index + 2}`,
      label: this.formatText(category),
      parentId: '',
      name: this.formatText(category),
      icon: iconCategoryMap[category] || '  bxs:hand-right',
      type: 0,
      route: `/${category}`,
      order: index + 2,
      category: category,
      children: [],
    };
  }

  static formatText(text: string): string {
    return text
      .replace('_', ' ') // Replace underscores with spaces
      .split(' ') // Split by spaces
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize first letter of each word
      .join(' '); // Join words with spaces
  }

  static getChildrenRouteData(
    category: PermissionCategory,
  ): AdminUser_PermissionDTO[] {
    const categoryRoutes = permissionsRouteData[category] || []; // Ensure it's an array

    if (!Array.isArray(categoryRoutes)) {
      console.warn(
        `Expected an array for category ${category}, but got:`,
        categoryRoutes,
      );
      return [];
    }

    return categoryRoutes.map((item, index) => ({
      id: `${index + 1}`,
      label: this.formatText(item.label),
      parentId: '',
      name: this.formatText(item.name),
      icon: item.icon || 'mdi-light:arrow-right',
      type: 1,
      route: `/${category}/${item.route}`,
      component: item.component,
      order: index + 2,
      category: category,
      children: [],
    }));
  }

  static getDashboardPermission(id: string): AdminUser_PermissionDTO {
    return {
      id: id,
      label: 'Dashboard',
      parentId: '',
      name: 'Dashboard',
      icon: 'bxs:dashboard',
      type: 1,
      route: '/dashboard',
      component: '/dashboard/workbench/index.tsx',
      order: 1,
      category: PermissionCategory.DASHBOARD,
    };
  }

  static getUserListPermission(id: string): AdminUser_PermissionDTO {
    return {
      id: id,
      label: 'Users',
      parentId: '',
      name: 'Users',
      icon: 'heroicons:users-16-solid',
      type: 1,
      route: '/users',
      component: '/dashboard/users/list/index.tsx',
      order: 1,
      category: PermissionCategory.USERS,
    };
  }

  static getContactQueriesListPermission(id: string): AdminUser_PermissionDTO {
    return {
      id: id,
      parentId: '',
      label: 'Contact Us Queries',
      name: 'Contact Us Queries',
      icon: 'material-symbols:support-agent-rounded',
      type: 1,
      route: '/contact_us',
      component: '/dashboard/contact_us/list/index.tsx',
      order: isNaN(Number(id)) ? 555 : Number(id),
      category: PermissionCategory.CONTACT_US,
    };
  }

  static getHelpListPermission(id: string): AdminUser_PermissionDTO {
    return {
      id: id,
      parentId: '',
      label: 'Help',
      name: 'Help',
      icon: 'mdi:account-help',
      type: 1,
      route: '/helps',
      component: '/dashboard/help/list/index.tsx',
      order: isNaN(Number(id)) ? 555 : Number(id),
      category: PermissionCategory.HELP,
    };
  }

  static getFaqListPermission(id: string): AdminUser_PermissionDTO {
    return {
      id: id,
      parentId: '',
      label: 'FAQs',
      name: 'FAQs',
      icon: 'material-symbols:help-rounded',
      type: 1,
      route: '/faqs',
      component: '/dashboard/faq/list/index.tsx',
      order: isNaN(Number(id)) ? 555 : Number(id),
      category: PermissionCategory.FAQ,
    };
  }
}

interface permissionsRouteDataElementsInterface {
  label: string;
  name: string;
  route: string;
  component: string;
  icon?: string;
}

interface permissionsRouteDataInterface {
  [PermissionCategory.MOOD]: permissionsRouteDataElementsInterface[];
  [PermissionCategory.ACTIVITY]: permissionsRouteDataElementsInterface[];
  [PermissionCategory.INGREDIENT]: permissionsRouteDataElementsInterface[];
  [PermissionCategory.RECIPE]: permissionsRouteDataElementsInterface[];
  [PermissionCategory.DEVICE]: permissionsRouteDataElementsInterface[];
}

export const HR_Permissions_Routes: PermissionCategory[] = [
  PermissionCategory.MOOD,
  PermissionCategory.ACTIVITY,
  PermissionCategory.INGREDIENT,
  PermissionCategory.RECIPE,
  PermissionCategory.DEVICE,
];

const addIcon = 'fluent:add-24-filled';
const listIcon = 'heroicons:list-bullet-solid';

const iconCategoryMap = {
  [PermissionCategory.MOOD]: 'mdi:emoji',
  [PermissionCategory.ACTIVITY]: 'grommet-icons:yoga',
  [PermissionCategory.INGREDIENT]: 'fa6-solid:carrot',
  [PermissionCategory.RECIPE]: 'fluent:food-16-filled',
  [PermissionCategory.DEVICE]: 'mingcute:remote-control-fill',
  //   [PermissionCategory.HELP]: 'mdi:account-help',
};

const permissionsRouteData: permissionsRouteDataInterface = {
  [PermissionCategory.MOOD]: [
    {
      label: 'List Mood Videos',
      name: 'List Mood Videos',
      route: 'list_mood_videos',
      icon: listIcon,
      component: '/dashboard/mood/list/index.tsx',
    },
    {
      label: 'Add Mood Videos',
      name: 'Add Mood Videos',
      route: 'add_mood_videos',
      icon: addIcon,
      component: '/dashboard/mood/add/index.tsx',
    },
  ],

  [PermissionCategory.ACTIVITY]: [
    {
      label: 'List Activity Videos',
      name: 'List Activity Videos',
      route: 'list_activity_videos',
      icon: listIcon,
      component: '/dashboard/activity/list/index.tsx',
    },
    {
      label: 'Add Activity Videos',
      name: 'Add Activity Videos',
      route: 'add_activity_videos',
      icon: addIcon,
      component: '/dashboard/activity/add/index.tsx',
    },
  ],

  [PermissionCategory.INGREDIENT]: [
    {
      label: 'List Ingredients',
      name: 'List Ingredients',
      route: 'list_ingredient',
      icon: listIcon,
      component: '/dashboard/ingredient/list/index.tsx',
    },
    {
      label: 'Add Ingredients',
      name: 'Add Ingredients',
      route: 'add_ingredient',
      icon: addIcon,
      component: '/dashboard/ingredient/add/index.tsx',
    },
  ],

  [PermissionCategory.RECIPE]: [
    {
      label: 'List Recipes',
      name: 'List Recipes',
      route: 'list_recipe',
      icon: listIcon,
      component: '/dashboard/recipe/list/index.tsx',
    },
    {
      label: 'Add Recipes',
      name: 'Add Recipes',
      route: 'add_recipe',
      icon: addIcon,
      component: '/dashboard/recipe/add/index.tsx',
    },
  ],

  [PermissionCategory.DEVICE]: [
    {
      label: 'List Devices',
      name: 'List Devices',
      route: 'list_device',
      icon: listIcon,
      component: '/dashboard/device/list/index.tsx',
    },
    {
      label: 'Add Devices',
      name: 'Add Devices',
      route: 'add_device',
      icon: addIcon,
      component: '/dashboard/device/add/index.tsx',
    },
  ],

  //   [PermissionCategory.HELP]: [
  //     {
  //       label: 'List Help Articles',
  //       name: 'List Help Articles',
  //       route: 'list_help',
  //       icon: listIcon,
  //       component: '/dashboard/help/list/index.tsx',
  //     },
  //     {
  //       label: 'Add Help Articles',
  //       name: 'Add Help Articles',
  //       route: 'add_help',
  //       icon: addIcon,
  //       component: '/dashboard/help/add/index.tsx',
  //     },
  //   ],
};
