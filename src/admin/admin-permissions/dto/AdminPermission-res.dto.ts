import { ApiProperty } from '@nestjs/swagger';
import { AdminUser_PermissionDTO } from './AdminUserPermissions.dto';
import { BaseResponse } from 'src/utils/responses';

export class AdminPermissionResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'The permissions related to the admin user. Can be null if no permissions are available.',
    type: [AdminUser_PermissionDTO], // Change to an array
    nullable: true,
  })
  permissions: null | AdminUser_PermissionDTO[];
}
