import { HttpStatus, Injectable } from '@nestjs/common';
import { User } from 'models/user';
import { AdminPermissionResDTO, PermissionsDTO } from './dto';

@Injectable()
export class AdminPermissionService {
  async getAdminPermissions(user: User): Promise<AdminPermissionResDTO> {
    const userProfile = PermissionsDTO.transform(user);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      permissions: userProfile.permissions || null,
    };
  }
}
