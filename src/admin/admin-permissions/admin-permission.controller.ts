import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { Request } from 'express';
import { AdminPermissionService } from './admin-permission.service';
import { AuthGuard } from 'src/middlewares';
import { Authority } from 'src/utils/decorators';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { AdminPermissionResDTO } from './dto';

@ApiTags('Admin-Permissions')
@ApiBearerAuth()
@Controller('admin')
@UseGuards(AuthGuard)
export class AdminPermissionController {
  constructor(
    private readonly adminPermissionService: AdminPermissionService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the activity media.',
    type: AdminPermissionResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Get('/permissions')
  async GetAdminPermissions(@Req() req: Request) {
    const user = req['user'];
    return this.adminPermissionService.getAdminPermissions(user);
  }
}
