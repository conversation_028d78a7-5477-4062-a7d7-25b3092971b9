import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { CreateAdminRegisterDto } from './dto/create-admin-register.dto';
import { InjectModel } from '@nestjs/mongoose';
import { User } from 'models/user';
import { Model } from 'mongoose';
import { UserRepoService } from 'src/repo/user-repo.service';
import { CredentialsAuthUtilsService } from 'src/auth/credentials-auth/credentials-auth-utils.service';
import { CustomLogger } from 'src/common/services';
import { ROLE_VALUES } from 'models/user/user.schema';
import { Request } from 'express';

@Injectable()
export class AdminRegisterService {
  constructor(
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
    private readonly userRepo: UserRepoService,
    private readonly credentialsAuthUtilsService: CredentialsAuthUtilsService,
    private readonly logger: CustomLogger,
  ) {
    this.logger.setContext('AdminRegisterService');
  }

  async create(createAdminRegisterDto: CreateAdminRegisterDto, req: Request) {
    const { name, email, password } = createAdminRegisterDto;

    // Debug log
    this.logger.log(`Received registration request with email: ${email}`);

    try {
      const user = await this.userRepo.findUserByEmail(email, false);

      if (user) {
        if (user.isDeleted) {
          throw new BadRequestException(
            'This account has been deactivated by the admin as per your request. Contact the admin for reactivation.',
          );
        } else {
          throw new BadRequestException(
            'An account with this email already exists.',
          );
        }
      }

      const newUser = await this.userModel.create({
        name,
        email,
        password,
        role: ROLE_VALUES.ADMIN,
        acceptTerms: true,
      });

      // Log created user info
      this.logger.log(`New user created: ${newUser.email}`);

      // The email on newUser is encrypted by a pre-save hook.
      // For the verification email, we need the original plaintext email.
      newUser.email = email;

      this.credentialsAuthUtilsService.sendVerificationEmail(
        newUser,
        req,
        null,
      );

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'Registration complete! Verification email sent.',
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
