import { Modu<PERSON> } from '@nestjs/common';
import { AdminRegisterService } from './admin-register.service';
import { AdminRegisterController } from './admin-register.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'models/user';
import { RepoModule } from 'src/repo/repo.module';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    RepoModule,
    AuthModule,
    CommonModule,
  ],
  controllers: [AdminRegisterController],
  providers: [AdminRegisterService],
})
export class AdminRegisterModule {}
