import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Recipes } from 'models/recipe';
import { isValidObjectId, Model } from 'mongoose';
import { UtilsService } from 'src/common/services';
import { getAllRecipeQueryInterface, RECIPE_VISIBILITY } from './interfaces';
import { AwsS3Service } from 'src/third-party/aws';
import {
  GetAllRecipeResDTO,
  AdminCreateRecipeReqDTO,
  AdminCreateRecipeResDTO,
  DeleteRecipeResDTO,
  AdminGetSingleRecipeResDTO,
  RecipeDTO,
  AdminUpdateRecipeReqDTO,
  AdminUpdateRecipeResDTO,
  CreateBulkRecipesResDTO,
} from './dto';
import { AdminCreateRecipeUtilsService } from './admin-create-recipe-utils.service';
import { User } from 'models/user';
import { UserMealRecords } from 'models/user-records/Meal-Records';
import { Ingredient } from 'models/ingredient';

@Injectable()
export class AdminRecipeService {
  constructor(
    @InjectModel(Recipes.name) private RecipeModel: Model<Recipes>,
    @InjectModel(UserMealRecords.name)
    private userMealRecordsModel: Model<UserMealRecords>,
    @InjectModel(Ingredient.name) private ingredientModel: Model<Ingredient>,
    private readonly utilsService: UtilsService,
    private readonly s3Service: AwsS3Service,
    private readonly createRecipeUtilsService: AdminCreateRecipeUtilsService,
  ) {}

  async getAllRecipes(
    queryFilters: getAllRecipeQueryInterface,
  ): Promise<GetAllRecipeResDTO> {
    const { page, title, mealType, visibility, category } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false, author: null };

    if (title) {
      query.title = { $regex: new RegExp(title, 'i') };
    }

    if (mealType) {
      query.mealType = { $eq: mealType };
    }

    if (category) {
      query.category = { $eq: category };
    }

    if (visibility) {
      if (visibility === RECIPE_VISIBILITY.PUBLIC) {
        query.author = null; // Public recipes have no author
      } else if (visibility === RECIPE_VISIBILITY.PRIVATE) {
        query.author = { $ne: null }; // Private recipes should have an author
      }
    }

    const Recipes = await this.RecipeModel.find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.RecipeModel.countDocuments(query);

    const RecipeResp = Recipes.map((item) => RecipeDTO.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: RecipeResp.length,
      recipes: RecipeResp,
    };
  }

  async getSingleRecipe(id: string): Promise<AdminGetSingleRecipeResDTO> {
    if (!isValidObjectId(id)) {
      throw new BadRequestException('Invalid recipe ID format');
    }

    const Recipe = await this.RecipeModel.findById(id).exec();

    if (!Recipe || Recipe.isDeleted) {
      throw new NotFoundException('Recipe not found');
    }

    // Ingredient name resolution logic
    const firstIngredient = Recipe.ingredients?.[0];
    let ingredientNames: string[] = [];
    if (firstIngredient && isValidObjectId(firstIngredient)) {
      // Fetch all unique ingredient documents
      const uniqueIds = Array.from(
        new Set(Recipe.ingredients.map((id) => id.toString())),
      );
      const ingredientDocs = await this.ingredientModel.find({
        _id: { $in: uniqueIds },
        isDeleted: false,
      });
      // Build a map from id to name
      const idToName = new Map(
        ingredientDocs.map((doc) => [doc._id.toString(), doc.name]),
      );
      // Check if any ingredient IDs were not found
      const foundIds = ingredientDocs.map((doc) => doc._id.toString());
      const missingIds = uniqueIds.filter((id) => !foundIds.includes(id));
      if (missingIds.length > 0) {
        throw new NotFoundException(
          `Ingredients not found for IDs: ${missingIds.join(', ')}`,
        );
      }
      // Map each ingredient in the original array to its name, preserving order and duplicates
      ingredientNames = Recipe.ingredients.map(
        (id) => idToName.get(id.toString()) || id.toString(),
      );
    } else {
      // Already in name format
      ingredientNames = Recipe.ingredients.map((ing) => ing.toString());
    }

    const resp = RecipeDTO.transform(Recipe);
    resp.ingredients = ingredientNames;

    return {
      error: false,
      statusCode: HttpStatus.OK,
      recipe: resp,
    };
  }

  async createSingleRecipe(
    RecipeData: AdminCreateRecipeReqDTO,
    thumbnailFile: Express.Multer.File,
  ): Promise<AdminCreateRecipeResDTO> {
    if (!thumbnailFile) {
      throw new BadRequestException('Please provide thumbnail !!');
    }

    const parsedNutritions =
      this.createRecipeUtilsService.parseAndValidateNutritionData(
        RecipeData.nutritionByQuantity,
      );

    this.createRecipeUtilsService.validateUniqueQuantities(parsedNutritions);
    this.createRecipeUtilsService.validateNutritionByQuantity(parsedNutritions);

    const createData = this.createRecipeUtilsService.buildRecipeData(
      RecipeData,
      parsedNutritions,
    );

    createData.thumbnailUrl =
      await this.createRecipeUtilsService.uploadThumbnail(thumbnailFile);

    const newRecipe = await this.RecipeModel.create(createData);
    const resp = RecipeDTO.transform(newRecipe);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Recipe created successfully !!',
      recipe: resp,
    };
  }

  async updateRecipe(
    id: string,
    updateBody: AdminUpdateRecipeReqDTO,
    thumbnailFile: Express.Multer.File,
  ): Promise<AdminUpdateRecipeResDTO> {
    const {
      title,
      ingredients,
      directions,
      timeToPrep,
      mealType,
      nutritionByQuantity,
      isPublished,
      category,
    } = updateBody;

    const recipe = await this.RecipeModel.findById(id);

    if (!recipe || recipe.isDeleted) {
      throw new NotFoundException('Recipe not found');
    }

    const existingUsersMealRecords = await this.userMealRecordsModel.findOne({
      isDeleted: false,
      meals: {
        $elemMatch: {
          recipeId: recipe._id.toString(),
        },
      },
    });

    if (existingUsersMealRecords) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'This recipe cannot be updated because it is linked to existing user meal records.\n Please contact developer support for assistance.',
        Recipe: null,
      };
    }

    const updateData: any = {};

    if (title) updateData.title = title;
    if (ingredients) updateData.ingredients = ingredients.split(',');
    if (directions) updateData.directions = directions;
    if (timeToPrep) updateData.timeToPrep = timeToPrep;
    if (mealType) updateData.mealType = mealType;
    if (isPublished) updateData.isPublished = isPublished;
    if (category) updateData.category = category;
    if (nutritionByQuantity) {
      const parsedNutritions =
        this.createRecipeUtilsService.parseAndValidateNutritionData(
          nutritionByQuantity,
        );

      this.createRecipeUtilsService.validateUniqueQuantities(parsedNutritions);
      this.createRecipeUtilsService.validateNutritionByQuantity(
        parsedNutritions,
      );

      updateData.nutritionByQuantity = parsedNutritions;
    }

    if (thumbnailFile) {
      updateData.thumbnailUrl = (
        await this.s3Service.uploadFile(thumbnailFile)
      ).Location;
    }

    const updatedRecipe = await this.RecipeModel.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true },
    ).exec();

    const resp = RecipeDTO.transform(updatedRecipe);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Updated Recipe Successfully !!',
      Recipe: resp,
    };
  }

  async deleteRecipe(id: string): Promise<DeleteRecipeResDTO> {
    const recipe = await this.RecipeModel.findById(id);

    if (!recipe || recipe.isDeleted) {
      throw new NotFoundException('Recipe not found');
    }

    recipe.isDeleted = true;
    await recipe.save();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Recipe deleted successfully',
    };
  }

  async createBulkRecipes(
    csvFile: Express.Multer.File,
    user: User,
  ): Promise<CreateBulkRecipesResDTO> {
    if (!csvFile) {
      throw new BadRequestException('Please provide the CSV file to upload.');
    }

    const uncompiledRecipes: { row: any; reason: string }[] = [];
    const compiledRecipes: { recipe: Recipes; status: string }[] = [];

    const rows = await this.createRecipeUtilsService.parseCSV(csvFile);

    for (const row of rows) {
      try {
        const recipeData =
          this.createRecipeUtilsService.extractRecipeDetails(row);

        const parsedNutritions =
          this.createRecipeUtilsService.parseAndValidateNutritionData(
            recipeData.nutritionByQuantity,
          );

        this.createRecipeUtilsService.validateUniqueQuantities(
          parsedNutritions,
        );
        this.createRecipeUtilsService.validateNutritionByQuantity(
          parsedNutritions,
        );

        const existingRecipe = await this.RecipeModel.findOne({
          title: recipeData.title,
          author: null,
        });

        if (existingRecipe) {
          throw new BadRequestException(
            'A recipe with this title already exists.',
          );
        } else {
          // Create new recipe
          const newRecipe = this.createRecipeUtilsService.buildRecipeData(
            recipeData,
            parsedNutritions,
          );
          const savedRecipe = await this.RecipeModel.create(newRecipe);
          compiledRecipes.push({ recipe: savedRecipe, status: 'CREATED' });
        }
      } catch (error) {
        uncompiledRecipes.push({
          row,
          reason: error.message || 'Invalid recipe data.',
        });
      }
    }

    await this.createRecipeUtilsService.sendReport(
      compiledRecipes,
      uncompiledRecipes,
      user,
    );

    const createdCount = compiledRecipes.filter(
      (r) => r.status === 'CREATED',
    ).length;
    const updatedCount = compiledRecipes.filter(
      (r) => r.status === 'UPDATED',
    ).length;

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: `${createdCount > 0 ? createdCount + ' recipes created. ' : ''}${updatedCount > 0 ? updatedCount + ' recipes updated. ' : ''}${uncompiledRecipes.length > 0 ? `Unable to add ${uncompiledRecipes.length} recipes. ` : ''}${uncompiledRecipes.length > 0 ? 'Report has been sent to you via email.' : ''}`,
    };
  }
}
