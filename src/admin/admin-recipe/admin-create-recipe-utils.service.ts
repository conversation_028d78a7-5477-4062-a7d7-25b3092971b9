import { BadRequestException, Injectable } from '@nestjs/common';
import {
  PIECE_NUTRITION_QUANTITY,
  Recipes,
  SERVING_NUTRITION_QUANTITY,
  SLICE_NUTRITION_QUANTITY,
} from 'models/recipe';
import {
  CustomConfigService,
  CustomLogger,
  EmailService,
} from 'src/common/services';
import { AwsS3Service } from 'src/third-party/aws';
import { NutritionByQuantityDTO } from './dto/Recipe.dto';
import { validateSync } from 'class-validator';
import { User } from 'models/user';

import * as stream from 'stream';
import * as csvParser from 'csv-parser';
import { v4 as uuid } from 'uuid';
import { Parser } from 'json2csv';

@Injectable()
export class AdminCreateRecipeUtilsService {
  constructor(
    private readonly s3Service: AwsS3Service,
    private readonly logger: CustomLogger,
    private readonly emailService: EmailService,
  ) {}

  validateNutritionByQuantity(
    nutritionByQuantity: NutritionByQuantityDTO[],
  ): void {
    const quantities = nutritionByQuantity.map((item) => item.quantity);

    const servingQuantities = quantities.filter((q) =>
      Object.values(SERVING_NUTRITION_QUANTITY).includes(
        q as SERVING_NUTRITION_QUANTITY,
      ),
    );
    const sliceNutritionQuantities = quantities.filter((q) =>
      Object.values(SLICE_NUTRITION_QUANTITY).includes(
        q as SLICE_NUTRITION_QUANTITY,
      ),
    );
    const pieceNutritionQuantities = quantities.filter((q) =>
      Object.values(PIECE_NUTRITION_QUANTITY).includes(
        q as PIECE_NUTRITION_QUANTITY,
      ),
    );

    if (
      sliceNutritionQuantities.length > 0 &&
      pieceNutritionQuantities.length > 0
    ) {
      throw new BadRequestException(
        'Cannot have both slice and piece nutrition for a recipe.',
      );
    }

    if (
      servingQuantities.length > 0 &&
      servingQuantities.length <
        Object.values(SERVING_NUTRITION_QUANTITY).length
    ) {
      throw new BadRequestException(
        `All serving quantities (small, medium, large) are required.`,
      );
    }

    if (
      sliceNutritionQuantities.length > 0 &&
      sliceNutritionQuantities.length <
        Object.values(SLICE_NUTRITION_QUANTITY).length
    ) {
      throw new BadRequestException(
        `All slice nutrition quantities (1, 2, 3, 4) are required.`,
      );
    }

    if (
      pieceNutritionQuantities.length > 0 &&
      pieceNutritionQuantities.length <
        Object.values(PIECE_NUTRITION_QUANTITY).length
    ) {
      throw new BadRequestException(
        `All piece nutrition quantities (1, 2, 3, 4) are required.`,
      );
    }
  }

  parseAndValidateNutritionData(
    nutritionData: string,
  ): NutritionByQuantityDTO[] {
    let parsedNutritions: NutritionByQuantityDTO[] = [];
    let parsedData: any;
    try {
      parsedData = JSON.parse(nutritionData);

      if (!Array.isArray(parsedData)) {
        throw new Error();
      }
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException('Unable to parse nutrition data...');
    }

    parsedNutritions = parsedData.map((item) => {
      const newData = new NutritionByQuantityDTO();
      const quantityValue = String(item.quantity);

      const isServing = Object.values(SERVING_NUTRITION_QUANTITY).includes(
        quantityValue.toLowerCase() as any,
      );
      const isSlice = Object.values(SLICE_NUTRITION_QUANTITY).includes(
        quantityValue as any,
      );
      const isPiece = Object.values(PIECE_NUTRITION_QUANTITY).includes(
        quantityValue as any,
      );

      if (isServing) {
        newData.quantity =
          quantityValue.toLowerCase() as SERVING_NUTRITION_QUANTITY;
      } else if (isSlice) {
        newData.quantity = quantityValue as SLICE_NUTRITION_QUANTITY;
      } else if (isPiece) {
        newData.quantity = quantityValue as PIECE_NUTRITION_QUANTITY;
      } else {
        throw new BadRequestException(
          `Invalid quantity value: ${item.quantity}`,
        );
      }
      newData.protein = item.protein;
      newData.calories = item.calories;
      newData.fats = item.fats;
      newData.fiber = item.fiber;
      newData.carbs = item.carbs;
      return newData;
    });

    for (const item of parsedNutritions) {
      const errors = validateSync(item);
      if (errors.length > 0) {
        throw new BadRequestException(
          `Invalid Nutrition data : ${Object.values(errors[0].constraints).reverse()[0]}`,
        );
      }
    }

    return parsedNutritions;
  }

  validateUniqueQuantities(parsedNutritions: NutritionByQuantityDTO[]): void {
    const uniqueQuantities = new Set<string>();

    for (const item of parsedNutritions) {
      if (uniqueQuantities.has(item.quantity)) {
        throw new BadRequestException(
          `Duplicate quantity found: ${item.quantity}`,
        );
      }

      uniqueQuantities.add(item.quantity);
    }
  }

  buildRecipeData(
    RecipeData: any,
    parsedNutritions: NutritionByQuantityDTO[],
  ): any {
    return {
      title: RecipeData.title,
      ingredients: Array.isArray(RecipeData.ingredients)
        ? RecipeData.ingredients
        : RecipeData.ingredients.split(','),
      directions: RecipeData.directions,
      timeToPrep: RecipeData.timeToPrep,
      mealType: RecipeData.mealType,
      nutritionByQuantity: parsedNutritions,
      isPublished: RecipeData.isPublished,
      thumbnailUrl: RecipeData.thumbnailUrl,
      category: RecipeData.category,
    };
  }

  async uploadThumbnail(thumbnailFile: Express.Multer.File): Promise<string> {
    return (await this.s3Service.uploadFile(thumbnailFile)).Location;
  }

  // ---------------------------------------------------------------------------
  extractRecipeDetails(row: any) {
    return {
      title: row.title,
      ingredients: row.ingredients.split(','),
      directions: row.directions,
      timeToPrep: row.timeToPrep,
      mealType: row.mealType,
      nutritionByQuantity: row.nutritionByQuantity,
      isPublished: row.isPublished === 'true',
      thumbnailUrl: row.thumbnailUrl,
      category: row.category,
    };
  }

  async sendReport(
    compiledRecipes: { recipe: Recipes; status: string }[],
    uncompiledRecipes: { row: any; reason: string }[],
    user: User,
  ) {
    if (uncompiledRecipes.length > 0) {
      const csvParser = new Parser({ fields: ['row', 'reason'] });
      const csvData = csvParser.parse(uncompiledRecipes);
      const csvBuffer = Buffer.from(csvData);

      const csvFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: `uncompiled_recipes_${uuid()}.csv`,
        encoding: '7bit',
        mimetype: 'text/csv',
        buffer: csvBuffer,
        size: csvBuffer.length,
        stream: null,
        destination: '',
        filename: '',
        path: '',
      };

      const url = (await this.s3Service.uploadFile(csvFile)).Location;

      const { subject, emailBody, fromEmail, AdminCSVReportEmailTemplate } =
        CustomConfigService.PROPERTIES.BulkRecipesCreationReportEmail;

      const html = AdminCSVReportEmailTemplate.replace('$$reportUrl', url);

      await this.emailService.sendTextMail({
        fromEmail,
        toEmail: user.email,
        subject,
        textBody: emailBody,
        html,
      });
    }
  }

  async parseCSV(csvFile: Express.Multer.File): Promise<any[]> {
    const bufferStream = new stream.PassThrough();
    bufferStream.end(csvFile.buffer);

    return new Promise((resolve, reject) => {
      const rows: any[] = [];
      bufferStream
        .pipe(csvParser())
        .on('data', (row) => rows.push(row))
        .on('end', () => resolve(rows))
        .on('error', reject);
    });
  }
}
