import { ApiProperty } from '@nestjs/swagger';
import { RecipeDTO } from './Recipe.dto';
import { BaseResponse } from 'src/utils/responses';

export class AdminCreateRecipeResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'recipe created successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'object containing details of the created recipe',
    type: RecipeDTO,
  })
  recipe: RecipeDTO;
}
