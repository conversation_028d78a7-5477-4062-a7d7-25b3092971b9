import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsString,
  IsBoolean,
  ValidateNested,
  IsArray,
  IsMongoId,
  IsEnum,
  Min,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Types } from 'mongoose';
import {
  MEAL_TYPES,
  PIECE_NUTRITION_QUANTITY,
  Recipes,
  SERVING_NUTRITION_QUANTITY,
  SLICE_NUTRITION_QUANTITY,
} from 'models/recipe';
import { DIET_PREFERENCE } from 'models/user';

export class NutritionByQuantityDTO {
  @ApiProperty({
    description: 'Quantity type for nutrition',
    enum: [
      ...Object.values(SERVING_NUTRITION_QUANTITY),
      ...Object.values(SLICE_NUTRITION_QUANTITY),
      ...Object.values(PIECE_NUTRITION_QUANTITY),
    ],
  })
  @IsEnum([
    ...Object.values(SERVING_NUTRITION_QUANTITY),
    ...Object.values(SLICE_NUTRITION_QUANTITY),
    ...Object.values(PIECE_NUTRITION_QUANTITY),
  ])
  @IsNotEmpty()
  quantity:
    | SERVING_NUTRITION_QUANTITY
    | SLICE_NUTRITION_QUANTITY
    | PIECE_NUTRITION_QUANTITY;

  @ApiProperty({ description: 'Amount of protein in grams' })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  protein: number;

  @ApiProperty({ description: 'Amount of calories in grams' })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  calories: number;

  @ApiProperty({ description: 'Amount of fats in grams' })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  fats: number;

  @ApiProperty({ description: 'Amount of fiber in grams' })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  fiber: number;

  @ApiProperty({ description: 'Amount of carbohydrates in grams' })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  carbs: number;

  static transform(object: NutritionByQuantityDTO): NutritionByQuantityDTO {
    const transformedObj = new NutritionByQuantityDTO();
    transformedObj.quantity = object.quantity;
    transformedObj.protein = object.protein;
    transformedObj.calories = object.calories;
    transformedObj.fats = object.fats;
    transformedObj.fiber = object.fiber;
    transformedObj.carbs = object.carbs;
    return transformedObj;
  }
}

export class RecipeDTO {
  @ApiProperty({ description: 'Unique identifier of the recipe' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Title of the recipe' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'List of ingredients' })
  @IsArray()
  @IsNotEmpty()
  ingredients: string[];

  @ApiProperty({ description: 'Cooking directions' })
  @IsString()
  @IsNotEmpty()
  directions: string;

  @ApiProperty({ description: 'Time required for preparation' })
  @Min(10)
  @IsNumber()
  @IsNotEmpty()
  timeToPrep: number;

  @ApiProperty({ description: 'Thumbnail URL of the recipe' })
  @IsString()
  @IsNotEmpty()
  thumbnailUrl: string;

  @ApiProperty({ description: 'Meal type (e.g., breakfast, lunch, dinner)' })
  @IsEnum(MEAL_TYPES)
  @IsNotEmpty()
  mealType: MEAL_TYPES;

  @ApiProperty({
    description: 'Nutritional breakdown by quantity',
    type: () => [NutritionByQuantityDTO],
  })
  @ValidateNested({ each: true })
  @Type(() => NutritionByQuantityDTO)
  @IsArray()
  @IsNotEmpty()
  nutritionByQuantity: NutritionByQuantityDTO[];

  @ApiProperty({ description: 'Indicates if the recipe is published' })
  @IsBoolean()
  @IsNotEmpty()
  isPublished: boolean;

  @ApiProperty({ description: 'Indicates if the recipe is deleted' })
  @IsBoolean()
  @IsNotEmpty()
  isDeleted: boolean;

  @ApiProperty({
    description: 'Author ID (null for admin recipes, user ID for user recipes)',
  })
  @IsMongoId()
  author: Types.ObjectId;

  @ApiProperty({
    description: 'category of dish',
    enum: DIET_PREFERENCE,
    example: DIET_PREFERENCE.VEG,
  })
  @IsEnum(DIET_PREFERENCE)
  category: DIET_PREFERENCE;

  createdAt: Date;

  static transform(object: Recipes): RecipeDTO {
    const transformedObj = new RecipeDTO();
    transformedObj.id = object._id.toString();
    transformedObj.title = object.title;
    transformedObj.ingredients = object.ingredients.map((ing) =>
      ing.toString(),
    );
    transformedObj.directions = object.directions;
    transformedObj.timeToPrep = object.timeToPrep;
    transformedObj.thumbnailUrl = object.thumbnailUrl;
    transformedObj.mealType = object.mealType as MEAL_TYPES;
    transformedObj.nutritionByQuantity = object.nutritionByQuantity.map(
      NutritionByQuantityDTO.transform,
    );
    transformedObj.isPublished = object.isPublished;
    transformedObj.category = object.category as DIET_PREFERENCE;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.author = object.author;
    transformedObj.createdAt = (object as any).createdAt;
    return transformedObj;
  }
}
