import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { RecipeDTO } from './Recipe.dto';

export class GetAllRecipeResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Total number of recipes available',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Number of recipes returned in the current request',
    example: 20,
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of recipes retrieved in the request',
    type: [RecipeDTO],
  })
  recipes: RecipeDTO[];
}
