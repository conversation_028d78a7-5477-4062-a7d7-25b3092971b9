import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
  Min,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { MEAL_TYPES } from 'models/recipe';
import { DIET_PREFERENCE } from 'models/user';

export class AdminCreateRecipeReqDTO {
  @ApiProperty({ description: 'Title of the recipe' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'List of ingredients' })
  @IsString()
  @IsNotEmpty()
  ingredients: string;

  @ApiProperty({ description: 'Cooking directions' })
  @IsString()
  @IsNotEmpty()
  directions: string;

  @ApiProperty({ description: 'Time required for preparation' })
  @Min(1)
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  timeToPrep: number;

  @ApiProperty({ description: 'Meal type category', enum: MEAL_TYPES })
  @IsEnum(MEAL_TYPES)
  @IsNotEmpty()
  mealType: MEAL_TYPES;

  @ApiProperty({ description: 'Nutritional information by quantity' })
  @IsString()
  @IsNotEmpty()
  nutritionByQuantity: string;

  @ApiProperty({ description: 'Publication status of the recipe' })
  @IsBoolean()
  @IsNotEmpty()
  @Transform(({ value }) => value === 'true' || value === true)
  isPublished: boolean;

  @ApiProperty({
    description: 'category of dish',
    enum: DIET_PREFERENCE,
    example: DIET_PREFERENCE.VEG,
  })
  @IsEnum(DIET_PREFERENCE)
  @IsNotEmpty()
  category: DIET_PREFERENCE;
}
