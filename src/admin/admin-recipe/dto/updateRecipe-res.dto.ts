import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { RecipeDTO } from './Recipe.dto';
import { IsOptional } from 'class-validator';

export class AdminUpdateRecipeResDTO extends BaseResponse {
  @ApiProperty({
    description:
      'Response message indicating the result of the update operation',
    example: 'Mood media updated successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'Updated mood media object',
    type: RecipeDTO,
  })
  @IsOptional()
  Recipe?: RecipeDTO | null;
}
