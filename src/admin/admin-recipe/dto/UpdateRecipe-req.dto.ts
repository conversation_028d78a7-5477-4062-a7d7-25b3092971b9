import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsNumber,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { MEAL_TYPES } from 'models/recipe';
import { DIET_PREFERENCE } from 'models/user';

export class AdminUpdateRecipeReqDTO {
  @ApiPropertyOptional({
    description: 'Title of the recipe',
    example: 'Spaghetti Bolognese',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Ingredients list',
    example: 'Tomatoes, Ground Beef, Pasta',
  })
  @IsOptional()
  @IsString()
  ingredients?: string;

  @ApiPropertyOptional({
    description: 'Cooking directions',
    example: 'Boil pasta, cook beef, mix with sauce',
  })
  @IsOptional()
  @IsString()
  directions?: string;

  @ApiPropertyOptional({
    description: 'Time to prepare the dish (in minutes)',
    example: 30,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  timeToPrep?: number;

  @ApiPropertyOptional({
    description: 'Type of meal',
    enum: MEAL_TYPES,
    example: MEAL_TYPES.DINNER,
  })
  @IsOptional()
  @IsEnum(MEAL_TYPES)
  mealType?: MEAL_TYPES;

  @ApiPropertyOptional({
    description: 'category of dish',
    enum: DIET_PREFERENCE,
    example: DIET_PREFERENCE.VEG,
  })
  @IsOptional()
  @IsEnum(DIET_PREFERENCE)
  category?: DIET_PREFERENCE;

  @ApiPropertyOptional({ description: 'Nutritional information by quantity' })
  @IsString()
  @IsOptional()
  nutritionByQuantity: string;

  @ApiPropertyOptional({
    description: 'Whether the recipe is published',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isPublished?: boolean;
}
