import {
  Controller,
  Get,
  Query,
  Param,
  Post,
  Body,
  Put,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Req,
} from '@nestjs/common';
import { Authority } from 'src/utils/decorators';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminRecipeService } from './admin-recipe.service';
import {
  GetAllRecipeResDTO,
  AdminGetSingleRecipeResDTO,
  AdminCreateRecipeResDTO,
  DeleteRecipeResDTO,
  AdminUpdateRecipeResDTO,
  AdminCreateRecipeReqDTO,
  AdminUpdateRecipeReqDTO,
} from './dto';
import { ErrorResponse } from 'src/utils/responses';
import { AuthGuard } from 'src/middlewares';
import { getAllRecipeQueryInterface } from './interfaces';
import {
  AllowedImageExtensions,
  getMulterMediaOptions,
} from 'src/utils/multer';
import { FileInterceptor } from '@nestjs/platform-express';
import { getMulterCSVOptions } from 'src/utils/multer/multer.utils';
import { Request } from 'express';

@ApiTags('Admin-Recipes')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('/admin/recipes')
export class AdminRecipeController {
  constructor(private readonly adminRecipeService: AdminRecipeService) {}

  // Get All Recipe
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the Recipe .',
    type: GetAllRecipeResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    description: 'The page number for pagination (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @ApiQuery({
    name: 'page',
    description: 'The title for searching (optional)',
    required: false,
    type: String,
    example: '1',
  })
  @Authority()
  @Get()
  async getAllRecipes(@Query() filterQueries: getAllRecipeQueryInterface) {
    return this.adminRecipeService.getAllRecipes(filterQueries);
  }

  // Get Single Recipe
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the Recipe .',
    type: AdminGetSingleRecipeResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Get('/:RecipeId')
  async getSingleRecipe(@Param('RecipeId') id: string) {
    return this.adminRecipeService.getSingleRecipe(id);
  }

  // Create Recipe
  @ApiResponse({
    status: 201,
    description: 'Successfully created Recipe .',
    type: AdminCreateRecipeResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @UseInterceptors(
    FileInterceptor(
      'thumbnailFile',
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  @Authority()
  @Post()
  async createRecipe(
    @Body() createBody: AdminCreateRecipeReqDTO,
    @UploadedFile() thumbnailFile: Express.Multer.File,
  ) {
    return this.adminRecipeService.createSingleRecipe(
      createBody,
      thumbnailFile,
    );
  }

  // Update Recipe
  @ApiResponse({
    status: 200,
    description: 'Successfully updated the Recipe .',
    type: AdminUpdateRecipeResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseInterceptors(
    FileInterceptor(
      'thumbnailFile',
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedImageExtensions,
      }),
    ),
  )
  @Authority()
  @Put('/:RecipeId')
  async updateRecipe(
    @Param('RecipeId') id: string,
    @Body() updateBody: AdminUpdateRecipeReqDTO,
    @UploadedFile() thumbnailFile: Express.Multer.File,
  ) {
    return this.adminRecipeService.updateRecipe(id, updateBody, thumbnailFile);
  }

  // Delete Recipe
  @ApiResponse({
    status: 200,
    description: 'Successfully deleted the Recipe .',
    type: DeleteRecipeResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Put('delete/:RecipeId')
  async deleteRecipe(@Param('RecipeId') id: string) {
    return this.adminRecipeService.deleteRecipe(id);
  }

  // Create Recipe
  @ApiResponse({
    status: 201,
    description: 'Successfully created Recipe .',
    type: AdminCreateRecipeResDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  @UseInterceptors(
    FileInterceptor('csvFile', getMulterCSVOptions({ fileSize: 50 })),
  )
  @Authority()
  @Post('/bulk')
  async createBulkRecipes(
    @Req() req: Request,
    @UploadedFile() csvFile: Express.Multer.File,
  ) {
    const user = req['user'];
    return this.adminRecipeService.createBulkRecipes(csvFile, user);
  }
}
