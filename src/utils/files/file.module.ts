import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { FileController } from './file.controller';
import { FileService } from './file.service';
import { FileUrlService } from './file-url.service';
import { AwsS3Service } from '../../third-party/aws/s3.service';
import { ClamAVService } from './clamav.service';
import { FileModel, FileSchema } from './file.schema';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([{ name: FileModel.name, schema: FileSchema }]),
    CommonModule,
    RepoModule,
  ],
  controllers: [FileController],
  providers: [FileService, FileUrlService, AwsS3Service, ClamAVService],
  exports: [FileService, FileUrlService],
})
export class FileModule {}
