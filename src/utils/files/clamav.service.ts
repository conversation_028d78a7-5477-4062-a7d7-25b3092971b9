import { Injectable, Logger } from '@nestjs/common';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs';

const execAsync = promisify(exec);

@Injectable()
export class ClamAVService {
  private readonly logger = new Logger(ClamAVService.name);

  async scanFile(
    filePath: string,
  ): Promise<{ isClean: boolean; result: string }> {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error('File not found');
      }

      // Run ClamAV scan
      const { stdout, stderr } = await execAsync(`clamscan ${filePath}`);

      const result = stdout + stderr;
      const isClean = !result.includes('FOUND') && result.includes('OK');

      this.logger.log(
        `Virus scan result for ${filePath}: ${isClean ? 'CLEAN' : 'INFECTED'}`,
      );

      return { isClean, result: result.trim() };
    } catch (error) {
      this.logger.error(`Error scanning file ${filePath}:`, error);
      throw new Error(`Virus scan failed: ${error.message}`);
    }
  }

  async isServiceAvailable(): Promise<boolean> {
    try {
      await execAsync('clamscan --version');
      return true;
    } catch (error) {
      this.logger.error('ClamAV service not available:', error);
      return false;
    }
  }
}
