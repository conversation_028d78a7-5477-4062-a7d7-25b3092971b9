import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type FileDocument = FileModel & Document;

@Schema({ timestamps: true })
export class FileModel {
  @Prop({ required: true })
  filename: string;

  @Prop({ required: true })
  originalName: string;

  @Prop({ required: true })
  s3Url: string;

  @Prop({ required: true })
  s3Key: string;

  @Prop({ required: true })
  expiryAt: Date;

  @Prop({ default: false })
  isExpired: boolean;

  @Prop({ required: true })
  fileSize: number;

  @Prop({ required: true })
  mimeType: string;

  @Prop({ default: 'clean' })
  virusScanStatus: string; // 'clean', 'infected', 'scanning', 'error'

  @Prop()
  uploadedBy: string;

  createdAt: Date;
  updatedAt: Date;
}

export const FileSchema = SchemaFactory.createForClass(FileModel);
