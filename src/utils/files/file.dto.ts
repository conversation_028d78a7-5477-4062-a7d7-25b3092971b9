import { IsString, IsOptional, IsNumber } from 'class-validator';

export class UploadFileDto {
  @IsOptional()
  @IsString()
  uploadedBy?: string;

  @IsOptional()
  @IsNumber()
  expiryDays?: number = 30; // Default 30 days
}

export class FileResponseDto {
  id: string;
  filename: string;
  originalName: string;
  s3Url: string;
  expiryAt: Date;
  isExpired: boolean;
  fileSize: number;
  mimeType: string;
  virusScanStatus: string;
  uploadedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}
