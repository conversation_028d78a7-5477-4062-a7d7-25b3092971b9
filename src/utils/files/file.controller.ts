import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
  Logger,
  UseGuards,
  SetMetadata,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileService } from './file.service';
import { UploadFileDto, FileResponseDto } from './file.dto';
import { AuthGuard } from 'src/middlewares';

@Controller('files')
export class FileController {
  private readonly logger = new Logger(FileController.name);

  constructor(private readonly fileService: FileService) {}

  @Post('upload')
  @UseGuards(AuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadFileDto: UploadFileDto,
  ): Promise<FileResponseDto> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Validate file size (100MB limit)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      throw new BadRequestException('File size exceeds 100MB limit');
    }

    this.logger.log(`File upload request: ${file.originalname}`);
    return this.fileService.uploadFile(file, uploadFileDto);
  }

  @Get(':id')
  @UseGuards(AuthGuard)
  async getFileById(@Param('id') id: string): Promise<FileResponseDto> {
    return this.fileService.getFileById(id);
  }

  @Put(':id/update-url')
  @UseGuards(AuthGuard)
  async updateFileUrl(@Param('id') id: string): Promise<FileResponseDto> {
    return this.fileService.updateFileUrl(id);
  }

  @Delete(':id')
  @UseGuards(AuthGuard)
  @SetMetadata('authority', true)
  async deleteFile(@Param('id') id: string): Promise<{ message: string }> {
    await this.fileService.deleteFile(id);
    return { message: 'File deleted successfully' };
  }

  @Get()
  @UseGuards(AuthGuard)
  async getAllFiles(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('uploadedBy') uploadedBy?: string,
  ) {
    return this.fileService.getAllFiles(page, limit, uploadedBy);
  }
}
