import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { FileService } from './file.service';

@Injectable()
export class FileUrlService {
  private readonly logger = new Logger(FileUrlService.name);

  constructor(private readonly fileService: FileService) {}

  /**
   * Get file URL by fileId with automatic refresh if expired
   * This is a common function that can be used across the application
   */
  async getFileUrl(fileId: string): Promise<{ url: string; expiryAt: Date }> {
    try {
      const fileRecord = await this.fileService.getFileById(fileId);

      // Check if file is expired or URL needs refresh
      const now = new Date();
      const isExpired = now > fileRecord.expiryAt || fileRecord.isExpired;

      if (isExpired) {
        this.logger.log(
          `File URL expired for fileId: ${fileId}, refreshing...`,
        );
        // Refresh the file URL
        const updatedFileRecord = await this.fileService.updateFileUrl(fileId);
        return {
          url: updatedFileRecord.s3Url,
          expiryAt: updatedFileRecord.expiryAt,
        };
      }

      return {
        url: fileRecord.s3Url,
        expiryAt: fileRecord.expiryAt,
      };
    } catch (error) {
      this.logger.error(`Failed to get file URL for fileId: ${fileId}`, error);
      throw new BadRequestException(`Failed to get file URL: ${error.message}`);
    }
  }

  /**
   * Get multiple file URLs with automatic refresh if expired
   */
  async getMultipleFileUrls(
    fileIds: string[],
  ): Promise<
    Array<{ fileId: string; url?: string; expiryAt?: Date; error?: string }>
  > {
    return Promise.all(
      fileIds.map(async (fileId) => {
        try {
          const { url, expiryAt } = await this.getFileUrl(fileId);
          return { fileId, url, expiryAt };
        } catch (error) {
          this.logger.error(`Failed to get URL for fileId: ${fileId}`, error);
          return { fileId, error: 'File not accessible' };
        }
      }),
    );
  }

  /**
   * Check if file URL is expired without refreshing
   */
  async isFileUrlExpired(fileId: string): Promise<boolean> {
    try {
      const fileRecord = await this.fileService.getFileById(fileId);
      const now = new Date();
      return now > fileRecord.expiryAt || fileRecord.isExpired;
    } catch {
      return true; // Consider as expired if file not found
    }
  }

  /**
   * Refresh file URL if expired
   */
  async refreshFileUrlIfExpired(
    fileId: string,
  ): Promise<{ url: string; expiryAt: Date; wasRefreshed: boolean }> {
    const isExpired = await this.isFileUrlExpired(fileId);

    if (isExpired) {
      const { url, expiryAt } = await this.getFileUrl(fileId);
      return { url, expiryAt, wasRefreshed: true };
    } else {
      const fileRecord = await this.fileService.getFileById(fileId);
      return {
        url: fileRecord.s3Url,
        expiryAt: fileRecord.expiryAt,
        wasRefreshed: false,
      };
    }
  }
}
