import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { FileDocument, FileModel } from './file.schema';

import { ClamAVService } from './clamav.service';
import { UploadFileDto, FileResponseDto } from './file.dto';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { AwsS3Service } from 'src/third-party/aws';

@Injectable()
export class FileService {
  private readonly logger = new Logger(FileService.name);

  constructor(
    @InjectModel(FileModel.name) private fileModel: Model<FileDocument>,
    private s3Service: AwsS3Service,
    private clamAVService: ClamAVService,
  ) {}

  async uploadFile(
    file: Express.Multer.File,
    uploadFileDto: UploadFileDto,
  ): Promise<FileResponseDto> {
    const tempFilePath = path.join(
      '/tmp',
      `${crypto.randomUUID()}-${file.originalname}`,
    );

    try {
      // Write file to temp location for virus scanning
      fs.writeFileSync(tempFilePath, file.buffer);

      // Step 1: Scan file for viruses
      this.logger.log(`Scanning file: ${file.originalname}`);
      const scanResult = await this.clamAVService.scanFile(tempFilePath);

      if (!scanResult.isClean) {
        throw new BadRequestException('File contains virus or malware');
      }

      // Step 2: Upload to S3
      this.logger.log(`Uploading clean file to S3: ${file.originalname}`);
      const { Location, Key } = await this.s3Service.uploadFile(file);

      // Step 3: Save to database
      const expiryDate = new Date();
      expiryDate.setDate(
        expiryDate.getDate() + (uploadFileDto.expiryDays || 30),
      );

      const fileRecord = new this.fileModel({
        filename: crypto.randomUUID(),
        originalName: file.originalname,
        s3Url: Location,
        s3Key: Key,
        expiryAt: expiryDate,
        fileSize: file.size,
        mimeType: file.mimetype,
        virusScanStatus: 'clean',
        uploadedBy: uploadFileDto.uploadedBy,
      });

      const savedFile = await fileRecord.save();
      this.logger.log(`File saved to database: ${savedFile.id}`);

      return this.mapToResponseDto(savedFile);
    } catch (error) {
      this.logger.error(`Error uploading file:`, error);
      throw error;
    } finally {
      // Clean up temp file
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    }
  }

  async getFileById(id: string): Promise<FileResponseDto> {
    const file = await this.fileModel.findById(id);
    if (!file) {
      throw new NotFoundException('File not found');
    }

    // Check if file is expired
    const now = new Date();
    const isExpired = now > file.expiryAt;

    if (isExpired && !file.isExpired) {
      await this.fileModel.findByIdAndUpdate(id, { isExpired: true });
      file.isExpired = true;
    }

    return this.mapToResponseDto(file);
  }

  async updateFileUrl(id: string): Promise<FileResponseDto> {
    const file = await this.fileModel.findById(id);
    if (!file) {
      throw new NotFoundException('File not found');
    }

    try {
      // Generate new presigned URL
      const newS3Url = await this.s3Service.getSignedUrl(file.s3Key, 3600);

      // Update expiry date (extend by 1 day)
      const newExpiryDate = new Date();
      newExpiryDate.setDate(newExpiryDate.getDate() + 1);

      const updatedFile = await this.fileModel.findByIdAndUpdate(
        id,
        {
          s3Url: newS3Url,
          expiryAt: newExpiryDate,
          isExpired: false,
          updatedAt: new Date(),
        },
        { new: true },
      );

      this.logger.log(`File URL updated: ${id}`);
      return this.mapToResponseDto(updatedFile);
    } catch (error) {
      this.logger.error(`Error updating file URL:`, error);
      throw new BadRequestException('Failed to update file URL');
    }
  }

  async deleteFile(id: string): Promise<void> {
    const file = await this.fileModel.findById(id);
    if (!file) {
      throw new NotFoundException('File not found');
    }

    try {
      // Delete from S3
      await this.s3Service.deleteFile(file.s3Key);

      // Delete from database
      await this.fileModel.findByIdAndDelete(id);

      this.logger.log(`File deleted: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting file:`, error);
      throw new BadRequestException('Failed to delete file');
    }
  }

  async getAllFiles(
    page: number = 1,
    limit: number = 10,
    uploadedBy?: string,
  ): Promise<{
    files: FileResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    const skip = (page - 1) * limit;
    const query = uploadedBy ? { uploadedBy } : {};

    const [files, total] = await Promise.all([
      this.fileModel
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      this.fileModel.countDocuments(query),
    ]);

    return {
      files: files.map((file) => this.mapToResponseDto(file)),
      total,
      page,
      limit,
    };
  }

  private mapToResponseDto(file: FileDocument): FileResponseDto {
    return {
      id: file._id.toString(),
      filename: file.filename,
      originalName: file.originalName,
      s3Url: file.s3Url,
      expiryAt: file.expiryAt,
      isExpired: file.isExpired,
      fileSize: file.fileSize,
      mimeType: file.mimeType,
      virusScanStatus: file.virusScanStatus,
      uploadedBy: file.uploadedBy,
      createdAt: file.createdAt,
      updatedAt: file.updatedAt,
    };
  }
}
