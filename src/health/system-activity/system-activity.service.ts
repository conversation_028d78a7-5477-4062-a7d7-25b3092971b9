import { Injectable, BadRequestException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { HealthSystemAggregate } from 'src/models/health/health.system.aggregate.schema';
import { Model } from 'mongoose';
import {
  AddHealthSystemAggregateReqDTO,
  AddHealthSystemAggregateResDTO,
  HealthDumpType,
} from './dto';
import * as moment from 'moment-timezone';

@Injectable()
export class SystemActivityService {
  constructor(
    @InjectModel(HealthSystemAggregate.name)
    private readonly aggregateModel: Model<HealthSystemAggregate>,
  ) {}

  async addSystemAggregate(
    dto: AddHealthSystemAggregateReqDTO,
  ): Promise<AddHealthSystemAggregateResDTO> {
    const { userId, deviceId, os, source, timeZone, dumps } = dto;
    const dateGrouped: Record<string, any> = {};
    let insertedCount = 0;
    let updatedCount = 0;

    for (const dump of dumps) {
      for (const item of dump.data) {
        const rawTime = item.time;
        const userTime = moment.tz(rawTime, timeZone);
        const dateKey = userTime.format('YYYY-MM-DD');

        if (!dateGrouped[dateKey]) {
          dateGrouped[dateKey] = {
            userId,
            deviceId,
            os,
            source,
            timeZone,
            date: dateKey,
            heartRate: [],
          };
        }

        const entry = dateGrouped[dateKey];

        switch (dump.name) {
          case 'Distance':
            entry.distance = {
              value: item.distance,
              measurement: item.measurement,
            };
            break;
          case 'Steps':
            entry.steps = {
              totalSteps: item.totalSteps,
            };
            break;
          case 'TotalCaloriesBurned':
            entry.calories = {
              value: item.totalCalories,
              measurement: item.measurement,
            };
            break;
          case 'HeartRate':
            const localDateKey = moment.tz(item.time, timeZone).format();
            entry.heartRate.push({
              min: item.min,
              max: item.max,
              time: localDateKey,
            });
            break;
        }
      }
    }
    for (const date in dateGrouped) {
      const doc = dateGrouped[date];
      if (!doc.distance) {
        doc.distance = { value: 0, measurement: '' };
      }
      if (!doc.steps) {
        doc.steps = { totalSteps: 0 };
      }
      if (!doc.calories) {
        doc.calories = { value: 0, measurement: '' };
      }
      if (
        !doc.heartRate ||
        !Array.isArray(doc.heartRate) ||
        doc.heartRate.length === 0
      ) {
        doc.heartRate = null;
      }

      const existing = await this.aggregateModel.findOne({
        userId: doc.userId,
        deviceId: doc.deviceId,
        source: doc.source,
        date: doc.date,
      });

      if (existing) {
        await this.aggregateModel.updateOne(
          {
            userId: doc.userId,
            deviceId: doc.deviceId,
            source: doc.source,
            date: doc.date,
          },
          { $set: doc },
        );
        updatedCount++;
      } else {
        await this.aggregateModel.create(doc);
        insertedCount++;
      }
    }

    return {
      error: false,
      statusCode: HttpStatus.OK,
      success: true,
      message: 'System aggregate data processed successfully.',
      updatedCount,
      insertedCount,
    };
  }
}
