export const CLASSIFICATION_PROMPT = `
You are an assistant that classifies user questions into one of the following Appetec feature categories:

- health
- help
- faq
- general
- device
- nutrition
- activity
- mood
- weight_loss_tracking
- video_library
- sleep
- recipes
- contact_us

Rules:
- Reply with only one word: the category name.
- Do not explain or add anything else.
- If multiple categories seem close, choose the **most directly related** based on user intent.

Classification Guide:
- "health": Questions about calories burned, steps, exercise duration, activity summary, device/system/manual data, daily summaries (e.g. "today's burned data", "show my yesterday's activity", "how much I walked this week").

- "help": User is asking **how to use a specific Appetec feature** or experiencing **technical issues** (e.g. “how can I change my goal”, “how to edit reminder”, “why is my data not syncing”). 
+ Ignore generic health or wellness "how to" questions like “how to lose weight”, “how to sleep better” — those are **not help**.

- "faq": Asking a **common question about the Appetec app**, not a how-to (e.g. “is there a dark mode?”, “how does the app work?”, “is my data private?”). 
+ Do **not classify generic questions** like “what is a good diet?” or “what is sleep tracking?” as faq.

- "general": Questions about profile, goals, device management, chat history, saved chats, reminders, app integration, logout, or contact us (e.g. “edit my goals”, “update my profile”, “do you save my chats?”, “set a new reminder”, “connect with Apple health”).
- "device": Questions about device syncing, device status, timers, data not showing, or Apple/Google fit (e.g. “sync my Fitbit”, “my device data is missing”, “use timer to track walk”).
- "nutrition": Asking about meals, food tracking, vitamins, nutrients, or adding/viewing meals (e.g. “what did I eat today”, “log my lunch”, “protein suggestions”, “see food graph”).
- "activity": Questions about workouts, logging physical activity, or workout videos (e.g. “add a walk session”, “show my workout log”, “watch exercise video”).
- "mood": Asking about logging mood, mood history, therapy music or videos (e.g. “I feel anxious”, “log sad mood”, “show me a calm video”).
- "weight_loss_tracking": Tracking/logging weight, viewing progress graph (e.g. “add my current weight”, “show my weight trend”, “how much weight I lost”).
- "video_library": Asking about wellness/workout videos (e.g. “show wellness videos”, “watch stretching video”).
- "sleep": Logging or tracking sleep duration, sleep records or graphs (e.g. “log my sleep”, “how much I slept yesterday”, “sleep trends”, also if user feel tiredness).
- "recipes": Questions about recipes, ingredients, meals planning (e.g. “show me a dinner recipe”, “add a custom meal”, “healthy lunch ideas”).
- "contact_us": Questions about contacting support, reporting issues, or giving feedback (e.g. “report a bug”, “contact support”, “give feedback”).

Question: "{{question}}"
Category:
`;
export const SUPPORTED_CATEGORIES = [
  'health',
  'help',
  'faq',
  'general',
  'device',
  'nutrition',
  'activity',
  'mood',
  'weight_loss_tracking',
  'video library',
  'sleep',
  'recipes',
  'contact_us',
] as const;

export type ChatbotCategory = (typeof SUPPORTED_CATEGORIES)[number];

export const getAttachmentPrompt = (reqBody: {
  attachment?: { type?: string };
}) =>
  reqBody.attachment?.type === 'image'
    ? `The user has attached an image. Carefully analyze the image content in detail. If the image is related to any of the following categories — health, help, FAQ, general, device, nutrition, activity, mood, weight loss tracking, video library, sleep, or recipes — incorporate relevant information from it into your answer. If the attachment contains health-related information (e.g., prescriptions, lab reports), prioritize interpreting it accurately and use it to enhance your response. If the image is not relevant to the question or categories, you may ignore it.`
    : reqBody.attachment?.type === 'application'
      ? `The user has attached a document. Read the content of the document carefully. If the document contains information related to any of the following categories — health, help, FAQ, general, device, nutrition, activity, mood, weight loss tracking, video library, sleep, or recipes — extract useful details and integrate them into your answer. Prioritize understanding any medical, nutritional, or personal data that may help answer the user's query more accurately. If the content is not relevant, feel free to disregard it.`
      : '';
