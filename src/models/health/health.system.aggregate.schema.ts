import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { SourceType } from 'src/health/system-activity/dto/AddHealthSystemAggregate-req.dto';

@Schema({ timestamps: true })
export class HealthSystemAggregate extends Document {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  deviceId: string;

  @Prop({ required: true })
  os: string;

  @Prop({ required: true })
  timeZone: string;

  @Prop({ required: true, enum: SourceType })
  source: SourceType;

  @Prop({
    required: true,
    type: {
      value: Number,
      measurement: String,
    },
    _id: false,
  })
  distance?: {
    value: number;
    measurement: string;
  };

  @Prop({
    type: {
      totalSteps: Number,
    },
    _id: false,
  })
  steps?: {
    totalSteps: number;
  };

  @Prop({
    type: {
      value: Number,
      measurement: String,
    },
    _id: false,
  })
  calories?: {
    value: number;
    measurement: string;
  };

  @Prop({
    type: [
      {
        min: Number,
        max: Number,
        time: String,
      },
    ],
    _id: false,
  })
  heartRate?: {
    min: number;
    max: number;
    time: Date;
  }[];
}

export const HealthSystemAggregateSchema = SchemaFactory.createForClass(
  HealthSystemAggregate,
);

HealthSystemAggregateSchema.index({ userId: 1 });
