# Contact Us File Implementation with ClamAV and S3

## Overview

This implementation updates the contact_us system to use file IDs instead of direct URLs, with automatic URL refresh when files expire. The system maintains security through ClamAV virus scanning and uses S3 for storage.

## Key Features

1. **File ID Storage**: Contact us attachments now store only `fileId` instead of direct URLs
2. **Dynamic URL Generation**: File URLs are generated on-demand with automatic refresh
3. **ClamAV Integration**: All files are scanned for viruses before upload
4. **S3 Storage**: Files are stored securely in AWS S3
5. **Automatic URL Refresh**: Expired URLs are automatically refreshed when accessed
6. **Common File Service**: Reusable file URL service across the application

## Architecture Changes

### 1. Schema Updates

**ContactUs_Query_Attachments Schema** (`models/contact-us-query/contact-us-query-attachments.schema.ts`):
```typescript
export class ContactUs_Query_Attachments extends Document {
  @Prop({ type: String, required: true })
  fileId: string; // NEW: Store file ID instead of URL

  @Prop({ type: String, required: false })
  url?: string; // OPTIONAL: For dynamic generation

  @Prop({ type: String, enum: CONTACTUS_ATTACHMENTS_TYPES, required: true })
  type: CONTACTUS_ATTACHMENTS_TYPES;
}
```

### 2. Service Layer

**New FileUrlService** (`src/utils/files/file-url.service.ts`):
- Common service for file URL management
- Automatic URL refresh when expired
- Error handling for inaccessible files
- Batch URL retrieval support

**Updated ContactUsService** (`src/user/contact_us/contact_us.service.ts`):
- Stores only `fileId` in database
- Uses FileUrlService for URL generation
- Provides methods for retrieving queries with dynamic URLs

### 3. API Endpoints

#### User Endpoints (`/contact`)

1. **POST /contact** - Submit contact us query (unchanged behavior)
2. **GET /contact/file/:fileId/url** - Get file URL by fileId
3. **GET /contact/query/:queryId** - Get query with dynamic file URLs

#### Admin Endpoints (`/admin/contact-us-query`)

1. **GET /admin/contact-us-query/with-files** - Get all queries with file URLs
2. **GET /admin/contact-us-query/:queryId/with-files** - Get single query with file URLs

## Implementation Flow

### File Upload Process

1. **File Upload**: User uploads file via contact us form
2. **Virus Scan**: ClamAV scans file for viruses/malware
3. **S3 Upload**: Clean files are uploaded to S3
4. **Database Storage**: Only `fileId` is stored in contact_us attachments
5. **Response**: User receives confirmation with fileId

### File Retrieval Process

1. **Request**: Frontend requests file URL using fileId
2. **Expiry Check**: System checks if current URL is expired
3. **Auto Refresh**: If expired, generates new presigned URL
4. **Response**: Returns valid URL with expiry time

### Frontend Integration

#### When File URL Expires

```javascript
// Frontend should call this API when file URL expires
const refreshFileUrl = async (fileId) => {
  try {
    const response = await fetch(`/contact/file/${fileId}/url`);
    const { url, expiryAt } = await response.json();
    
    // Update UI with new URL
    updateFileUrl(fileId, url, expiryAt);
    
    return url;
  } catch (error) {
    console.error('Failed to refresh file URL:', error);
    // Handle error - show file not accessible message
  }
};

// Check expiry before using URL
const getValidFileUrl = async (fileId, currentUrl, expiryAt) => {
  const now = new Date();
  const expiry = new Date(expiryAt);
  
  if (now > expiry) {
    // URL expired, refresh it
    return await refreshFileUrl(fileId);
  }
  
  return currentUrl;
};
```

#### Retrieving Contact Us Query with Files

```javascript
// Get contact us query with dynamic file URLs
const getContactUsQuery = async (queryId) => {
  const response = await fetch(`/contact/query/${queryId}`);
  const query = await response.json();
  
  // All attachment URLs are automatically refreshed if expired
  query.attachments.forEach(attachment => {
    if (attachment.url) {
      // Use the URL - it's guaranteed to be valid
      displayFile(attachment.url, attachment.type);
    } else if (attachment.error) {
      // Handle file not accessible
      showError(attachment.error);
    }
  });
};
```

## Error Handling

### File Not Found
- Returns `{ error: "File not accessible" }` in attachment object
- Frontend should display appropriate message

### Expired URLs
- Automatically refreshed when accessed
- No manual intervention required

### Virus Detection
- Files rejected during upload
- Error message returned to user

## Security Benefits

1. **No Direct URL Storage**: URLs are generated on-demand
2. **Virus Scanning**: All files scanned before storage
3. **Presigned URLs**: Time-limited access to S3 objects
4. **Access Control**: File access through application logic
5. **Audit Trail**: File access can be logged and monitored

## Migration Notes

### Existing Data
- Existing contact_us records with direct URLs will continue to work
- New uploads will use the fileId approach
- Gradual migration can be implemented if needed

### Database Changes
- Added `fileId` field to ContactUs_Query_Attachments
- Made `url` field optional
- No breaking changes to existing data

## Testing

### Unit Tests
- Test file upload with virus scanning
- Test URL generation and refresh
- Test error handling for missing files

### Integration Tests
- Test complete contact us flow with file attachments
- Test admin endpoints with file URL generation
- Test URL expiry and refresh functionality

## Performance Considerations

1. **Caching**: File URLs can be cached until expiry
2. **Batch Operations**: Multiple file URLs can be retrieved in single call
3. **Lazy Loading**: URLs generated only when needed
4. **S3 Optimization**: Presigned URLs reduce server load

## Monitoring

### Metrics to Track
- File upload success/failure rates
- Virus scan results
- URL refresh frequency
- File access patterns
- Error rates for file retrieval

### Alerts
- High virus detection rates
- S3 upload failures
- ClamAV service unavailability
- Unusual file access patterns
