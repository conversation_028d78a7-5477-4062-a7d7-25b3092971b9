import { Linking, PermissionsAndroid, Platform } from 'react-native';

// HEALTH_METRICS stays the same
const HEALTH_METRICS = {
    CALORIES_BURNED: 'ActiveCaloriesBurned',
    BASEL_BODY_TEMP: 'BasalBodyTemperature',
    BASEL_METABOLIC_RATE: 'BasalMetabolicRate',
    BLOOD_GLUCOSE: 'BloodGlucose',
    BLOOD_PRESSURE: "BloodPressure",
    BODY_FAT: 'BodyFat',
    BODY_TEMP: 'BodyTemperature',
    BONE_MASS: 'BoneMass',
    CERVICAL_MUCUS: 'CervicalMucus',
    CYCLING_PEDALING_CADENCE: 'CyclingPedalingCadence',
    DISTANCE: 'Distance',
    ELEVATION_GAINED: 'ElevationGained',
    EXERCISE_SESSION: 'ExerciseSession',
    FLOORS: 'FloorsClimbed',
    HEART_RATE: "HeartRate",
    HEIGHT: 'Height',
    HYDRATION: 'Hydration',
    LEAN_BODY_MASS: 'LeanBodyMass',
    MENSTRUATION_FLOW: 'MenstruationFlow',
    MENSTRUATION_PERIOD: 'MenstruationPeriod',
    NUTRITION: 'Nutrition',
    OVULATION_TEST: 'OvulationTest',
    OXYGEN_SATURATION: 'OxygenSaturation',
    POWER: 'Power',
    RESPIRATORY_RATE: 'RespiratoryRate',
    RESTING_HEART_RATE: 'RestingHeartRate',
    SEXUAL_ACTIVITY: 'SexualActivity',
    SLEEP_SESSION: 'SleepSession',
    SPEED: "Speed",
    STEPS: 'Steps',
    STEPS_CANDENCE: 'StepsCadence',
    TOTAL_CALORIES_BURNED: 'TotalCaloriesBurned',
    VO2MAX: 'Vo2Max',
    WEIGHT: 'Weight',
    WHEEL_CHAIR_PUSHES: 'WheelchairPushes'
};

// Guarded HealthConnect functions

export const hasGoogleHealthInstalled = async () => {
    if (Platform.OS !== 'android') return false;
    try {
        const { initialize } = await import('react-native-health-connect');
        const isInitialized = await initialize();
        return isInitialized;
    } catch (error) {
        return false;
    }
};

export const requestGoogleHealthPermissions = async () => {
    if (Platform.OS !== 'android') return false;
    try {
        const { requestPermission } = await import('react-native-health-connect');
        const permissionsToRequest = Object.values(HEALTH_METRICS).map(metric => ({
            accessType: 'read',
            recordType: metric
        }));
        await requestPermission([...permissionsToRequest]);
        const status = await PermissionsAndroid.request('android.permission.health.READ_HEALTH_DATA_IN_BACKGROUND');
        return status === 'granted';
    } catch (error) {
        return false;
    }
};

export const getGrantedPermissionsList = async () => {
    if (Platform.OS !== 'android') {
        return {
            permissionsGranted: [],
            areAllPermissionsGranted: false
        };
    }
    try {
        const { getGrantedPermissions } = await import('react-native-health-connect');
        const grantedPermissions = await getGrantedPermissions();
        return {
            permissionsGranted: grantedPermissions,
            areAllPermissionsGranted:
                grantedPermissions.filter(recordType => recordType.accessType === "read").length === Object.keys(HEALTH_METRICS).length
        };
    } catch (error) {
        return {
            permissionsGranted: [],
            areAllPermissionsGranted: false
        };
    }
};

export const removeGoogleHealthPermission = async () => {
    if (Platform.OS !== 'android') return false;
    try {
        const { revokeAllPermissions } = await import('react-native-health-connect');
        await revokeAllPermissions();
        return true;
    } catch (error) {
        return false;
    }
};

export const handleManagePermissions = async () => {
    if (Platform.OS === "android") {
        try {
            const { openHealthConnectSettings } = await import('react-native-health-connect');
            await openHealthConnectSettings();
        } catch (error) {
            await Linking.openURL('https://play.google.com/store/apps/details?id=com.google.android.apps.healthdata');
        }
    }
    // iOS: do nothing
};
