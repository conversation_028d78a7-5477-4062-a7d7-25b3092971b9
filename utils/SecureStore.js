import * as SecureStore from 'expo-secure-store';


export const saveToken = async (key, value) => {
    await SecureStore.setItemAsync(key, value);
};

export const retriveStorageToken = async (key) => {
    let result = await SecureStore.getItemAsync(key);
    if (result) {
        alert("🔐 Here's your value 🔐 \n" + result);
        return result;
    }
};

export const removeToken = async (key) => {
    await SecureStore.deleteItemAsync(key);
};

export const saveRefreshToken = async (token) => {
    await SecureStore.setItemAsync('refreshToken', token);
};

// Get Refresh Token
export const getRefreshToken = async () => {
    return await SecureStore.getItemAsync('refreshToken');
};

// Delete Refresh Token (On Logout)
export const deleteRefreshToken = async () => {
    await SecureStore.deleteItemAsync('refreshToken');
};
