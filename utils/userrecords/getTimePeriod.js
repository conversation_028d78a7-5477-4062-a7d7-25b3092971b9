import getMonthName from "utils/dateandtimeformatters/getMonthName";

export const getTimePeriod = (start,end) => {
    const startDate = new Date(start);
    const endDate =  new Date(end);

    const  timePeriod = startDate.getFullYear() === endDate.getFullYear() && startDate.getMonth() === endDate.getMonth()
        ? `${startDate.getDate()} - ${endDate.getDate()} ${getMonthName(endDate).substring(0,3)} ${endDate.getFullYear()}`
        : (
            startDate.getFullYear() === endDate.getFullYear()
            ? `${startDate.getDate()} ${getMonthName(startDate).substring(0,3)} - ${endDate.getDate()} ${getMonthName(endDate).substring(0,3)} ${endDate.getFullYear()}`
            : `${startDate.getDate()} ${getMonthName(startDate).substring(0,3)} ${startDate.getFullYear()} - ${endDate.getDate()} ${getMonthName(endDate).substring(0,3)} ${endDate.getFullYear()}`
        );

    return timePeriod;
}