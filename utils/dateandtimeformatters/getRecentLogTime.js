const getFormattedDate = (date) => {
    const newDate = new Date(date);
    const monthNames = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
    ];
    return `At ${String(newDate.getDate()).padStart(2, "0")}-${monthNames[parseInt(newDate.getMonth())]
        }-${newDate.getFullYear()}`;
};



const getRecentLogTime = (timestamp, time, prefix = "Today at") => {
    const date = new Date(timestamp);
    const today = new Date();

    if (today.toDateString() !== new Date(time).toDateString()) {
        return getFormattedDate(new Date(timestamp));
    } else
        return `${prefix} ${date
            .toLocaleTimeString("en-US", {
                hour: "2-digit",
                minute: "2-digit",
                hour12: true,
            })
            .replace("am", "AM")
            .replace("pm", "PM")}`;
};

export default getRecentLogTime;