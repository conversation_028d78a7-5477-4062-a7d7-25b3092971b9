import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import NavigationArrowButton from './NavigationArrowButton';
import FormattedTime from 'components/CustomText/FormattedTime';
import useDeviceTimerStore from 'store/deviceTimerStore';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';

/**
 * A simplified dashboard section component with just header and last log
 * @param {Object} props - Component props
 * @param {string} props.title - The section title
 * @param {Function} props.onNavigate - Function to call when navigation button is pressed
 * @param {string} props.backgroundColor - Background color for the section card
 * @param {Object} props.style - Additional styles for the section container
 * @param {string} props.totalUsageTime - Total device usage time to display
 */
const SimpleDashboardSection = ({
  title,
  onNavigate,
  backgroundColor = Colors.darkGreen,
  style,
  totalUsageTime
}) => {
  const { isLoadingTimerHistory, lastDeviceLogTime } = useDeviceTimerStore(state => state);

  return (
    <View style={[styles.container, { backgroundColor }, style]}>
      {/* Section Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>{title}</Text>
          <SkeletonItem width="90%" height={14} borderRadius={4} isLoading={isLoadingTimerHistory} style={{ marginTop: 4 }}>
            <Text style={styles.lastLogTime}>
              <FormattedTime timestamp={lastDeviceLogTime} prefix='Last Log: ' defaultPrefix='Last Log: Today at' />
            </Text>
          </SkeletonItem>
          {/* {totalUsageTime && (
            <Text style={styles.usageTime}>
              Total Usage: {totalUsageTime}
            </Text>
          )} */}
        </View>
        <NavigationArrowButton onPress={onNavigate} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: Colors.primaryGreen,
    borderRadius: 25,
    borderColor: Colors.lightGreen,
    borderWidth: 2,
    marginTop: 8
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 29,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
    // lineHeight: 36
  },
  card: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 25,
  },
  lastLogLabel: {
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.white,
  },
  lastLogTime: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.white,
    // marginVertical: 4,
  },
  usageTime: {
    fontSize: 20,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
    marginTop: 16,
  }
});

export default SimpleDashboardSection;
