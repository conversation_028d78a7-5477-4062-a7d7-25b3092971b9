import React, { useCallback, useRef } from 'react';
import { TouchableOpacity, StyleSheet, Image } from 'react-native';
import { Colors } from 'constants/theme/colors';
import { memo } from 'react';

/**
 * A reusable navigation arrow button component with a long arrow
 * @param {Object} props - Component props
 * @param {Function} props.onPress - Function to call when button is pressed
 * @param {Object} props.style - Additional styles for the button container
 * @param {Object} props.hitSlop - Hit slop for the button (default: 10px on all sides)
 */
const NavigationArrowButton = ({
  onPress,
  style,
  hitSlop = { top: 10, bottom: 10, left: 10, right: 10 },
  throttleDelay = 500
}) => {
  const lastTapTime = useRef(0);

  const handlePress = useCallback(() => {
    const now = Date.now();

    if (now - lastTapTime.current >= throttleDelay) {
      lastTapTime.current = now;
      onPress();
    }
  }, [onPress, throttleDelay]);

  return (
    <TouchableOpacity
      style={[styles.arrowButton, style]}
      onPress={handlePress}
      hitSlop={hitSlop}
    >
      <Image
        source={require('assets/icons/arrow.png')}
        style={styles.arrowIcon}
        resizeMode="cover"
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  arrowButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primaryPurple,
    width: 60,
    borderRadius: 25,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  arrowIcon: {
    width: 150,
    height: 20,
    marginTop: 5,
    // tintColor: Colors.white, // Make the arrow white
  }
});

export default memo(NavigationArrowButton);
