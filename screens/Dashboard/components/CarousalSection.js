import React, { memo, useRef, useState } from 'react';
import { StyleSheet, Text, View, Dimensions, TouchableOpacity, Image } from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';

const { width } = Dimensions.get('window');

const CarousalSection = ({ data, loading = false, error = null, refreshCarousel = () => { } }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const carouselRef = useRef(null);

    const handlePrev = () => {
        carouselRef.current?.prev();
    };

    const handleNext = () => {
        carouselRef.current?.next();
    };

    return (
        <View style={styles.container}>
            <Carousel
                ref={carouselRef}
                width={width - 24}
                height={260}
                data={(loading || error) ? [{}] : data}
                autoPlay={
                    !loading && !error
                }
                enabled={!(loading || error)}
                // mode='parallax'
                // modeConfig={{
                //     parallaxScrollingScale: 0.9,
                //     parallaxScrollingOffset: 50,
                // }}
                autoPlayInterval={3000}
                scrollAnimationDuration={800}
                // onSnapToItem={setCurrentIndex}
                onProgressChange={(progress, absoluteProgress) => {
                    if (!loading && !error) {
                        setCurrentIndex(Math.round(absoluteProgress) % data.length);
                    }
                    // setCurrentIndex(Math.abs(Math.round(progress / (width - 24))) % 4);
                }}
                renderItem={({ item }) => (
                    loading ? (
                        <View style={{ flex: 1, paddingHorizontal: 4 }}>
                            <View style={[styles.card, {
                                flex: 1,
                                flexDirection: "column",
                            }]}>
                                <SkeletonItem
                                    width="30%"
                                    height={28}
                                    borderRadius={16}
                                    style={{ marginBottom: 12, marginTop: 4 }}
                                    isLoading={true}
                                />
                                <SkeletonItem
                                    width="60%" height={28} borderRadius={30}
                                    style={{ marginBottom: 16, marginTop: 8 }}
                                />
                                <SkeletonItem
                                    width="80%" height={18} borderRadius={30}
                                    style={{ marginBottom: 12, marginTop: 4 }}
                                />
                                <SkeletonItem
                                    width="100%" height={14} borderRadius={30}
                                    style={{ marginBottom: 8, marginTop: 8 }}
                                />
                                <SkeletonItem
                                    width="40%" height={14} borderRadius={30}
                                />
                            </View>
                        </View>
                    ) : error ? (
                        <View style={{ flex: 1, paddingHorizontal: 4 }}>
                            <View style={[styles.card, { justifyContent: "center", alignItems: "center" }]}>
                                <Text style={styles.errorext}>{error}</Text>
                                <TouchableOpacity onPress={refreshCarousel} activeOpacity={.85} style={styles.arrowButton}>
                                    <MaterialIcons name="refresh" size={24} color={Colors.white} />
                                </TouchableOpacity>
                            </View> x
                        </View>
                    ) : (
                        <View style={{ flex: 1, paddingHorizontal: 4 }}>
                            <View style={styles.card}>
                                {item.image && (
                                    <Image
                                        source={{ uri: item.image }}
                                        style={styles.backgroundImage}
                                        resizeMode="cover"
                                    />
                                )}
                                <View style={styles.tagContainer}>
                                    <Text style={styles.tagText} numberOfLines={1} allowFontScaling={false}>{String(item?.tag).substring(0, 1).toUpperCase() + String(item?.tag).substring(1)}</Text>

                                </View>
                                <Text style={styles.greeting} numberOfLines={1} allowFontScaling={false}>{item?.greetings}</Text>
                                <Text style={styles.subheading} numberOfLines={1} allowFontScaling={false}>{item.subheading}</Text>
                                <Text style={styles.content} textBreakStrategy='simple' numberOfLines={3} allowFontScaling={false}>{item.content}</Text>
                            </View>
                        </View>
                    )
                )}
                style={{ alignSelf: 'center' }}
            />
            {
                (!error || (error && loading)) && (
                    <View style={styles.navContainer}>
                        <TouchableOpacity onPress={handlePrev} disabled={loading} activeOpacity={.85} style={styles.arrowButton}>
                            <Text style={[styles.arrow]}>{'◀'}</Text>
                        </TouchableOpacity>
                        <Text style={styles.pagination}>{`${currentIndex + 1}/${(loading || error) ? 1 : data.length}`}</Text>
                        <TouchableOpacity onPress={handleNext} disabled={loading} activeOpacity={.85} style={styles.arrowButton}>
                            <Text style={[styles.arrow]}>{'▶'}</Text>
                        </TouchableOpacity>
                    </View>
                )
            }
        </View>
    );
};

export default memo(CarousalSection);

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        height: 260,
    },
    card: {
        flex: 1,
        backgroundColor: Colors.lightPurple,
        borderRadius: 30,
        borderWidth: 2,
        borderColor: Colors.lightGreen,
        padding: 16,
        paddingVertical: 24,
        justifyContent: 'flex-start',
    },
    tagContainer: {
        alignSelf: 'flex-start',
        borderWidth: 2,
        borderColor: Colors.lightGreen,
        borderRadius: 16,
        paddingVertical: 4,
        paddingHorizontal: 16,
        marginBottom: 4,
        backgroundColor: 'transparent',
    },
    tagText: {
        color: Colors.white,
        fontSize: 12,
        fontFamily: ThemeFonts.Exo_400,
        letterSpacing: 0.1,
        textTransform: 'capitalize'
    },
    greeting: {
        fontSize: 28,
        color: Colors.white,
        marginVertical: 12,
        fontFamily: ThemeFonts.Exo_700,
        letterSpacing: 0.5,
    },
    subheading: {
        fontSize: 16,
        color: Colors.white,
        marginBottom: 8,
        fontFamily: ThemeFonts.Exo_400,
    },
    content: {
        fontSize: 12,
        color: Colors.white,
        marginTop: 2,
        lineHeight: 20,
        fontFamily: ThemeFonts.Exo_400,
    },
    navContainer: {
        width: "100%",
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 28,
        paddingBottom: 16,
        top: 8
    },
    arrowButton: {
        padding: 10,
    },
    arrow: {
        fontSize: 16,
        color: Colors.white,
        fontWeight: 'bold',
    },
    pagination: {
        color: Colors.white,
        fontSize: 12,
        marginHorizontal: 16,
        fontWeight: ThemeFonts.Exo_400,
    },
    backgroundImage: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        borderRadius: 30,
        opacity: 0.3,
    },
    loadingText: {
        color: Colors.white,
        fontSize: 18,
        fontFamily: ThemeFonts.Exo_500,
    },
    errorext: {
        color: Colors.white,
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_500,
    },
});
