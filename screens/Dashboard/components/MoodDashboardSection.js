import React, { memo } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import NavigationArrowButton from './NavigationArrowButton';
import FormattedTime from 'components/CustomText/FormattedTime';
import { MOOD_OPTIONS } from 'constants/constants';
import useMoodStore from 'store/moodStore';
import { SkeletonItem, SkeletonCircle } from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';

// Default quote when no mood is selected
const DEFAULT_QUOTE = "Be aware of your emotional state";

/**
 * A simplified dashboard section component with just header and last log
 * @param {Object} props - Component props
 * @param {string} props.title - The section title
 * @param {Function} props.onNavigate - Function to call when navigation button is pressed
 * @param {Object} props.style - Additional styles for the section container
 */
const MoodDashboardSection = memo(({
    title,
    onNavigate,
    style,
}) => {
    const { isLoadingLastLoggedMood, lastLoggedMood } = useMoodStore(state => state);
    const moodType = lastLoggedMood?.moodType;

    // Find the active mood object to get its quote
    const activeMood = MOOD_OPTIONS.find(mood => mood.value === moodType);
    const currentQuote = activeMood ? activeMood.quote : DEFAULT_QUOTE;

    // Extracted as a separate component for better readability
    const renderMoodItem = (mood, index) => {
        const isActive = moodType === mood.value;

        if (isLoadingLastLoggedMood) {
            return (
                <View key={index} style={[styles.moodItem]}>
                    <SkeletonItem width={40} height={40} style={{ marginTop: 0 }} isLoading={true} borderRadius={100} />
                    <SkeletonItem width={30} height={10} style={{ marginTop: 6, marginBottom: 4 }} isLoading={true} />
                </View>
            );
        }

        return (
            <View key={index} style={styles.moodItem}>
                <Image
                    source={isActive ? mood.filledIcon : mood.borderIcon}
                    style={[
                        styles.moodIcon,
                        isActive && styles.activeMoodIcon,
                        isActive && { backgroundColor: Colors.primaryGreen }
                    ]}
                    resizeMode="contain"
                />
                {isActive ? (
                    <Text style={styles.activeMoodText}>{mood.label}</Text>
                ) : (
                    <Text style={styles.moodText}></Text>
                )}
            </View>
        );
    };

    return (
        <View style={[styles.container, style]}>
            {/* Section Header */}
            <View style={styles.header}>
                <View>
                    <Text style={styles.title}>{title}</Text>
                    <SkeletonItem width={120} height={12} isLoading={isLoadingLastLoggedMood} style={{ marginTop: 4 }}>
                        <Text style={styles.lastLogTime}>
                            <FormattedTime timestamp={lastLoggedMood?.updatedAt} prefix='Last Log: ' defaultPrefix='Last Log: Today at' />
                        </Text>
                    </SkeletonItem>
                </View>
                <NavigationArrowButton onPress={onNavigate} />
            </View>
            <View style={styles.contentContainer}>
                <View style={styles.card}>
                    {MOOD_OPTIONS.map(renderMoodItem)}
                </View>

                <View style={styles.emptyStateContainer}>
                    <SkeletonItem width="90%" height={14} isLoading={isLoadingLastLoggedMood}>
                        <Text style={styles.emptyStateTitle}>{currentQuote}</Text>
                    </SkeletonItem>
                    <SkeletonItem width="60%" height={12} style={{ marginTop: 16 }} isLoading={isLoadingLastLoggedMood} >
                        <Text style={styles.emptyStateSubtitle}>{!activeMood ? "Log your mood now" : "Track your mood regularly"}</Text>
                    </SkeletonItem>
                </View>
            </View>
        </View>
    );
});

const styles = StyleSheet.create({
    container: {
        marginVertical: 20,
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: Colors.lightGreen,
        borderRadius: 25,
        borderColor: Colors.primaryPurple,
        borderWidth: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        // marginBottom: 4,
    },
    title: {
        fontSize: 29,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.primaryPurple,
    },
    card: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        // padding: 14,
        paddingVertical: 12,
        // paddingHorizontal: 8,
        borderRadius: 25,
    },
    lastLogTime: {
        fontSize: 12,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.black,
    },
    moodItem: {
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "stretch",
        paddingVertical: 4
    },
    moodIcon: {
        width: 38,
        height: 38,
    },
    activeMoodIcon: {
        width: 44,
        height: 44,
        borderRadius: 100,
    },
    moodText: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_600,
        color: Colors.black,
    },
    activeMoodText: {
        fontSize: 10,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.primaryGreen,
        marginTop: 4,
        marginHorizontal: 2,
    },
    contentContainer: {
        flexDirection: 'column',
    },
    emptyStateContainer: {
        alignItems: 'flex-start',
        // marginTop: 16,
        paddingHorizontal: 4,
    },
    emptyStateTitle: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.black,
        textAlign: 'left',
        marginBottom: 8,
        // paddingHorizontal: 10,
        // lineHeight: 20,
    },
    emptyStateSubtitle: {
        fontSize: 12,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.black,
        textAlign: 'left',
    }
});

export default MoodDashboardSection;
