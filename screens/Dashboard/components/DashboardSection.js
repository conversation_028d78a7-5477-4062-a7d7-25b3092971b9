import React from 'react';
import { View, StyleSheet } from 'react-native';
import SectionHeader from './SectionHeader';
import SectionMetricCard from './SectionMetricCard';
import NutritionStackedBarGraph from 'components/Charts/BarGraphs/NutritionStackedBarGraph';
import useNutritionMealRecordStore from 'store/nutritionMealRecordStore';

/**
 * A complete dashboard section component with header and metrics card
 * @param {Object} props - Component props
 * @param {string} props.title - The section title
 * @param {Function} props.onNavigate - Function to call when navigation button is pressed
 * @param {Array} props.metrics - Array of metric objects with value, label, and optional suffix
 * @param {boolean} props.showTime - Whether to show the formatted time (default: true)
 * @param {Object} props.style - Additional styles for the section container
 * @param {Object} props.headerStyle - Additional styles for the header
 * @param {Object} props.cardStyle - Additional styles for the metrics card
 */
const DashboardSection = ({
  title,
  onNavigate,
  metrics,
  showTime = true,
  style,
  headerStyle,
  cardStyle,
  timestamp,
  isLoading,
  itemCount = 3
}) => {

  return (
    <View style={[styles.container, style]}>
      <SectionHeader
        title={title}
        onNavigate={onNavigate}
        timestamp={timestamp}
        showTime={showTime}
        style={headerStyle}
        isLoading={isLoading}
      />
      <SectionMetricCard
        metrics={metrics}
        style={cardStyle}
        isLoading={isLoading}
        itemCount={itemCount}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
    // backgroundColor: Colors.lightGreen
  },
});

export default DashboardSection;
