import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import NavigationArrowButton from './NavigationArrowButton';
import FormattedTime from 'components/CustomText/FormattedTime';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';

/**
 * @param {Object} props - Component props
 * @param {string} props.title - The section title
 * @param {Function} props.onNavigate - Function to call when navigation button is pressed
 * @param {boolean} props.showTime - Whether to show the formatted time (default: true)
 * @param {Object} props.style - Additional styles for the header container
*/

const SectionHeader = memo(({
  title,
  onNavigate,
  timestamp,
  showTime = true,
  style,
  isLoading = false
}) => {
  return (
    <View style={[styles.header, style]}>
      <View>
        <Text style={styles.title}>{title}</Text>
        <SkeletonItem width="80%" height={14} borderRadius={4} isLoading={isLoading} style={{ marginTop: 4 }}>
          {(showTime) && (
            <Text style={styles.timeText}>
              <FormattedTime timestamp={timestamp} prefix='Last Log: ' defaultPrefix='Last Log: Today at' />
            </Text>
          )}
        </SkeletonItem>
      </View>
      <NavigationArrowButton onPress={onNavigate} />
    </View>
  );
});

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 35,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.black,
  },
  timeText: {
    fontSize: 12,
    color: Colors.black,
    textAlign: 'start',
    fontFamily: ThemeFonts.Exo_500,
    marginRight: 8,
  },
});

export default SectionHeader;
