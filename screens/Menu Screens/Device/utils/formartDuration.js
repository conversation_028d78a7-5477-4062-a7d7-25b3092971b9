const formatDuration = (value) => {
    if (value === 0) return "0 sec";

    const hours = Math.floor(value / 3600);
    const minutes = Math.floor((value % 3600) / 60);
    const seconds = value % 60;

    if (hours > 0) {
        return `${hours} hr${minutes > 0 ? ` ${minutes} min` : ''}`;
    } else if (minutes > 0) {
        return `${minutes} min${seconds > 0 ? ` ${seconds} sec` : ''}`;
    } else {
        return `${seconds} sec`;
    }
}

export default formatDuration;