import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import Animated from 'react-native-reanimated';
import useDeviceTimerStore from 'store/deviceTimerStore';
import { TIMER_DURATION_OPTIONS } from 'constants/constants';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const TimerDisplay = React.memo(({
    size,
    outerRadius,
    outerStrokeWidth,
    innerRadius,
    innerStrokeWidth,
    innerCircumference,
    animatedProps,
    formatTime,
    timerDuration,
    isTimerRunning,
    setCurrentOpenDropdown,
    currentOpenDropdown,
    setTimerDuration,
    resetTimer,
    styles,
    Colors,
    CustomSelect
}) => {
    // Only subscribe to remainingTime
    const remainingTime = useDeviceTimerStore(state => state.remainingTime);

    return (
        <View style={styles.timerContainer}>
            {/* SVG Circle Timer */}
            <Svg width={size} height={size} style={styles.timerSvg}>
                {/* Outer Circle (static) */}
                <Circle
                    cx={size / 2}
                    cy={size / 2}
                    r={outerRadius}
                    stroke={Colors.lightGreen}
                    strokeWidth={outerStrokeWidth}
                    fill="transparent"
                />
                {/* Inner Circle Progress (animated) */}
                <AnimatedCircle
                    cx={size / 2}
                    cy={size / 2}
                    r={innerRadius}
                    stroke={Colors.primaryGreen}
                    strokeWidth={innerStrokeWidth}
                    fill="transparent"
                    strokeDasharray={innerCircumference}
                    strokeLinecap="round"
                    animatedProps={animatedProps}
                />
            </Svg>
            {/* Timer Display */}
            <View style={styles.timerTextContainer}>
                <View style={styles.timerTextWrapper}>
                    <Text style={styles.timerText}>{formatTime(remainingTime)}</Text>
                </View>
                <TouchableOpacity
                    style={[styles.editButton, isTimerRunning && styles.disabledEditButton]}
                    onPress={() => !isTimerRunning && setCurrentOpenDropdown('timer')}
                    disabled={isTimerRunning}
                >
                    <Text style={[styles.editButtonText, isTimerRunning && styles.disabledEditButtonText]}>Edit</Text>
                </TouchableOpacity>
                {/* Timer Duration Dropdown - only show when timer is not running */}
                {currentOpenDropdown === 'timer' && !isTimerRunning && (
                    <View style={styles.timerDropdown}>
                        <CustomSelect
                            options={TIMER_DURATION_OPTIONS}
                            selectedValue={timerDuration}
                            onValueChange={async (value) => {
                                setTimerDuration(value);
                                resetTimer();
                                setCurrentOpenDropdown(null);
                            }}
                            width={120}
                            triggerStyle={{ paddingVertical: 3, paddingHorizontal: 10 }}
                            disabled={isTimerRunning}
                            backgroundColor={Colors.primaryPurple}
                            textColor={Colors.white}
                            activeOptionBgColor={Colors.lightPurple}
                            optionsBgColor={Colors.white}
                            optionsBorderWidth={2}
                            optionsBorderRadius={20}
                            optionsBorderColor={Colors.primaryPurple}
                            triggerBorderWidth={2}
                            currentOpenDropdown={currentOpenDropdown}
                            dropdownId={'timer'}
                            setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                            alignDropdown='flex-start'
                            changeBG={true}
                        />
                    </View>
                )}
            </View>
        </View>
    );
});

export default TimerDisplay; 