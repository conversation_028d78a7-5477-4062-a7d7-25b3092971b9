import { FlatList, Image, InteractionManager, ScrollView, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import AppLayout from "navigations/components/Layouts/AppLayout";
import { recipesService } from 'services/recipesService';
import { ThemeFonts } from 'constants/theme/fonts';
import { Colors } from 'constants/theme/colors';
import { useNavigation, useRoute } from '@react-navigation/native';
import { CustomButton, CustomLoader } from 'components/CustomAction';
import CustomSelectWithLabel from 'components/CustomAction/CustomSelectWithLabel';
import NutrientCard from '../components/NutrientCard';
import useNutritionMealStore from 'store/nutritionMealsStore';
import CustomImageWithTextInput from '../components/CustomImageWithTextInput';
import { useAuth } from 'context/AuthContext';

const Measurement_Options = [
    "grams", "milligrams", "oz"
];

const AddMealScreen = () => {
    const route = useRoute();
    const navigator = useNavigation();
    const scrollViewRef = useRef();
    const { state: { user } } = useAuth();

    const meals = useNutritionMealStore(state => state.meals);

    const addMealData = useNutritionMealStore(state => state.addMealData);

    const [loading, setLoading] = useState(true);
    const [recipe, setRecipe] = useState();
    const [error, setError] = useState();

    const [selectedQuantity, setSelectedQuantity] = useState(null);
    const [selectedMeasurement, setSelectedMeasurement] = useState(null);

    const [closeDropdowns, setCloseDropdowns] = useState(false);
    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
    const [hasScreenLoaded, setHasScreenLoaded] = useState(false);

    const totalCalories = selectedQuantity ? recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.calories : 0;

    useEffect(() => {
        setLoading(true);
        InteractionManager.runAfterInteractions(() => {
            (async () => {
                const res = await recipesService.getSingleRecipe({ id: route?.params?.id });
                if (res.success) {
                    setRecipe(res.data);
                }
                else setError(res.error);
                setLoading(false);
            })();
            setHasScreenLoaded(true);
        });
    }, [route.params.id]);


    const scrollToOffset = (y) => {
        scrollViewRef.current?.scrollTo({ y: y, animated: true });
    };

    if (!hasScreenLoaded) {
        return (
            <AppLayout>
                <View style={styles.loaderContainer}>
                    <CustomLoader />
                </View>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false} ref={scrollViewRef}>
                <TouchableWithoutFeedback onPress={() => {
                    setCloseDropdowns(prev => !prev);
                    setCurrentOpenDropdown(null);
                }}>
                    <View style={{ flex: 1, flexDirection: "column", marginBottom: 80 }} >
                        <View style={styles.header}>
                            <Text style={styles.headerTitle}>{route.params.meal_name}</Text>
                            <Text style={styles.totalCaloriesText}>{`${meals.reduce((total, meal) => total + meal.calories, 0) + totalCalories} kcal`}</Text>
                        </View>
                        <View style={{ marginHorizontal: 0, marginTop: 16 }}>
                            <CustomImageWithTextInput
                                isLoading={loading}
                                mealName={recipe?.title}
                                subTile={
                                    recipe?.author === user.id ? "Custom meal" : "Recommended"
                                }
                                editable={false}
                                calories={
                                    selectedQuantity ? recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.calories : 0
                                }
                                mealImage={recipe?.thumbnailUrl}
                            />
                        </View>
                        <View style={{ marginVertical: 32, gap: 32, marginHorizontal: 0 }}>
                            <CustomSelectWithLabel
                                label="Add Measurement"
                                separateLabel="What is the unit?"
                                options={Measurement_Options.map((value) => {
                                    return {
                                        label: value,
                                        value: value
                                    };
                                })}
                                selectedValue={selectedMeasurement}
                                onValueChange={(value) => setSelectedMeasurement(value)}
                                labelStyle={{
                                    backgroundColor: Colors.veryDarkGreen,
                                    color: Colors.white,
                                    paddingVertical: 20
                                }}
                                triggerZ={10}
                                listZ={9}
                                scrollToOffset={(value) => scrollToOffset(value - 200)}
                                outsideClick={closeDropdowns}
                                currentOpenDropdown={currentOpenDropdown}
                                dropdownId={1}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                isLoading={loading}
                            />
                            <CustomSelectWithLabel
                                label="Add Quantity"
                                separateLabel="What is the quantity?"
                                options={recipe?.nutritionByQuantity.map((quantity) => {
                                    return {
                                        label: quantity.quantity,
                                        value: quantity.quantity
                                    };
                                }) || []}
                                selectedValue={selectedQuantity}
                                onValueChange={(value) => setSelectedQuantity(value)}
                                labelStyle={{
                                    backgroundColor: Colors.veryDarkGreen,
                                    color: Colors.white,
                                    paddingVertical: 20
                                }}
                                triggerZ={8}
                                listZ={7}
                                scrollToOffset={(value) => scrollToOffset(value - 200)}
                                outsideClick={closeDropdowns}
                                currentOpenDropdown={currentOpenDropdown}
                                dropdownId={2}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                isLoading={loading}
                            />
                        </View>
                        {
                            (selectedQuantity && selectedMeasurement) && (
                                <View style={{ gap: 35, marginHorizontal: 0, marginTop: 24 }}>
                                    <View style={styles.nutritionCardContainer}>
                                        <NutrientCard label="Protein" value={recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.protein} measurement={selectedMeasurement} loading={loading} />
                                        <NutrientCard label="Fats" value={recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.fats} measurement={selectedMeasurement} loading={loading} />
                                    </View>
                                    <View style={styles.nutritionCardContainer}>
                                        <NutrientCard label="Carbs" value={recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.carbs} measurement={selectedMeasurement} loading={loading} />
                                        <NutrientCard label="Fiber" value={recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.fiber} measurement={selectedMeasurement} loading={loading} />
                                    </View>
                                </View>
                            )
                        }

                        <View style={{ marginTop: 168, alignItems: "flex-end", marginHorizontal: 0 }}>
                            <CustomButton title={"Save"} onPress={() => {
                                addMealData({
                                    recipeId: route.params.id,
                                    measurement: selectedMeasurement,
                                    quantity: selectedQuantity,
                                    name: recipe.title,
                                    image: recipe.thumbnailUrl,
                                    calories: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.calories,
                                    protein: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.protein,
                                    fiber: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.fiber,
                                    fats: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.fats,
                                    carbs: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.carbs,
                                    author: recipe?.author
                                });
                                navigator.goBack();
                            }}
                                disabled={!(selectedMeasurement && selectedQuantity)}
                            />
                        </View>
                    </View>
                </TouchableWithoutFeedback>
            </ScrollView>
        </AppLayout>
    );
};

export default AddMealScreen;

const styles = StyleSheet.create({
    header: {
        marginHorizontal: 24,
    },
    headerTitle: {
        fontSize: 35,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    totalCaloriesText: {
        fontSize: 25,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_600
    },
    recipeImage: {
        height: 225,
        borderRadius: 25
    },
    recipeImgDataContainer: {
        position: "absolute",
        right: 12,
        left: 12,
        bottom: 12,
        backgroundColor: Colors.veryLightGreen,
        padding: 16,
        // paddingVertical: 12,
        borderRadius: 25,
        flexDirection: "row",
        alignItems: "flex-end"
    },
    loaderContainer: {
        flex: 1,
        bottom: 50,
        justifyContent: "center",
        alignItems: 'stretch',
    },
    recipeName: {
        fontSize: 19,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    nutritionCardContainer: {
        flexDirection: "row",
        gap: 24
    }
});