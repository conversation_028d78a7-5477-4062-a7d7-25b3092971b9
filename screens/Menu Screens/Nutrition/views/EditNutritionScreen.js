import {
  <PERSON><PERSON><PERSON>,
  InteractionManager,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useEffect, useRef, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { recipesService } from "services/recipesService";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import {
  CustomAlert,
  CustomButton,
  CustomLoader,
} from "components/CustomAction";
import { useNavigation, useRoute } from "@react-navigation/native";
import AddedMealCard from "../components/AddedMealCard";
import useNutritionMealStore from "store/nutritionMealsStore";
import { mealRecordsService } from "services/mealRecordsService";
import SearchBar from "components/CustomAction/SearchBar";
import { getMealTime } from "../utils/getMealTime";
import useNutritionMealRecordStore from "store/nutritionMealRecordStore";
import useIngredientsStore from "store/ingredientsStore";
import SkeletonItem, { SkeletonList } from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";
import { useAuth } from "context/AuthContext";
import { screenHeight } from "constants/sizes";
import CustomToast from "components/CustomAction/CustomToast";

const EditNutritionScreen = () => {
  const navigate = useNavigation();
  const route = useRoute();
  const scrollViewRef = useRef();
  const { state: { user } } = useAuth();

  const [isFetchingMealData, setIsFetchingMealData] = useState(true);

  const [loading, setLoadingRecipes] = useState(false);
  const [recipeQuery, setRecipeQuery] = useState("");
  const [recipes, setRecipes] = useState([]);
  const [error, setError] = useState();
  const [hasMoreRecipes, setHasMoreRecipes] = useState(true);
  const [page, setPage] = useState(1);

  const mealData = useNutritionMealStore((state) => state.meals);
  const prevMealsData = useNutritionMealStore((state) => state.prevMealsData);
  const initializeMealsData = useNutritionMealStore(
    (state) => state.initializeMealsData
  );

  const isLoadingMealStore = useNutritionMealRecordStore(
    (state) => state.isLoadingMealStore
  );

  const setIsLoadingMealStore = useNutritionMealRecordStore(
    (state) => state.setIsLoadingMealStore
  );
  const getMealRecords = useNutritionMealRecordStore(
    (state) => state.getMealRecords
  );
  const getMealGraphRecords = useNutritionMealRecordStore(
    (state) => state.getMealGraphRecords
  );
  const getNutritionHighlights = useNutritionMealRecordStore(
    (state) => state.getNutritionHighlights
  );
  const getHistoryMealRecords = useNutritionMealRecordStore(
    (state) => state.getHistoryMealRecords
  );

  const resetIngredients = useIngredientsStore((state) => state.resetIngredients);

  const [outsideClick, setOutsideClick] = useState(null);
  const [currentScrollPos, setCurrentScrollPos] = useState(0);
  const [hasScreenLoaded, setHasScreenLoaded] = useState(false);

  useEffect(() => {
    setIsFetchingMealData(true);
    InteractionManager.runAfterInteractions(() => {
      (async () => {
        if (route.params?.mealRecordId) {
          const res = await mealRecordsService.getMealRecord(
            route.params.mealRecordId
          );

          if (res.success) {
            const record = res.data;

            const transformedMeals = record.meals.map((meal) => ({
              recipeId: meal.recipeId,
              measurement: meal.measurement,
              quantity: meal.quantity,
              name: meal.title || meal.quantity || "Meal",
              image: meal.thumbnailUrl || "",
              calories: meal.calories || 0,
              protein: meal.protein || 0,
              fiber: meal.fiber || 0,
              fats: meal.fats || 0,
              carbs: meal.carbs || 0,
              author: meal?.author || null,
            }));

            initializeMealsData(transformedMeals);
          } else {
            initializeMealsData([]);
            setError(res.error);
          }
        }
        setIsFetchingMealData(false);
      })();
    });
  }, [route.params]);

  const getRecipesData = async () => {
    if (loading || !hasMoreRecipes) return;
    setLoadingRecipes(true);

    const res = await recipesService.getAllRecipes({
      query: recipeQuery,
      page: page,
    });

    if (res.success) {
      if (res.data.length > 0) {
        setRecipes((prev) => [...prev, ...res.data]);
        setPage((prevPage) => prevPage + 1);
        setHasMoreRecipes(true);
      } else {
        setHasMoreRecipes(false);
      }
    } else {
      setError(res.error);
      setHasMoreRecipes(false);
    }

    setLoadingRecipes(false);
  };

  useEffect(() => {
    let isActive = true;
    setLoadingRecipes(true);
    InteractionManager.runAfterInteractions(() => {
      (async () => {
        setRecipes([]);
        const response = await recipesService.getAllRecipes({
          query: recipeQuery,
          page: 1,
        });

        if (isActive && response.success) {
          setRecipes(response.data);
          setPage(2);
          setHasMoreRecipes(response.data.length > 0);
        } else {
          setRecipes([]);
          setPage(1);
          setHasMoreRecipes(false);
          setError(response.error);
        }
        setLoadingRecipes(false);
      })();
    });

    return () => {
      isActive = false;
    };
  }, [recipeQuery]);

  const handleSaveMeal = async () => {
    setIsLoadingMealStore(true);
    const formattedDate = (
      route?.params?.selectedDate
        ? new Date(route.params.selectedDate)
        : new Date()
    ).toISOString();

    const mealRecordData = {
      mealName: route.params.mealType,
      mealTime: getMealTime(route.params.mealType),
      meals: mealData.map((meal) => {
        const mealItem = {
          recipeId: meal.recipeId,
          quantity: meal.quantity || "small",
          measurement: String(meal.measurement || "grams").toLowerCase(),
          protein: meal.protein || 0,
          calories: meal.calories || 0,
          fats: meal.fats || 0,
          fiber: meal.fiber || 0,
          carbs: meal.carbs || 0,
        };
        return mealItem;
      }),
    };

    let saveMealRes = null;

    if (route?.params?.mealRecordId) {
      saveMealRes = await mealRecordsService.updateMealRecord(
        route.params.mealRecordId,
        mealRecordData
      );
    } else {
      mealRecordData.date = formattedDate;
      saveMealRes = await mealRecordsService.addMealRecord(mealRecordData);
    }

    if (saveMealRes.success) {
      if (route.params?.selectedDate) {
        await getHistoryMealRecords(route.params.selectedDate);
      }
      if (
        !route.params?.selectedDate ||
        route.params?.selectedDate === new Date().toISOString().split("T")[0]
      ) {
        await getMealRecords();
      }

      await getMealGraphRecords();
      await getNutritionHighlights();
      setIsLoadingMealStore(false);

      const isFocused = navigate.isFocused();

      if (isFocused) {
        navigate.goBack();
      }
    } else {
      setError(saveMealRes.error);
      setIsLoadingMealStore(false);
    }
  };

  const hasMealsDataChanged = () => {
    if (mealData.length !== prevMealsData.length) {
      return true;
    }

    const sortMeals = (meals) =>
      [...meals].sort((a, b) => {
        const keyA = `${a.recipeId}-${a.quantity}-${a.measurement}`;
        const keyB = `${b.recipeId}-${b.quantity}-${b.measurement}`;
        return keyA.localeCompare(keyB);
      });

    const sortedCurrentMeals = sortMeals(mealData);
    const sortedPrevMeals = sortMeals(prevMealsData);

    return (
      JSON.stringify(sortedCurrentMeals) !== JSON.stringify(sortedPrevMeals)
    );
  };

  const handleLoadMoreRecipes = () => {
    if (!loading && hasMoreRecipes) {
      getRecipesData();
    }
  };

  const scrollToOffset = (y) => {
    scrollViewRef.current?.scrollTo({
      y: y + currentScrollPos,
      animated: false,
      duration: 300,
    });
  };

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      setHasScreenLoaded(true);
    });
  }, []);

  if (isLoadingMealStore || !hasScreenLoaded) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        scrollEnabled={true}
        nestedScrollEnabled
        onScroll={(e) => {
          setCurrentScrollPos(e.nativeEvent.contentOffset.y);
        }}
      >
        <TouchableWithoutFeedback
          onPress={() => setOutsideClick(null)}
        >
          <View
            style={{
              flex: 1,
              flexDirection: "column",
              paddingBottom: screenHeight * 0.45,
              gap: 16,
            }}
          >
            <View style={styles.header}>
              <Text style={styles.headerTitle}>{route.params.mealType}</Text>
              <SkeletonItem isLoading={isFetchingMealData} width={"35%"} height={32} style={{
                marginTop: 8
              }}>
                <Text style={styles.totalCaloriesText}>
                  {`${mealData.reduce(
                    (total, meal) => total + meal.calories,
                    0
                  )} cal`}
                </Text>
              </SkeletonItem>
            </View>
            <View style={{ marginHorizontal: 0 }}>
              <SkeletonList height={100} items={1} gap={24} borderRadius={25} isLoading={isFetchingMealData}>
                <FlatList
                  data={mealData}
                  keyExtractor={(_, index) => index.toString()}
                  scrollEnabled={false}
                  contentContainerStyle={{ gap: 24 }}
                  style={{ flexGrow: 0 }}
                  renderItem={({ item, index }) => (
                    <AddedMealCard
                      meal={item}
                      index={index}
                      reminderName={route.params.mealType}
                      isCustomMeal={item.author === user.id}

                    />
                  )}
                />
                {
                  hasMealsDataChanged() && mealData?.length == 0 && (
                    <View style={{ marginTop: 8, marginHorizontal: 16 }}>
                      <Text style={{ fontFamily: ThemeFonts.Exo_700, color: Colors.black, fontSize: 16, textAlign: "center" }}>Please save the meals</Text>
                    </View>
                  )
                }
              </SkeletonList>
            </View>

            {hasMealsDataChanged() && (
              <View
                style={{ flexDirection: "row", justifyContent: "flex-end" }}
              >
                {/* Delete button removed as it's not needed for meal records */}
                <CustomButton
                  title={"Save All"}
                  onPress={handleSaveMeal}
                  style={{
                    alignSelf: "flex-end",
                    marginTop: 16,
                  }}
                />
              </View>
            )}

            {(mealData?.length != 0 || isFetchingMealData) && (
              <View style={[styles.header, { marginVertical: 16 }]}>
                <Text style={styles.headerTitle}>Add More</Text>
              </View>
            )}

            <View>
              <SearchBar
                query={recipeQuery}
                keyExtractor={({ item }) => item.id + ""}
                options={recipes.map((recipe, index) => ({
                  label: recipe.title,
                  value: recipe.id,
                  icon: recipe.author === user.id ? "custom" : (
                    index % 3 == 1 ? "recommended" : null
                  )
                  // icon: (
                  //   <Image source={require("assets/icons/custom_meal_icon.png")} style={{ width: 20, height: 20 }} />
                  // )
                }))}
                onValueChange={(value) => {
                  setRecipes([]);
                  setRecipeQuery(value);
                }}
                onPress={(id) => {
                  navigate.navigate("Add Meal", {
                    id: id,
                    meal_name: route.params.mealType,
                  });
                  setOutsideClick(null);
                }}
                id={"meal_search_bar"}
                currentOpenId={outsideClick}
                setCurrentOpenId={setOutsideClick}
                onEndReached={handleLoadMoreRecipes}
                loading={loading}
                scrollToOffset={scrollToOffset}
                remeasureYpos={mealData?.length}
                disabled={isFetchingMealData}
              />

              <Text
                style={{
                  textAlign: "center",
                  fontSize: 24,
                  fontFamily: ThemeFonts.Exo_500,
                  color: Colors.black,
                  marginVertical: 16,
                }}
              >
                Or
              </Text>

              <View style={{ marginBottom: 20 }}>
                <TouchableOpacity
                  onPress={() => {
                    navigate.navigate("Add User Meal", {
                      meal_name: route.params.mealType,
                    });
                    resetIngredients();
                  }
                  }
                  style={{
                    borderRadius: 25,
                    backgroundColor: Colors.lightGray,
                    padding: 12,
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                  activeOpacity={.85}
                  disabled={isFetchingMealData}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                      backgroundColor: Colors.lightGreen,
                      borderRadius: 25,
                      padding: 12,
                    }}
                  >
                    <View style={{ flex: 1 }}>
                      <Text
                        style={{
                          fontSize: 18,
                          color: Colors.black,
                          fontFamily: ThemeFonts.Exo_700,
                        }}
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        Add Custom Meal
                      </Text>
                    </View>
                    <Text
                      style={{
                        paddingVertical: 8,
                        paddingHorizontal: 24,
                        backgroundColor: Colors.primaryPurple,
                        color: Colors.white,
                        borderRadius: 100,
                        fontFamily: ThemeFonts.Exo_600,
                        fontSize: 14,
                      }}
                    >
                      Add
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
      <CustomToast
        error={error}
        clearErrors={() => setError(null)}
      />
    </AppLayout>
  );
};

export default EditNutritionScreen;

const styles = StyleSheet.create({
  header: {
    marginHorizontal: 24,
    // top: 10
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  totalCaloriesText: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_600,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  recipeName: {
    fontSize: 19,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  recipeImgDataContainer: {
    position: "absolute",
    right: 12,
    left: 12,
    bottom: 12,
    backgroundColor: Colors.veryLightGreen,
    padding: 16,
    // paddingVertical: 12,
    borderRadius: 25,
    flexDirection: "row",
    alignItems: "flex-end",
  },
});
