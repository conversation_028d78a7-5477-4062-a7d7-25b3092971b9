import { StyleSheet, Text, View, ScrollView, TouchableWithoutFeedback } from 'react-native';
import React, { useEffect, useState } from 'react';
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import { CustomButton, CustomLoader } from 'components/CustomAction';
import CustomSelectWithLabel from 'components/CustomAction/CustomSelectWithLabel';
import { useNavigation } from '@react-navigation/native';
import useNutritionMealRecordStore, { AVAILABLE_MEAL_TYPES } from 'store/nutritionMealRecordStore';
import useGlobalLoadingState from 'store/globalLoadingState';

const AddMealType = () => {
    const navigation = useNavigation();
    const [newMealType, setNewMealType] = useState('');
    const [error, setError] = useState('');

    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

    const { isLoadingMealRecords, mealRecords } = useNutritionMealRecordStore(state => state);
    const { addMealRecord, getMealRecords } = useNutritionMealRecordStore(state => state);
    const { setLoading } = useGlobalLoadingState(state => state);

    const availableMealOptions = AVAILABLE_MEAL_TYPES.filter(mealType =>
        !mealRecords.some(record =>
            record.mealName?.toLowerCase() === mealType.toLowerCase()
        )
    );

    const handleAddMealType = async () => {
        if (!newMealType) {
            setError('Please select a meal type');
            return;
        }
        setLoading(true);
        addMealRecord(newMealType);
        setNewMealType('');
        await getMealRecords();
        setLoading(false);
    };

    useEffect(() => {
        availableMealOptions.length === 0 && navigation.goBack();
    }, [availableMealOptions.length]);


    if (isLoadingMealRecords) {
        return <AppLayout>
            <View style={styles.loaderContainer}>
                <CustomLoader />
            </View>
        </AppLayout>;
    }

    return (
        <AppLayout>
            <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ flexGrow: 1 }}>
                <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
                    <View style={styles.container}>
                        <Text style={styles.headerTitle}>Add a Meal Type</Text>

                        <View style={styles.inputContainer}>
                            {availableMealOptions.length > 0 && <CustomSelectWithLabel
                                options={availableMealOptions.map(type => ({ label: type, value: type }))}
                                label="Select Meal Type"
                                separateLabel="What type of meal would you like to add?"
                                selectedValue={newMealType}
                                onValueChange={(value) => {
                                    setNewMealType(value);
                                    setError('');
                                }}
                                placeholder="Choose a meal type"
                                // error={error}
                                triggerZ={6}
                                listZ={5}
                                currentOpenDropdown={currentOpenDropdown}
                                dropdownId={1}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                labelStyle={{
                                    color: Colors.veryDarkGreen,
                                    backgroundColor: Colors.veryLightGreen,
                                    paddingVertical: 20
                                }}
                            />}
                            {error ? <Text style={styles.errorText}>{error}</Text> : null}

                            <CustomButton
                                title="Add Meal"
                                onPress={handleAddMealType}
                                style={styles.addButton}
                                fontFamily={ThemeFonts.Exo_600}
                            />
                        </View>
                    </View>
                </TouchableWithoutFeedback>
            </ScrollView>
        </AppLayout>
    );
};

export default AddMealType;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingHorizontal: 8,
        paddingTop: 20
    },
    loaderContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        bottom: 50,
    },
    backButton: {
        marginBottom: 20,
    },
    headerTitle: {
        fontSize: 32,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.black,
        marginBottom: 30,
        paddingLeft: 16
    },
    inputContainer: {
        marginBottom: 30,
    },
    label: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.black,
        marginBottom: 10,
    },
    errorText: {
        color: 'red',
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        marginVertical: 5,
        marginHorizontal: 10,
    },
    addButton: {
        width: '100%',
        marginTop: 20,
    }
});