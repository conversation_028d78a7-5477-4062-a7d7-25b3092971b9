import { StyleSheet, Text, View } from 'react-native'
import React, { useState } from 'react'
import AddCustomMeal from './CustomMeal/AddCustomMeal';
import AddMealIngredient from './CustomMeal/AddMealIngredient';

const AddUserMeal = () => {
    const [currentScreen, setCurrentScreen] = useState(1);

    return (
        <>
            {currentScreen == 1 && <AddCustomMeal setCurrentScreen={setCurrentScreen} />}
            {currentScreen == 2 && <AddMealIngredient setCurrentScreen={setCurrentScreen} />}
            {currentScreen == 3 && <SelectCustmIngredient setCurrentScreen={setCurrentScreen} />}
        </>
    )
}

export default AddUserMeal

const styles = StyleSheet.create({})