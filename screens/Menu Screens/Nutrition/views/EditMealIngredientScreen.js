import {
  Interaction<PERSON>anager,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useEffect, useRef, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { useNavigation, useRoute } from "@react-navigation/native";
import { CustomButton, CustomLoader } from "components/CustomAction";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import useNutritionMealStore from "store/nutritionMealsStore";
import CustomMealImage from "../components/CustomImageWithTextInput";
import NutrientCard from "../components/NutrientCard";
import useIngredientsStore from "store/ingredientsStore";
import { mealRecordsService } from "services/mealRecordsService";
import getNutrients from "../utils/getNutrients";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const Measurement_Options = ["grams", "milligrams", "oz"];

const Quantity_Options = ["small", "medium", "large"];

const EditMealIngredientScreen = () => {
  const navigate = useNavigation();
  const route = useRoute();

  const navigator = useNavigation();
  const scrollViewRef = useRef();
  const meals = useNutritionMealStore((state) => state.meals);
  const addedIngredients = useIngredientsStore((state) => state.ingredients);
  const editIngredient = useIngredientsStore((state) => state.editIngredient);
  const deleteIngredient = useIngredientsStore((state) => state.deleteIngredient);

  const selectedMealQuantity = useIngredientsStore((state) => state.selectedMealQuantity);
  const addedIngredient = useIngredientsStore((state) => state.ingredients);

  const [error, setError] = useState();

  // Custom meal data
  const [mealName, setMealName] = useState(addedIngredients[route.params?.id]?.name);
  const [mealImage, setMealImage] = useState(addedIngredients[route.params?.id]?.image);
  const [ingredientMeasurements, setIngredientMeasurements] = useState([]);

  const [selectedMeasurement, setSelectedMeasurement] = useState(addedIngredients[route.params?.id]?.measurement);
  const [selectedQuantity, setSelectedQuantity] = useState(addedIngredients[route.params?.id]?.quantity);

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  const [loading, setLoading] = useState(true);
  const [hasScreenLoaded, setHasScreenLoaded] = useState(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      setLoading(true);
      (async () => {
        const res = await mealRecordsService.getIngredient(addedIngredients[route.params?.id]?.recipeId);
        if (res.success) {
          setMealName(res.data.name);
          setMealImage(res.data.thumbnailUrl);
          setIngredientMeasurements(res.data.ingredientNutritionByQuantity);
        }
        else {
          setError(res.error);
        }
        setLoading(false);
      })();

      setHasScreenLoaded(true);
    });
  }, []);

  const scrollToOffset = (y, animated = true) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: animated, duration: 300 });
  };

  const handleSave = () => {
    editIngredient({
      data: {
        recipeId: addedIngredients[route.params?.id]?.recipeId,
        measurement: selectedMeasurement,
        quantity: selectedQuantity,
        name: mealName,
        image: mealImage,
        calories: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.calories,
        protein: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.protein,
        fiber: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.fiber,
        fats: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.fats,
        carbs: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.carbs,
      },
      index: route.params.id
    });
    navigator.goBack();
  };

  const handleDelete = () => {
    deleteIngredient(route.params.id);
    navigator.goBack();
  };

  if (error) {
    return (
      <AppLayout>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            padding: 16,
            gap: 8,
          }}
        >
          <Text style={styles.dataFetchError}>{error}</Text>
          <CustomButton
            title={"Go Back"}
            onPress={() => {
              navigator.goBack();
            }}
          />
        </View>
      </AppLayout>
    )
  }

  if (!hasScreenLoaded) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        ref={scrollViewRef}
        contentContainerStyle={{
          flexGrow: 1,
        }}
      >
        <TouchableWithoutFeedback
          onPress={() => {
            setCurrentOpenDropdown(null);
          }}
        >
          <View style={{
            flex: 1, flexDirection: "column", gap: 32, marginHorizontal: 0,
            paddingBottom: 90,
          }}>
            <View style={styles.header}>
              <Text style={styles.headerTitle}>
                {route.params?.meal_name || "Add Custom Meal"}
              </Text>
              <SkeletonItem isLoading={loading} width={"35%"} height={32} style={{
                marginTop: 8
              }}>
                <Text style={styles.totalCaloriesText}>{`${meals.reduce((total, meal) => total + meal.calories, 0) + (
                  selectedQuantity ? getNutrients(ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.calories, selectedMealQuantity) : 0
                ) + getNutrients(addedIngredient.reduce((total, ingredient, i) => {
                  return i == route.params.id ? total : total + ingredient.calories;
                }, 0), selectedMealQuantity)
                  } kcal`}</Text>
              </SkeletonItem>
            </View>

            {/* Image Selection */}
            <CustomMealImage mealName={mealName} setMealName={setMealName} mealImage={mealImage} pickImage={() => { }} editable={false} calories={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.calories} isLoading={loading} />

            <CustomSelectWithLabel
              separateLabel="What is the measure?"
              label="Add measure"
              selectedValue={selectedMeasurement}
              onValueChange={(value) => setSelectedMeasurement(value)}
              options={
                Measurement_Options.map((value) => {
                  return {
                    label: value,
                    value: value
                  };
                })
              }
              dropdownId={"measurement_options"}
              currentOpenDropdown={currentOpenDropdown}
              setCurrentOpenDropdown={(value) => {
                setCurrentOpenDropdown(value);
              }}
              scrollToOffset={(value) => scrollToOffset(value - 300)}
              triggerZ={10}
              listZ={9}
              isLoading={loading}
            />

            <CustomSelectWithLabel
              separateLabel="What is the quantity?"
              label="Add quantity"
              selectedValue={selectedQuantity}
              onValueChange={(value) => setSelectedQuantity(value)}
              options={
                ingredientMeasurements.map((measure) => {
                  return {
                    label: String(measure.quantity).split("_").join(" "),
                    value: measure.quantity
                  };
                })
              }
              dropdownId={"measurement_options"}
              currentOpenDropdown={currentOpenDropdown}
              setCurrentOpenDropdown={(value) => {
                setCurrentOpenDropdown(value);
              }}
              scrollToOffset={(value) => scrollToOffset(value - 300)}
              triggerZ={8}
              listZ={7}
              isLoading={loading}
            />

            <View style={{ gap: 35, marginHorizontal: 8, marginTop: 24 }}>
              <View style={styles.nutritionCardContainer}>
                <NutrientCard label="Protein" value={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.protein} measurement={selectedMeasurement} loading={loading} />
                <NutrientCard label="Fats" value={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.fats} measurement={selectedMeasurement} loading={loading} />
              </View>
              <View style={styles.nutritionCardContainer}>
                <NutrientCard label="Carbs" value={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.carbs} measurement={selectedMeasurement} loading={loading} />
                <NutrientCard label="Fiber" value={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.fiber} measurement={selectedMeasurement} loading={loading} />
              </View>
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginTop: 8,
                alignItems: "center",
                marginHorizontal: 8,
              }}
            >
              <CustomButton
                title={"Delete"}
                onPress={handleDelete}
                style={{
                  width: "auto",
                  paddingHorizontal: 28,
                  backgroundColor: Colors.white,
                  borderWidth: 2,
                  borderColor: Colors.primaryPurple,
                }}
                textColor={Colors.primaryPurple}
                disabled={
                  !(
                    mealName &&
                    mealImage && selectedMeasurement && selectedQuantity
                  )
                }
              />
              <CustomButton
                title={"Save"}
                onPress={handleSave}
                style={{
                  width: "auto",
                  paddingHorizontal: 28
                }}
                disabled={
                  !(
                    mealName &&
                    mealImage && selectedMeasurement && selectedQuantity
                  )
                }
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default EditMealIngredientScreen;

const styles = StyleSheet.create({
  header: {
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  totalCaloriesText: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_600,
  },
  nutritionCardContainer: {
    flexDirection: "row",
    gap: 24
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});
