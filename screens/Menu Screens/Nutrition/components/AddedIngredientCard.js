import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import { useNavigation } from '@react-navigation/native';
import { CustomButton } from 'components/CustomAction';

const AddedIngredientCard = ({ ingredient, id, route_params }) => {
    const navigate = useNavigation();
    return (
        <View
            style={styles.container}
        >
            <View
                style={styles.mainContainer}
            >
                <View style={{ flex: 1, alignItems: "center", flexDirection: "row", gap: 4 }}>
                    <Text
                        style={styles.ingredientName}
                        numberOfLines={1}
                    >
                        {ingredient.name}
                    </Text>
                    <Text
                        style={styles.ingredientMeasurement}
                        numberOfLines={1}
                    >
                        ({String(ingredient.quantity).split("_").join(" ")})
                    </Text>
                </View>
                <CustomButton title="Edit" style={{ width: "auto", paddingHorizontal: 24 }} onPress={() => {
                    navigate.navigate("Edit Meal Ingredient", { id: id, ...route_params });
                }}
                />
            </View>
        </View>
    )
}

export default AddedIngredientCard

const styles = StyleSheet.create({
    container: {
        borderRadius: 30,
        backgroundColor: 'transparent',
        padding: 8,
        justifyContent: "center",
        alignItems: "center",
        borderWidth: 2,
        borderColor: Colors.primaryGreen
    },
    mainContainer: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        width: "100%",
        backgroundColor: Colors.primaryGreen,
        borderRadius: 25,
        padding: 12,
        paddingHorizontal: 16
    },
    ingredientName: {
        fontSize: 18,
        color: Colors.white,
        fontFamily: ThemeFonts.Exo_700,
    },
    ingredientMeasurement: {
        fontSize: 14,
        color: Colors.white,
        fontFamily: ThemeFonts.Exo_500_Italic,
    }
})