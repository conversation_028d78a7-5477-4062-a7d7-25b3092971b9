import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { ThemeFonts } from 'constants/theme/fonts';
import { Colors } from 'constants/theme/colors';
import { measure } from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';
import { TouchableOpacity } from 'react-native-gesture-handler';

const measurementConversion = (value, measurement) => {
    switch (measurement) {
        case "Grams": return value;
        case "Milligrams": return value * 1000;
        case "oz": return Number(value / 28.3495).toPrecision(3);
        default: return value;
    }
};

const createNutrientsString = (measurement, protein, carbs, fats, fiber) => {
    switch (measurement) {
        case "grams": return `Protein (${protein}g),Fiber (${fiber}g),Fats (${fats}g) & Carbs (${carbs}g)`;
        case "milligrams": return `Protein (${measurementConversion(protein, "Milligrams")}mg),Fiber (${measurementConversion(fiber, "Milligrams")}mg),Fats (${measurementConversion(fats, "Milligrams")}mg) & Carbs (${measurementConversion(carbs, "Milligrams")}mg)`;
        case "oz": return `Protein (${measurementConversion(protein, "oz")}oz),Fiber (${measurementConversion(fiber, "oz")}oz),Fats (${measurementConversion(fats, "oz")}oz) & Carbs (${measurementConversion(carbs, "oz")}oz)`;
    }
};

const AddedMealCard = ({ meal, index, reminderName, isCustomMeal }) => {
    const navigator = useNavigation();
    return (
        <TouchableOpacity
            onPress={() => navigator.navigate(
                "Edit Meal",
                { id: index, meal_name: reminderName }
            )}
            activeOpacity={.95}
        >
            <View>
                {meal?.image && <Image source={{ uri: meal.image }} style={styles.recipeImage} />}
                <View style={{ padding: 12, backgroundColor: meal?.image ? 'transparent' : Colors.lightGray, borderRadius: 25 }}>
                    <View style={styles.recipeImgDataContainer}>
                        <View style={{ flex: 1, alignItems: "flex-start", gap: 4 }}>
                            <Text style={{ fontSize: 10, fontFamily: ThemeFonts.Exo_400, backgroundColor: Colors.primaryGreen, color: Colors.white, paddingHorizontal: 12, borderRadius: 10, paddingVertical: 2 }}>
                                {isCustomMeal ? 'Custom meal' : 'Recommended'}
                            </Text>
                            <Text style={[styles.recipeName]} numberOfLines={1}>{meal.name}</Text>
                        </View>
                        <Text style={{ padding: 8, backgroundColor: Colors.primaryPurple, color: Colors.white, borderRadius: 100, paddingHorizontal: 16, fontFamily: ThemeFonts.Exo_600 }}>{meal.calories} kcal</Text>
                    </View>
                </View>
            </View>
            <View style={{ marginHorizontal: 16, marginVertical: 4 }}>
                <Text style={styles.measurementText}>{createNutrientsString(meal.measurement, meal.protein, meal.carbs, meal.fats, meal.fiber)}</Text>
            </View>
        </TouchableOpacity>
    );
};

export default AddedMealCard;

const styles = StyleSheet.create({
    recipeImage: {
        borderRadius: 25,
        position: "absolute",
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
        // right: 12,
        // left: 12,
        // bottom: 12,
        // top: 12,
    },
    recipeImgDataContainer: {
        backgroundColor: Colors.veryLightGreen,
        padding: 16,
        // paddingVertical: 12,
        borderRadius: 25,
        flexDirection: "row",
        alignItems: "flex-end",
    },
    recipeName: {
        fontSize: 19,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    measurementText: {
        fontSize: 12,
        fontFamily: ThemeFonts.Exo_400
    }
});