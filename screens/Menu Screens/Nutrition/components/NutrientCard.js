import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';


const measurementConversion = (value, measurement) => {
    switch (measurement) {
        case "grams": return `${value} gm`;
        case "milligrams": return `${value * 1000} mg`;
        case "oz": return `${Number(value / 28.3495).toPrecision(3)} oz`;
        default: return value;
    }
};

const NutrientCard = ({ label, value, measurement, loading = false }) => {
    return (
        <View style={styles.nutritionCard}>
            <Text style={styles.nutritionName}>{label}</Text>
            <SkeletonItem height={48} borderRadius={25} isLoading={loading}>
                <Text style={styles.nutritionValue}>{measurementConversion(value, measurement)}</Text>
            </SkeletonItem>
        </View>
    );
};

export default NutrientCard;

const styles = StyleSheet.create({
    nutritionCard: {
        flex: 1,
        gap: 16
    },
    nutritionName: {
        color: Colors.veryDarkGreen,
        fontFamily: ThemeFonts.Exo_700,
        fontSize: 19,
        backgroundColor: Colors.lightGreen,
        borderRadius: 25,
        padding: 16,
        textAlign: "center"
    },
    nutritionValue: {
        color: Colors.white,
        fontFamily: ThemeFonts.Exo_400,
        fontSize: 18,
        borderRadius: 25,
        padding: 16,
        paddingVertical: 8,
        textAlign: "center",
        borderWidth: 2,
        borderColor: Colors.veryDarkGreen,
        color: Colors.black
    }
});