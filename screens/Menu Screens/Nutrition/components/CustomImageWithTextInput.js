import { Image, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { Entypo } from "@expo/vector-icons";
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';

const CustomImageWithTextInput = ({ mealName, setMealName, mealImage, pickImage = () => { }, editable, calories = 0, subTile = "Custom meal", isLoading }) => {

    if (isLoading) {
        return (
            <SkeletonItem height={225} borderRadius={25} />
        )
    }
    return (
        <TouchableOpacity
            onPress={() => {
                editable && pickImage();
            }
            }
            activeOpacity={editable ? 0.85 : 1}
        >
            <View style={{ borderRadius: 25, overflow: "hidden" }}>
                {mealImage ? (
                    <Image source={{ uri: mealImage }} style={styles.recipeImage} />
                ) : (
                    <View style={styles.imagePlaceholder}>
                        <View style={styles.addImageIcon}>
                            <Entypo name="image" size={40} color={Colors.darkGray} />
                            <Entypo name="plus" size={18} color={Colors.darkGray} style={styles.plusIcon} />
                        </View>
                    </View>
                )}
                <View style={styles.recipeImgDataContainer}>
                    <View style={{ flex: 1, gap: 4 }}>
                        <Text style={{ fontSize: 10, fontFamily: ThemeFonts.Exo_400, backgroundColor: Colors.primaryGreen, color: Colors.white, paddingHorizontal: 12, borderRadius: 10, paddingVertical: 2, alignSelf: "flex-start" }}>{subTile}</Text>
                        {
                            editable ? <TextInput style={[styles.recipeName, {
                                fontFamily: mealName ? ThemeFonts.Exo_700 : ThemeFonts.Exo_700_Italic,
                            }]} numberOfLines={1} value={mealName} editable={editable} onChangeText={(value) => {
                                setMealName(value)
                            }} placeholder='Enter meal name' placeholderTextColor={Colors.darkGray} /> : <Text style={[styles.recipeName]} numberOfLines={1} ellipsizeMode='tail'>{mealName}</Text>
                        }
                    </View>
                    <Text style={{ padding: 8, backgroundColor: Colors.primaryPurple, color: Colors.white, borderRadius: 100, paddingHorizontal: 16, fontFamily: ThemeFonts.Exo_600 }}>
                        {`${calories} kcal`}
                    </Text>
                </View>
            </View>
        </TouchableOpacity>
    )
}

export default CustomImageWithTextInput

const styles = StyleSheet.create({
    imageContainer: {
        borderRadius: 25,
        overflow: "hidden",
        marginHorizontal: 16,
        height: 225,
    },
    mealImage: {
        height: "100%",
        width: "100%",
        borderRadius: 25,
    },
    imagePlaceholder: {
        height: "100%",
        width: "100%",
        backgroundColor: Colors.lightGray,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 25,
        height: 225,
    },
    addImageIcon: {
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
    },
    plusIcon: {
        position: 'absolute',
        top: -4,
        left: -4,
        backgroundColor: Colors.lightGray,
        // borderRadius: 10,
    },
    recipeImage: {
        height: "100%",
        borderRadius: 25,
        height: 225,
    },
    recipeName: {
        fontSize: 19,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    recipeImgDataContainer: {
        position: "absolute",
        right: 12,
        left: 12,
        bottom: 12,
        backgroundColor: Colors.veryLightGreen,
        padding: 16,
        // paddingVertical: 12,
        borderRadius: 25,
        flexDirection: "row",
        alignItems: "flex-end",
        gap: 8
    },
    imageMealPlaceholder: {
        fontSize: 19,
        color: Colors.gray,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_600_Italic,
    }
})