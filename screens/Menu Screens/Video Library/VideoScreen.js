import {
  <PERSON><PERSON><PERSON><PERSON>,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { CustomAlert, CustomLoader, CustomSelect } from "components/CustomAction";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import VideoOverviewCard from "components/CustomCards/VideoOverviewCard";
import { FlatList } from "react-native";
import FlatListBottomLoader from "components/Loaders/FlatListBottomLoader";
import videosService from "services/videosService";
import CustomToast from "components/CustomAction/CustomToast";
import { InteractionManager } from "react-native";

const VIDEOS_CATEGORIES = [
  {
    label: "Workouts",
    value: "workout",
  },
  {
    label: "Wellness",
    value: "wellness",
  },
];

const VideoScreen = () => {

  const [selectedCategory, setSelectedCategory] = useState(null);

  const [isLoadingVideos, setIsLoadingVideos] = useState(true);
  const [videos, setVideos] = useState([]);
  const [page, setPage] = useState(1);
  const [hasMoreVideos, setHasMoreVideos] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);

  const [error, setError] = useState(null);
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  const getVideos = async () => {
    if (isLoadingVideos || !hasMoreVideos) return;
    setIsLoadingVideos(true);

    const response = await videosService.getAllVideos({
      tag: selectedCategory,
      page: page,
    });

    if (response.success) {
      setVideos((prevData) => [...prevData, ...response.data]);
      setPage((prevPage) => prevPage + 1);
      setHasMoreVideos(response.data.length > 0);
    } else {
      setHasMoreVideos(false);
      setError(response.error);
    }

    setIsLoadingVideos(false);
  };

  useEffect(() => {
    let isMounted = true;

    InteractionManager.runAfterInteractions(() => {
      setIsLoadingVideos(true);
      setPage(1);
      setHasMoreVideos(true);
      setVideos([]);

      (async () => {
        const getRecipesRes = await videosService.getAllVideos({
          tag: selectedCategory,
        });

        if (isMounted) {
          if (getRecipesRes.success) {
            setVideos(getRecipesRes.data);
            setPage(2);
            setHasMoreVideos(getRecipesRes.data.length > 0);
          } else {
            setError(getRecipesRes.error);
          }
        }

        setIsLoadingVideos(false);
      })();
    });

    return () => {
      isMounted = false;
    };
  }, [selectedCategory]);

  const onRefresh = async () => {
    setPage(1);
    setIsLoadingVideos(true);
    setHasMoreVideos(true);
    setIsRefreshing(true);
    setVideos([]);
    setIsLoadingVideos(true);

    const response = await videosService.getAllVideos({
      tag: selectedCategory,
      page: 1,
    });

    if (response.success) {
      setVideos(response.data);
      setPage(2);
      setHasMoreVideos(response.data.length > 0);
    } else {
      setHasMoreVideos(false);
      setError(response.error);
    }

    setIsLoadingVideos(false);
    setIsRefreshing(false);
  };

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      setHasLoaded(true);
    });
  }, []);

  if (!hasLoaded) return (
    <AppLayout>
      <View style={styles.loaderContainer}>
        <CustomLoader />
      </View>
    </AppLayout>
  );

  return (
    <AppLayout>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          { flexGrow: 1 },
          {
            paddingBottom: isLoadingVideos && hasMoreVideos ? 90 : 90,
          },
        ]}
        onScroll={(e) => {
          let paddingToBottom = 90;
          paddingToBottom += e.nativeEvent.layoutMeasurement.height;
          if (
            e.nativeEvent.contentOffset.y >=
            e.nativeEvent.contentSize.height - paddingToBottom
          ) {
            getVideos();
          }
        }}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[Colors.primaryGreen]}
            progressViewOffset={24}
          />
        }
      >
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View style={{ flex: 1 }}>
            <Text style={styles.headerTitle}>Video library</Text>
            <CustomSelect
              options={VIDEOS_CATEGORIES}
              label="Select"
              selectedValue={selectedCategory}
              onValueChange={(value) => setSelectedCategory(value)}
              backgroundColor={Colors.primaryPurple}
              textColor={Colors.white}
              triggerZ={20}
              listZ={19}
              currentOpenDropdown={currentOpenDropdown}
              dropdownId={1}
              setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
              changeBG={true}
            />
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                marginTop: 16,
              }}
            >
              <FlatList
                data={videos}
                scrollEnabled={false}
                numColumns={1}
                keyExtractor={(item) => item.id}
                contentContainerStyle={{
                  gap: 28,
                }}
                showsVerticalScrollIndicator={false}
                style={{}}
                renderItem={({ item, index }) => (
                  <VideoOverviewCard
                    video={{
                      title: item.title,
                      description: item.description,
                      videoUrl: item.videoUrl,
                      id: item.id,
                      createdAt: item.createdAt,
                      isPublished: item.isPublished,
                    }}
                  />
                )}
                ListFooterComponent={
                  isLoadingVideos && hasMoreVideos ? (
                    <FlatListBottomLoader />
                  ) : null
                }
              />
            </View>
            <CustomToast
              error={error}
              clearErrors={() => {
                setError(null);
              }}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default VideoScreen;

const styles = StyleSheet.create({
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
    paddingLeft: 22,
    marginBottom: 28,
    textTransform: "capitalize",
  },
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    bottom: 50,
  },
});
