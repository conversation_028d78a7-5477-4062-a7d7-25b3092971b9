import {
  <PERSON><PERSON><PERSON>,
  InteractionManager,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useCallback, useEffect, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import SleepLineGraph from "components/Charts/LineGraphs/SleepLineGraph";
import DeviceBarGraph from "components/Charts/BarGraphs/DeviceBarGraph";
import NutritionStackedBarGraph from "components/Charts/BarGraphs/NutritionStackedBarGraph";
import WeightLineGraph from "components/Charts/LineGraphs/WeightLineGraph";
import ActivityLineGraph from "components/Charts/LineGraphs/ActivityLineGraph";
import MoodLineGraph2 from "components/Charts/LineGraphs/MoodLineGraph2";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import GraphWrapper from "./components/GraphWrapper";
import useSleepStore from "store/sleepStore";
import useDeviceTimerStore from "store/deviceTimerStore";
import useMoodStore from "store/moodStore";
import { useAuth } from "context/AuthContext";
import formatDuration from "../Device/utils/formartDuration";
import useNutritionMealRecordStore from "store/nutritionMealRecordStore";
import useUserWeightStore from "store/userWeightStore";
import useActivityStore from "store/activityStore";
import { CustomLoader } from "components/CustomAction";

const SummaryScreen = () => {
  const {
    state: { user },
  } = useAuth();

  const isLoadingTimerHistory = useDeviceTimerStore((state) => state.isLoadingTimerHistory);
  const totalUserTime = useDeviceTimerStore((state) => state.totalUserTime);
  const getTimerHistory = useDeviceTimerStore((state) => state.getTimerHistory);
  const getTimerGraphRecords = useDeviceTimerStore((state) => state.getTimerGraphRecords);

  const isLoadingLastSleepLogged = useSleepStore((state) => state.isLoadingLastSleepLogged);
  const lastLoggedSleep = useSleepStore((state) => state.lastLoggedSleep);
  const getLastLoggedSleep = useSleepStore((state) => state.getLastLoggedSleep);
  const getSleepGraphRecords = useSleepStore((state) => state.getSleepGraphRecords);

  const isLoadingLastLoggedMood = useMoodStore((state) => state.isLoadingLastLoggedMood);
  const lastLoggedMood = useMoodStore((state) => state.lastLoggedMood);
  const getMoodLastLogged = useMoodStore((state) => state.getMoodLastLogged);
  const getMoodGraphData = useMoodStore((state) => state.getMoodGraphData);

  const isLoadingMealRecords = useNutritionMealRecordStore((state) => state.isLoadingMealRecords);
  const todayTotalCalories = useNutritionMealRecordStore((state) => state.todayTotalCalories);
  const getMealRecords = useNutritionMealRecordStore((state) => state.getMealRecords);
  const getMealGraphRecords = useNutritionMealRecordStore((state) => state.getMealGraphRecords);

  const isLoadingLastLoggedWeightData = useUserWeightStore(state => state.isLoadingLastLoggedWeightData);
  const lastLoggedWeight = useUserWeightStore(state => state.lastLoggedWeight);
  const getLastLoggedWeightData = useUserWeightStore(state => state.getLastLoggedWeightData);
  const getWeightGraphData = useUserWeightStore(state => state.getWeightGraphData);

  const isLoadingActivityLedger = useActivityStore(state => state.isLoadingActivityLedger);
  const activityLedgerData = useActivityStore(state => state.activityLedgerData);
  const getTodaysLedger = useActivityStore(state => state.getTodaysLedger);
  const getActivityGraph = useActivityStore(state => state.getActivityGraph);

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  const [hasLoaded, setHasLoaded] = useState(false);

  const refreshGraphData = async () => {
    // setIsLoading(true);
    // await Promise.all([
    getTimerHistory();
    getTimerGraphRecords();
    getMoodGraphData();
    getMoodLastLogged();
    getSleepGraphRecords();
    getLastLoggedSleep();
    getMealRecords();
    getMealGraphRecords();
    getLastLoggedWeightData();
    getWeightGraphData()
    getTodaysLedger()
    getActivityGraph()
    // ]);
    // setIsLoading(false);
  };

  useEffect(() => {
    InteractionManager.runAfterInteractions(
      () => {
        refreshGraphData();
        setHasLoaded(true);
      }
    );
  }, []);

  if (!hasLoaded) return (
    <AppLayout>
      <View style={styles.loaderContainer}>
        <CustomLoader />
      </View>
    </AppLayout>
  );

  return (
    <AppLayout>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          { flexGrow: 1 },
          {
            paddingBottom: 90,
          },
        ]}
        refreshControl={
          <RefreshControl
            onRefresh={refreshGraphData}
            colors={[Colors.primaryGreen]}
            progressViewOffset={24}
          />
        }
      >
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View style={{ gap: 16 }}>
            <Text style={styles.headerTitle}>Summary</Text>
            <View style={{ gap: 24 }}>
              <GraphWrapper
                graphName="Device usage"
                userCurrValue={formatDuration(totalUserTime)}
                userGoal={user?.deviceUsageLimit != 'null' ? `${user?.deviceUsageLimit}hr` : null}
                isLoadingMetaData={isLoadingTimerHistory}
                Graph={hasLoaded && DeviceBarGraph}
                currentOpenDropdown={currentOpenDropdown}
                setCurrentOpenDropdown={setCurrentOpenDropdown}
              />
              <GraphWrapper
                graphName="Sleep"
                userCurrValue={`${lastLoggedSleep?.numOfHours || 0}hr`}
                userGoal={`${user?.goals?.filter((goal) => goal?.goal_type === "sleep")[0]
                  ?.selected_goal
                  }hr`}
                isLoadingMetaData={isLoadingLastSleepLogged}
                Graph={hasLoaded && SleepLineGraph}
                currentOpenDropdown={currentOpenDropdown}
                setCurrentOpenDropdown={setCurrentOpenDropdown}
              />
              <GraphWrapper
                graphName="Mood"
                userCurrValue={
                  lastLoggedMood?.moodType
                    ? lastLoggedMood?.moodType[0].toUpperCase() +
                    lastLoggedMood?.moodType.slice(1)
                    : ''
                }
                isLoadingMetaData={isLoadingLastLoggedMood}
                Graph={hasLoaded && MoodLineGraph2}
                currentOpenDropdown={currentOpenDropdown}
                setCurrentOpenDropdown={setCurrentOpenDropdown}
              />
              <GraphWrapper
                graphName="Food"
                userCurrValue={
                  `${todayTotalCalories} cal`
                }
                isLoadingMetaData={isLoadingMealRecords || !hasLoaded}
                Graph={hasLoaded && NutritionStackedBarGraph}
                currentOpenDropdown={currentOpenDropdown}
                setCurrentOpenDropdown={setCurrentOpenDropdown}
              />
              <GraphWrapper
                graphName="Weight"
                userCurrValue={
                  lastLoggedWeight?.weight
                    ? `${lastLoggedWeight?.weight} kg`
                    : `${user?.weight} kg`
                }
                isLoadingMetaData={isLoadingLastLoggedWeightData || !hasLoaded}
                Graph={hasLoaded && WeightLineGraph}
                currentOpenDropdown={currentOpenDropdown}
                setCurrentOpenDropdown={setCurrentOpenDropdown}
              />
              <GraphWrapper
                graphName="Activity"
                isLoadingMetaData={isLoadingActivityLedger || !hasLoaded}
                userCurrValue={`${Number(activityLedgerData?.totalBurnedCalories || 0).toFixed(0)} kcal`}
                Graph={hasLoaded && ActivityLineGraph}
                currentOpenDropdown={currentOpenDropdown}
                setCurrentOpenDropdown={setCurrentOpenDropdown}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default SummaryScreen;

const styles = StyleSheet.create({
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
    paddingLeft: 22,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    bottom: 50,
  },
});
