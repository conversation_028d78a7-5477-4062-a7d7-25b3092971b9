import {
    Interaction<PERSON>anager,
    ScrollView,
    StyleSheet,
    Text,
    TouchableWithoutFeedback,
    View,
} from "react-native";
import React, { useCallback, useEffect, useRef, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import {
    CustomAlert,
    CustomButton,
    CustomLoader,
} from "components/CustomAction";
import { useNavigation, useRoute } from "@react-navigation/native";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { ACTIVITY_TYPES, DURATIONS } from "../constants";
import { activityService } from "services/activityService";
import useActivityStore from "store/activityStore";
import CustomToast from "components/CustomAction/CustomToast";

const EditActivityScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const scrollViewRef = useRef();

    // Activity store
    const updateActivityInStore = useActivityStore(state => state.updateActivity);
    const deleteActivityFromStore = useActivityStore(state => state.deleteActivity);
    const getActivityHighlights = useActivityStore(state => state.getActivityHighlights);
    const getTodaysLedger = useActivityStore(state => state.getTodaysLedger);
    const getRecentActivity = useActivityStore(state => state.getRecentActivity);

    const [isLoading, setIsLoading] = useState(true);
    const [dataFetchError, setDataFetchError] = useState(null);

    const [activityData, setActivityData] = useState(null);
    const [selectedDuration, setSelectedDuration] = useState(null);
    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
    const [editActivityError, setEditActivityError] = useState(null);

    const [showDeleteConfirmPopup, setShowDeleteConfirmPopup] = useState(false);

    const [validationError, setValidationError] = useState(null);
    const [hasLoadedScreen, setHasLoadedScreen] = useState(false);

    useEffect(() => {
        setIsLoading(true);
        InteractionManager.runAfterInteractions(() => {
            fetchActivityData();
            setHasLoadedScreen(true);
        });
    }, [route.params.id]);

    const fetchActivityData = async () => {
        try {
            const res = await activityService.getActivityById(route.params.id);
            if (res.success) {
                setActivityData(res.data);
                setSelectedDuration(res.data.durationInMinutes);
            } else {
                setDataFetchError(res.error);
            }
        } catch (error) {
            setDataFetchError("Failed to fetch activity data");
        } finally {
            setIsLoading(false);
        }
    };

    const validateData = () => {
        if (!selectedDuration) {
            setValidationError({
                durationError: "Please select a duration",
            });
            return false;
        }

        setValidationError(null);
        return true;
    };

    const handleEditActivity = async () => {
        // Check if duration has changed
        if (activityData && activityData.durationInMinutes === selectedDuration) {
            navigation.goBack();
            return;
        }

        if (!validateData()) return;

        setIsLoading(true);
        try {
            const res = await updateActivityInStore({
                id: route.params.id,
                durationInMinutes: selectedDuration,
            });

            if (res.success) {
                getActivityHighlights();
                getTodaysLedger();
                getRecentActivity();
                const isFocused = navigation.isFocused();

                if (isFocused) {
                    navigation.goBack();
                }
            } else {
                setEditActivityError(res.error);
            }
        } catch (error) {
            setEditActivityError("Failed to update activity");
        } finally {
            setIsLoading(false);
        }
    };

    const scrollToOffset = useCallback((y) => {
        scrollViewRef.current?.scrollTo({ y: y, animated: true });
    }, [scrollViewRef]);

    const handleDeleteActivity = async () => {
        setShowDeleteConfirmPopup(false);
        setIsLoading(true);

        try {
            const response = await deleteActivityFromStore(route?.params?.id);

            if (response.success) {
                getActivityHighlights();
                getTodaysLedger();
                getRecentActivity();

                const isFocused = navigation.isFocused();

                if (isFocused) {
                    navigation.goBack();
                }
            } else {
                setEditActivityError(response.error);
                setIsLoading(false);
            }
        } catch (error) {
            setEditActivityError("Failed to delete activity");
            setIsLoading(false);
        }
    };

    if (dataFetchError) {
        return (
            <AppLayout>
                <View
                    style={{
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                        padding: 16,
                        gap: 8,
                    }}
                >
                    <Text style={styles.dataFetchError}>{dataFetchError}</Text>
                    <CustomButton
                        title={"Go Back"}
                        onPress={() => {
                            navigation.goBack();
                        }}
                    />
                </View>
            </AppLayout>
        );
    }

    if (!hasLoadedScreen) {
        return (
            <AppLayout>
                <View style={styles.loaderContainer}>
                    <CustomLoader />
                </View>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <TouchableWithoutFeedback
                onPress={() => {
                    setCurrentOpenDropdown(null);
                }}
            >
                <View style={{ flex: 1, flexDirection: "column", marginBottom: 80 }}>
                    <ScrollView
                        ref={scrollViewRef}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{}}
                    >
                        <TouchableWithoutFeedback
                            style={styles.container}
                            onPress={() => {
                                setCurrentOpenDropdown(null);
                            }}
                        >
                            <View style={{ paddingBottom: 120 }}>
                                <CustomToast
                                    error={editActivityError}
                                    clearErrors={() => {
                                        setEditActivityError(null);
                                    }}
                                />
                                <View style={styles.header}>
                                    <Text style={styles.headerText}>
                                        Edit Activity
                                    </Text>
                                    <MaterialIcons name="edit" size={32} color={Colors.black} />
                                </View>
                                <View style={{ gap: 36, marginTop: 24 }}>
                                    <CustomSelectWithLabel
                                        options={ACTIVITY_TYPES}
                                        label="Activity Type"
                                        selectedValue={activityData?.activityType}
                                        onValueChange={(value) => { }}
                                        triggerZ={8}
                                        listZ={7}
                                        currentOpenDropdown={currentOpenDropdown}
                                        scrollToOffset={(value) => scrollToOffset(value)}
                                        dropdownId={1}
                                        setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                        isEditing={false}
                                        disabledSelectColor={Colors.lightGray}
                                        disabledTextColor={Colors.black}
                                        showArrowUpDown={false}
                                        isLoading={isLoading}
                                    />

                                    {/* Duration - Editable */}
                                    <CustomSelectWithLabel
                                        options={DURATIONS}
                                        label="Duration"
                                        selectedValue={selectedDuration}
                                        onValueChange={(value) => setSelectedDuration(value)}
                                        triggerZ={6}
                                        listZ={5}
                                        currentOpenDropdown={currentOpenDropdown}
                                        scrollToOffset={(value) => scrollToOffset(value)}
                                        error={validationError?.durationError}
                                        dropdownId={1}
                                        setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                        clearValidationError={() => {
                                            setValidationError((prev) => ({
                                                ...prev,
                                                durationError: "",
                                            }));
                                        }}
                                        isLoading={isLoading}
                                    />
                                </View>
                            </View>
                        </TouchableWithoutFeedback>
                    </ScrollView>
                    <View style={styles.buttonContainer}>
                        <CustomButton
                            title={"Delete"}
                            onPress={() => {
                                setShowDeleteConfirmPopup(true);
                            }}
                            style={isLoading ? { width: 96 } : {
                                width: 96,
                                backgroundColor: Colors.white,
                                borderWidth: 2,
                                borderColor: Colors.primaryPurple,
                            }}
                            textColor={Colors.primaryPurple}
                            isLoading={isLoading}
                            disabled={isLoading}
                        />
                        <CustomButton
                            title={"Save"}
                            onPress={handleEditActivity}
                            style={{
                                width: 96,
                            }}
                            isLoading={isLoading}
                            disabled={isLoading}
                        />
                    </View>
                    <CustomAlert
                        title={"Delete Activity"}
                        message={"Do you want to delete this activity?"}
                        visible={showDeleteConfirmPopup}
                        buttons={[
                            {
                                text: "Cancel",
                                onPress: () => {
                                    setShowDeleteConfirmPopup(false);
                                },
                                style: "allowButton",
                            },
                            { text: "Delete", onPress: () => { handleDeleteActivity(); setShowDeleteConfirmPopup(false); }, style: "allowButton" },
                        ]}
                        onClose={() => {
                            setShowDeleteConfirmPopup(false);
                        }}
                    />
                </View>
            </TouchableWithoutFeedback>
        </AppLayout>
    );
};

export default EditActivityScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginBottom: 20,
        gap: 10,
        flexDirection: "column",
        justifyContent: "space-between",
        paddingBottom: 70,
        backgroundColor: Colors.lightOrange,
    },
    header: {
        flexDirection: "row",
        justifyContent: "flex-start",
        alignItems: "center",
        marginBottom: 20,
        marginHorizontal: 16,
        gap: 5,
    },
    headerText: {
        fontSize: 32,
        color: Colors.black,
        textAlign: "start",
        fontFamily: "Exo_700Bold",
        textTransform: "capitalize",
        textAlign: "left",
    },
    buttonContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 10,
        alignItems: "center",
        marginRight: 5,
    },
    dropdown: {
        marginVertical: 5,
    },
    loaderContainer: {
        flex: 1,
        bottom: 50,
        justifyContent: "center",
        alignItems: "stretch",
    },
    dataFetchError: {
        top: -20,
        fontSize: 16,
        fontFamily: "Exo_500Medium",
        textAlign: "center",
    },
    readOnlyField: {
        marginHorizontal: 16,
    },
    readOnlyLabel: {
        fontSize: 16,
        fontFamily: "Exo_600SemiBold",
        color: Colors.black,
        marginBottom: 8,
    },
    readOnlyValue: {
        backgroundColor: Colors.lightGray,
        borderRadius: 12,
        padding: 16,
        borderWidth: 1,
        borderColor: Colors.gray,
    },
    readOnlyText: {
        fontSize: 16,
        fontFamily: "Exo_500Medium",
        color: Colors.darkGray,
        textTransform: "capitalize",
    },
});
