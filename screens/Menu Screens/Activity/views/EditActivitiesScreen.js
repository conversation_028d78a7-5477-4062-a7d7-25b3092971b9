import {
  <PERSON><PERSON><PERSON>,
  InteractionManager,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useState, useEffect, useRef } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import {
  CustomButton,
  CustomLoader,
} from "components/CustomAction";
import { ACTIVITY_TYPES, DURATIONS } from "../constants";
import useActivityStore from "store/activityStore";
import ActivityOverviewCard from "../components/ActivityOverviewCard";
import SkeletonItem, { SkeletonList } from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";
import { Ionicons } from '@expo/vector-icons';
import { RefreshControl } from "react-native";
import CustomSelectWithFixedHeightAndLabel from "components/CustomAction/CustomSelectWithFixedHeightAndLabel";
import CustomToast from "components/CustomAction/CustomToast";
import { useAuth } from "context/AuthContext";
import { getMovementCalories } from "constants/options";

const EditActivitiesScreen = () => {
  const scrollViewRef = useRef();

  const { state: { user } } = useAuth();

  // Activity store
  const saveActivity = useActivityStore(state => state.saveActivity);
  const isSavingActivity = useActivityStore(state => state.isSavingActivity);
  const savingActivityError = useActivityStore(state => state.savingActivityError);
  const clearActivityStoreErrors = useActivityStore(state => state.clearActivityStoreErrors);
  const allActivities = useActivityStore(state => state.allActivities);
  const totalBurnedCalories = useActivityStore(state => state.totalBurnedCalories);
  const isLoadingAllActivities = useActivityStore(state => state.isLoadingAllActivities);
  const getAllActivities = useActivityStore(state => state.getAllActivities);
  const getActivityHighlights = useActivityStore(state => state.getActivityHighlights);
  const getTodaysLedger = useActivityStore(state => state.getTodaysLedger);
  const getRecentActivity = useActivityStore(state => state.getRecentActivity);

  const [selectedType, setSelectedType] = useState(null);
  const [selectedDuration, setSelectedDuration] = useState(null);
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [validationError, setValidationError] = useState(null);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);

  const [showMore, setShowMore] = useState(false);
  const [hasLoadedScreen, setHasLoadedScreen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getAllActivities();
      setHasLoadedScreen(true);
    });
  }, []);

  // Validation function
  const validateData = () => {
    if (!selectedType) {
      setValidationError({
        activityTypeError: "Please select an activity type",
      });
      return false;
    }

    if (!selectedDuration) {
      setValidationError({
        durationError: "Please select a duration",
      });
      return false;
    }

    setValidationError(null);
    return true;
  };

  const handleSaveActivity = async () => {
    // Clear previous errors
    clearActivityStoreErrors();
    setValidationError(null);
    setCurrentOpenDropdown(null);

    // Validate input
    if (!validateData()) {
      return;
    }

    setSelectedType(null);
    setSelectedDuration(null);

    const res = await saveActivity({
      activityType: selectedType,
      durationInMinutes: selectedDuration,
    });

    if (res.success) {
      setShowSuccessAlert(true);
      getRecentActivity();
      getTodaysLedger();
      getActivityHighlights();
    } else {
      setShowErrorAlert(true);
    }
  };
  const scrollToOffset = (y) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: true });
  };

  if (!hasLoadedScreen) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <ScrollView showsVerticalScrollIndicator={false} ref={scrollViewRef} contentContainerStyle={{ flexGrow: 1, paddingBottom: 130 }} refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={() => {
        if (isSavingActivity) return;
        setIsRefreshing(true);
        getAllActivities();
        setIsRefreshing(false);
      }} colors={[Colors.primaryGreen]} isLoadingAllActivities progressViewOffset={24} />}>
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View
            style={{ paddingHorizontal: 8, paddingTop: 8, paddingBottom: 100 }}
          >
            {/* Success Alert */}
            <CustomToast
              error={showSuccessAlert && "Activity saved successfully!"}
              clearErrors={() => setShowSuccessAlert(false)}
              isError={false}
            />

            <View style={styles.header}>
              <Text style={styles.headerText}>Today's Activity</Text>
              <SkeletonItem isLoading={isLoadingAllActivities || isSavingActivity} width="40%" height={32} borderRadius={10} style={{ marginTop: 4 }} duration={1000}>
                <Text style={styles.calorieText}>{`${totalBurnedCalories}/${
                  getMovementCalories(user?.goals?.filter(goal => goal?.goal_type === "movement")[0]?.selected_goal)}`} kcal</Text>
              </SkeletonItem>
            </View>
            <View>
              {isLoadingAllActivities || isSavingActivity ? (
                <View style={{ gap: 25 }}>
                  <SkeletonList items={3} height={120} gap={16} borderRadius={25} />
                  <SkeletonItem width={120} height={32} borderRadius={16} isLoading={isLoadingAllActivities || isSavingActivity} duration={1000} style={{ marginLeft: 16 }} />
                </View>
              ) : allActivities && allActivities.length > 0 ? (
                <View>
                  <FlatList
                    data={!showMore ? allActivities.slice(0, 3) : allActivities}
                    keyExtractor={(item) => item._id}
                    scrollEnabled={false}
                    contentContainerStyle={{ gap: 16 }}
                    renderItem={({ item }) => {
                      return (
                        <ActivityOverviewCard
                          activity={{
                            id: item._id,
                            title: item.activityType,
                            totalCalories: item.burnedCalories,
                            duration: item.durationInMinutes,
                            steps: item.steps
                          }}
                        />
                      );
                    }}
                  />
                  {allActivities.length > 3 && <TouchableOpacity
                    activeOpacity={.5}
                    onPress={() => setShowMore(prev => !prev)}
                    style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 16, marginTop: 16, }}
                    disabled={isLoadingAllActivities || isSavingActivity}
                  >
                    <Text style={[styles.lastLogTimeText, { fontSize: 16, fontFamily: ThemeFonts.Exo_600, color: Colors.primaryPurple, marginRight: 5, }]}>
                      {showMore ? 'Show Less' : 'Show More'}
                    </Text>
                    <Ionicons
                      name={showMore ? "chevron-up" : "chevron-down"}
                      size={16}
                      color={Colors.primaryPurple}
                    />
                  </TouchableOpacity>}
                </View>
              ) : (
                <View style={styles.emptyState}>
                  <Text style={styles.emptyStateText}>No activities recorded yet</Text>
                  <Text style={styles.emptyStateSubText}>Add your first activity below</Text>
                </View>
              )}
            </View>
            {ACTIVITY_TYPES.filter(item => !allActivities.some(activity => activity.activityType === item.value)).length > 0 && (
              <>

                <Text style={[styles.headerText, { margin: 16, marginTop: 36 }]}>
                  Add More
                </Text>
                <View style={{ gap: 36, marginTop: 8 }}>
                  <CustomSelectWithFixedHeightAndLabel
                    options={ACTIVITY_TYPES.filter(item => !allActivities.some(activity => activity.activityType === item.value))}
                    label="Activity Type"
                    selectedValue={selectedType}
                    onValueChange={(value) => setSelectedType(value)}
                    triggerZ={8}
                    listZ={7}
                    currentOpenDropdown={currentOpenDropdown}
                    scrollToOffset={scrollToOffset}
                    dropdownId={1}
                    setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                    isLoading={isSavingActivity || isLoadingAllActivities}
                    isEditing={!isSavingActivity || !isLoadingAllActivities}
                    maxOptionsShown={4}
                    error={validationError?.activityTypeError}
                    clearValidationError={() => {
                      setValidationError((prev) => ({
                        ...prev,
                        activityTypeError: "",
                      }));
                    }}
                  />
                  <CustomSelectWithLabel
                    options={DURATIONS}
                    label="Duration"
                    selectedValue={selectedDuration}
                    onValueChange={(value) => setSelectedDuration(value)}
                    triggerZ={6}
                    listZ={5}
                    currentOpenDropdown={currentOpenDropdown}
                    dropdownId={2}
                    setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                    scrollToOffset={scrollToOffset}
                    isLoading={isSavingActivity || isLoadingAllActivities}
                    isEditing={!isSavingActivity || !isLoadingAllActivities}
                    error={validationError?.durationError}
                    clearValidationError={() => {
                      setValidationError((prev) => ({
                        ...prev,
                        durationError: "",
                      }));
                    }}
                  />
                </View>
                <CustomButton
                  title="Save"
                  onPress={handleSaveActivity}
                  style={{
                    alignSelf: "flex-end",
                    marginTop: 24,
                    width: 96,
                  }}
                  isLoading={isSavingActivity || isLoadingAllActivities}
                />
              </>
            )}
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default EditActivitiesScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
    paddingTop: 8,
  },
  content: {
    padding: 16,
    gap: 20,
  },
  header: {
    marginBottom: 24,
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 32,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.black,
    marginBottom: 8,
  },
  headerText: {
    fontSize: 32,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.black,
  },
  calorieText: {
    fontSize: 25,
    fontFamily: ThemeFonts.Exo_500,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    right: 0,
    padding: 16,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.black,
    textAlign: "center",
    marginBottom: 8,
  },
  emptyStateSubText: {
    fontSize: 14,
    fontFamily: ThemeFonts.Exo_400,
    color: Colors.gray,
    textAlign: "center",
  },
});
