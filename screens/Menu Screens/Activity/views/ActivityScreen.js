import {
  FlatList,
  Image,
  InteractionManager,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import { CustomButton, CustomLoader } from "components/CustomAction";
import { useNavigation, useRoute } from "@react-navigation/native";
import VideoOverviewCard from "components/CustomCards/VideoOverviewCard";
import useActivityStore from "store/activityStore";
import HighlightCard from "components/CustomCards/HighlightCard";
import ActivityLedgerCard from "../components/ActivityLedgerCard";
import { RefreshControl } from "react-native";
import SkeletonItem, { SkeletonList } from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";
import FormattedTime from "components/CustomText/FormattedTime";
import ActivityLineGraph from "components/Charts/LineGraphs/ActivityLineGraph";
import CustomToast from "components/CustomAction/CustomToast";
import { useMoreActivityVideos } from "hooks/Activity/useMoreActivityVideos";
import { screenWidth } from "constants/sizes";


const ActivityScreen = () => {
  const navigation = useNavigation();

  const isLoadingActivityHighlights = useActivityStore(state => state.isLoadingActivityHighlights);
  const isLoadingActivityLedger = useActivityStore(state => state.isLoadingActivityLedger);
  const isLoadingRecentActivity = useActivityStore(state => state.isLoadingRecentActivity);
  const isLoadingRecommendedVideo = useActivityStore(state => state.isLoadingRecommendedVideo);

  const recentActivity = useActivityStore(state => state.recentActivity);

  const activityLedgerData = useActivityStore(state => state.activityLedgerData);
  const activityHighlights = useActivityStore(state => state.activityHighlights);
  const recommendedActivityVideo = useActivityStore(state => state.recommendedActivityVideo);

  const activityHighlightsError = useActivityStore(state => state.activityHighlightsError);
  const activityGraphError = useActivityStore(state => state.activityGraphError);
  const activityHistoryError = useActivityStore(state => state.activityHistoryError);
  const allActivitiesError = useActivityStore(state => state.allActivitiesError);
  const recommendedActivityVideoError = useActivityStore(state => state.recommendedActivityVideoError);
  const activityLedgerError = useActivityStore(state => state.activityLedgerError);
  const savingActivityError = useActivityStore(state => state.savingActivityError);
  const recentActivityError = useActivityStore(state => state.recentActivityError);
  const clearActivityStoreErrors = useActivityStore(state => state.clearActivityStoreErrors);

  const getActivityHighlights = useActivityStore(state => state.getActivityHighlights);
  const getTodaysLedger = useActivityStore(state => state.getTodaysLedger);
  const getRecentActivity = useActivityStore(state => state.getRecentActivity);
  const getRecommendedActivityVideo = useActivityStore(state => state.getRecommendedActivityVideo);
  const getActivityGraph = useActivityStore(state => state.getActivityGraph);

  const { isLoadingMoreActivityVideos, moreActivityVideos, getAllActivityVideos, moreActivityVideosError, setMoreActivityVideosError } = useMoreActivityVideos();

  const [refreshing, setRefreshing] = useState(false);
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getActivityHighlights();
      getTodaysLedger();
      getRecentActivity()
      getRecommendedActivityVideo();
      getActivityGraph();
      setHasLoaded(true);
    })
  }, []);

  if (!hasLoaded) return (
    <AppLayout>
      <View style={styles.loaderContainer}>
        <CustomLoader />
      </View>
    </AppLayout>
  );

  return (
    <AppLayout illustration={true}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ flexGrow: 1, paddingBottom: 10 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              setRefreshing(true);
              getTodaysLedger();
              getActivityHighlights();
              getRecentActivity();
              getRecommendedActivityVideo();
              getActivityGraph();
              getAllActivityVideos();
              setRefreshing(false);
            }}
          />
        }
      >
        <TouchableWithoutFeedback>
          <View style={styles.container}>
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Activity</Text>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginHorizontal: 16,
                  marginTop: 8
                }}
              >
                <SkeletonItem isLoading={isLoadingRecentActivity} width="40%" height={34} borderRadius={8} duration={1000}>
                  <Text style={[styles.subHeading, { maxWidth: '80%' }]} numberOfLines={1} ellipsizeMode="tail">
                    {
                      recentActivity?.activityType || "Not logged yet"
                    }
                  </Text>
                </SkeletonItem>
                <SkeletonItem isLoading={isLoadingRecentActivity} width="30%" height={16} borderRadius={4}>
                  {
                    <FormattedTime style={styles.lastLogTimeText} timestamp={recentActivity?.updatedAt} prefix="Today at" />
                  }
                </SkeletonItem>
              </View>
              <ActivityLedgerCard isLoadingActivityLedger={isLoadingActivityLedger} activityLedgerData={activityLedgerData} />
              <CustomButton
                title="Log Activity"
                style={{ alignSelf: "flex-end", width: 120, marginTop: 16 }}
                onPress={() => {
                  navigation.navigate("Edit Activities");
                }}
                isLoading={isLoadingActivityLedger}
              />
            </View>
            <View style={{ gap: 16 }}>
              <ActivityLineGraph currentOpenDropdown={currentOpenDropdown} setCurrentOpenDropdown={setCurrentOpenDropdown} />
              <HighlightCard highlightData={activityHighlights} isLoading={isLoadingActivityHighlights} />
            </View>

            {/* Exercise Selection */}
            <View style={{ gap: 18, marginHorizontal: 4 }}>
              <View style={{ marginTop: 24, gap: 16 }}>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-end",
                    marginHorizontal: 16,
                  }}
                >
                  <Text style={[styles.subHeading, { marginHorizontal: 8 }]}>
                    Suggested
                  </Text>
                  <Text style={styles.popularText}>Popular</Text>
                </View>
                <SkeletonItem isLoading={isLoadingRecommendedVideo} width="100%" height={200} borderRadius={25}>
                  <VideoOverviewCard video={recommendedActivityVideo} />
                </SkeletonItem>
              </View>

              <View style={{ marginTop: 24 }}>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-end",
                    marginHorizontal: 16,
                  }}
                >
                  <Text style={[styles.headerTitle, { marginHorizontal: 0 }]}>
                    More Workouts
                  </Text>
                </View>

                {
                  isLoadingMoreActivityVideos ? (
                    <View style={{ gap: 24 }}>
                      {
                        Array.from({ length: 3 }).map((_, index) => (
                          <View key={index} style={{ marginTop: 24 }}>
                            <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "flex-end", marginHorizontal: 16, marginBottom: 12 }}>
                              <SkeletonItem width="35%" height={32} borderRadius={25} />
                              <SkeletonItem width="30%" height={32} borderRadius={25} />
                            </View>
                            <SkeletonItem height={(screenWidth - 16) * (9 / 16)} borderRadius={25} />
                          </View>
                        ))
                      }
                    </View>
                  ) : (
                    moreActivityVideos.length == 0 ? (
                      <View style={{ padding: 16 }}>
                        <Text style={styles.noMoreVideosText}>No more videos available</Text>
                      </View>
                    ) : (
                      <FlatList
                        data={moreActivityVideos}
                        keyExtractor={(item, index) => String(index)}
                        scrollEnabled={false}
                        contentContainerStyle={{ gap: 16 }}
                        renderItem={({ item }) => (
                          <View style={{ marginTop: 24 }}>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                alignItems: "flex-end",
                                marginHorizontal: 16,
                                marginBottom: 12,
                              }}
                            >
                              <Text style={styles.subHeading}>{item?.heading}</Text>
                              <Text style={styles.popularText}>Trending</Text>
                            </View>
                            <VideoOverviewCard
                              video={item?.video}
                            />
                          </View>
                        )}
                      />
                    )
                  )
                }
              </View>
            </View>
            <CustomToast
              error={
                activityGraphError ||
                activityHighlightsError ||
                activityHistoryError ||
                allActivitiesError ||
                recommendedActivityVideoError ||
                activityLedgerError ||
                savingActivityError || recentActivityError || moreActivityVideosError
              }
              clearErrors={() => {
                clearActivityStoreErrors();
                setMoreActivityVideosError(null);
              }}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default ActivityScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: "column",
    marginBottom: 80,
    top: 10,
  },
  header: {
    // marginHorizontal: 8,
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
    marginHorizontal: 16,
  },
  subHeading: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
    textTransform: "capitalize",
    textAlign: "left"
  },
  lastLogTimeText: {
    fontSize: 12,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  popularText: {
    backgroundColor: Colors.primaryGreen,
    fontSize: 18,
    color: Colors.white,
    padding: 2,
    paddingHorizontal: 20,
    borderRadius: 50,
    fontFamily: ThemeFonts.Exo_400,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  workoutBgImage: {
    height: 225,
    borderRadius: 25,
  },
  workoutContainer: {
    position: "absolute",
    right: 12,
    left: 12,
    bottom: 12,
    backgroundColor: Colors.veryLightGreen,
    padding: 16,
    borderRadius: 25,
    flexDirection: "row",
    alignItems: "flex-end",
  },
  recommendedTag: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_400,
    backgroundColor: Colors.primaryGreen,
    color: Colors.white,
    paddingHorizontal: 8,
    borderRadius: 10,
    paddingVertical: 2,
  },
  activityMetric: {
    alignItems: "center",
    flex: 1,
  },
  metricValue: {
    fontSize: 24,
    fontFamily: ThemeFonts.Lexend_700,
    color: Colors.white,
  },
  metricLabel: {
    fontSize: 13,
    fontFamily: ThemeFonts.Exo_400,
    color: Colors.white,
    marginTop: 4,
  },
  verticalDivider: {
    width: 1,
    height: 50,
    backgroundColor: Colors.white,
    marginHorizontal: 8,
  },
  noMoreVideosText: {
    fontSize: 16,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
    textAlign: "center",
  }
});