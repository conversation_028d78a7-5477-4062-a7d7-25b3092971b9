import { View } from "react-native";
import { CommonActions, useNavigation } from "@react-navigation/native";
import { CustomAlert, CustomLoader } from "components/CustomAction";
import { AuthContext } from "context/AuthContext";
import { useContext, useEffect, useState } from "react";
import useHealthPermissionStore from "store/healthPermissionStore";
import useMoodStore from "store/moodStore";
import useNotificationStore from "store/notificationStore";
import useNutritionMealRecordStore from "store/nutritionMealRecordStore";
import useSleepStore from "store/sleepStore";
import useUserWeightStore from "store/userWeightStore";

export default LogoutScreen = () => {
    const navigation = useNavigation();
    const { signOut } = useContext(AuthContext);
    const { setNotificationStatus } = useNotificationStore(state => state);
    const [showAlert, setShowAlert] = useState(false);
    const [isLoggingOut, setIsLoggingOut] = useState(false);

    const { resetHealthPermissionStore } = useHealthPermissionStore(state => state);
    const { clearMealRecords } = useNutritionMealRecordStore(state => state);
    const { resetMoodStore } = useMoodStore(state => state);
    const { resetSleepStore } = useSleepStore(state => state);
    const { resetWeightStore } = useUserWeightStore(state => state);

    // Show the alert when component mounts
    useEffect(() => {
        setShowAlert(true);
    }, []);

    const handleLogout = async () => {
        try {
            setIsLoggingOut(true);

            await signOut();
            // Reset all stores regardless of API success
            setNotificationStatus({ isNotificationActive: false });
            resetHealthPermissionStore();
            clearMealRecords();
            resetMoodStore();
            resetSleepStore();
            resetWeightStore();

        } catch (error) {

        } finally {
            setIsLoggingOut(false);
        }
    };

    const handleCancel = () => {
        setShowAlert(false);
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [
                    { name: 'dashboard' }
                ]
            })
        );
    };

    return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            {isLoggingOut && <CustomLoader />}
            <CustomAlert
                visible={showAlert}
                title="Confirm Logout"
                message="Are you sure you want to logout?"
                buttons={[
                    {
                        text: "Cancel",
                        onPress: handleCancel,
                        style: "cancelButton"
                    },
                    {
                        text: "Logout",
                        onPress: () => {
                            setShowAlert(false);
                            handleLogout();
                        },
                        style: "allowButton"
                    }
                ]}
                onClose={handleCancel}
            />
        </View>
    );
};