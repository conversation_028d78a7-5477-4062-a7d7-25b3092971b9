import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Animated,
  FlatList,
  ScrollView,
  RefreshControl,
  InteractionManager,
} from "react-native";
import { CustomLoader, CustomSearch } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenWidth } from "constants/sizes";
import HelpTopicCard from "./components/HelpTopicCard";
import SearchSuggestions from "./components/SearchSuggestions";
import FAQ_HelpService from "services/FAQ_HelpService";
import FlatListBottomLoader from "components/Loaders/FlatListBottomLoader";
import { useNavigation } from "@react-navigation/native";
import CustomToast from "components/CustomAction/CustomToast";
import { TouchableWithoutFeedback } from "react-native";

export default HelpScreen = () => {
  const scrollViewRef = useRef();
  const navigator = useNavigation();

  const [searchQuery, setSearchQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);

  const [page, setPage] = useState(1);
  const [helpData, setHelpData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [helpError, setHelpError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [hasScreenLoaded, setHasScreenLoaded] = useState(false);

  // Fetch help data
  const getHelpData = async () => {
    if (loading || !hasMore) return;
    setLoading(true);

    const response = await FAQ_HelpService.getAllHelps(page);

    if (response.success) {
      if (response.data.length > 0) {
        setHelpData((prevData) => [...prevData, ...response.data]);
        setPage((prevPage) => prevPage + 1);
      } else {
        setHasMore(false);
      }
    } else {
      setHelpError(response.error);
      setHasMore(false);
    }
    setLoading(false);
  };

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getHelpData();
      setHasScreenLoaded(true);
    });
  }, []);

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      getHelpData();
    }
  };

  const handleCategorySelect = (category) => {
    navigator.navigate("Help Details", { id: category });
  };

  const handleSearch = async (text) => {
    setSearchQuery(text);
  };

  useEffect(() => {
    if (searchQuery.length === 0) {
      setSuggestions([]);
      return;
    }
    let isActive = true;
    (async () => {
      setSuggestions([]);
      if (searchQuery.length > 0) {
        const response = await FAQ_HelpService.getAllHelps(undefined, searchQuery)
        if (isActive && response.success) {
          setSuggestions(response.data);
        }
        else if (isActive && !response.success) {
          setHelpError(response.error);
        }
      }
    })();

    return () => {
      isActive = false;
    };
  }, [searchQuery]);

  const onRefresh = async () => {
    setLoading(true);
    setPage(1);
    setHasMore(true);
    setHelpData([]);
    setRefreshing(true);

    const response = await FAQ_HelpService.getAllHelps(1);

    if (response.success) {
      setHelpData(response.data);
      setPage(2);
      setHasMore(response.data.length > 0);
    } else {
      setHelpError(response.error);
      setHasMore(false);
    }

    setLoading(false);
    setRefreshing(false);
  };

  if (!hasScreenLoaded) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout illustration={false}>
      <ScrollView
        ref={scrollViewRef}
        style={styles.container}
        contentContainerStyle={[
          { flexGrow: 1 },
        ]}
        showsVerticalScrollIndicator={false}
        onScroll={(e) => {
          let paddingToBottom = 10;
          paddingToBottom += e.nativeEvent.layoutMeasurement.height;
          if (
            e.nativeEvent.contentOffset.y >=
            e.nativeEvent.contentSize.height - paddingToBottom
          ) {
            handleLoadMore();
          }
        }}

        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.primaryGreen]}
            progressViewOffset={24}
          />
        }
      >
        <TouchableWithoutFeedback>
          <View style={{ flex: 1, paddingBottom: 90 }}>
            <CustomToast
              error={helpError}
              clearErrors={() => {
                setHelpError(null);
              }}
              position="top"
            />
            <Text style={styles.headerText}>Hi! What do you need help with?</Text>
            {/* Search Section */}
            <View style={styles.searchSection}>
              <CustomSearch
                value={searchQuery}
                onChangeText={handleSearch}
                placeholder="Search For Options"
              />
              {suggestions?.length > 0 && (
                <View style={styles.suggestionsContainer}>
                  {suggestions.map((suggestion, index) => (
                    <SearchSuggestions
                      key={index}
                      suggestion={suggestion}
                      setSearchQuery={setSearchQuery}
                      setSuggestions={setSuggestions}
                      handleCategorySelect={handleCategorySelect}
                      isLastChild={index === suggestions.length - 1}
                    />
                  ))}
                </View>
              )}
            </View>
            {/* Categories and Topics Container */}
            <View style={styles.mainContainer}>
              {/* Categories */}
              <Animated.View
                style={[styles.topicsContainer]}
              >
                <View style={{ width: screenWidth, paddingHorizontal: 20 }}>
                  <FlatList
                    data={helpData}
                    keyExtractor={(item) => item.id.toString()}
                    scrollEnabled={false}
                    renderItem={({ item }) => (
                      <HelpTopicCard
                        category={item}
                        handleCategorySelect={handleCategorySelect}
                      />
                    )}
                    contentContainerStyle={{ gap: 15 }}
                    ListFooterComponent={loading ? <FlatListBottomLoader /> : null}
                  />
                </View>
              </Animated.View>
              {/* )} */}
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth,
    left: -20,
    backgroundColor: Colors.white,
    // padding: 20,
    borderRadius: 10,
    // overflow: 'hidden'
  },
  headerText: {
    fontSize: 32,
    top: -15,
    color: Colors.black,
    textAlign: "start",
    marginHorizontal: 24,
    marginVertical: 10,
    fontFamily: "Exo_700Bold",
    padding: 20,
    paddingBottom: 0,
  },
  searchSection: {
    position: "relative",
    marginBottom: 20,
    marginHorizontal: 20,
  },
  suggestionsContainer: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: Colors.white,
    borderRadius: 10,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: Colors.primaryPurple,
  },
  mainContainer: {
    position: "relative",
  },
  categoriesContainer: {
    marginBottom: 10,
  },
  topicsContainer: {
    // position: 'absolute',
    // top: -25,
    flexDirection: "row",
    left: 0,
    right: 0,
    backgroundColor: Colors.white,
    // paddingHorizontal: 20,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});
