import React, { useEffect, useRef, useState } from "react";
import { View, Text, StyleSheet, Animated, FlatList, ScrollView, RefreshControl, TouchableWithoutFeedback, InteractionManager } from "react-native";
import { CustomAlert, CustomLoader, CustomSearch } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenWidth } from "constants/sizes";
import SearchSuggestions from "./components/SearchSuggestions";
import FAQ_HelpService from "services/FAQ_HelpService";
import { useNavigation, useRoute } from "@react-navigation/native";
import CategoryTopicCard from "./components/CategoryTopicCard";
import CustomToast from "components/CustomAction/CustomToast";

export const HelpScreenDetails = () => {
  const scrollViewRef = useRef();
  const route = useRoute();
  const navigator = useNavigation();

  const [loading, setLoading] = useState(true);

  const [searchQuery, setSearchQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [currentTopicId, setCurrentTopicId] = useState(null);
  const [categoryData, setCategoryData] = useState();
  const [refreshing, setRefreshing] = useState(false);

  const [helpError, setHelpError] = useState(null);
  const [hasScreenLoaded, setHasScreenLoaded] = useState(false);

  const handleCategorySelect = (category) => {
    navigator.navigate("Help Details", { id: category });
  };

  const handleSearch = async (text) => {
    setSearchQuery(text);
  };

  useEffect(() => {
    if (searchQuery.length === 0) {
      setSuggestions([]);
      return;
    }

    let isActive = true;
    (async () => {
      setSuggestions([]);
      if (searchQuery.length > 0) {
        const response = await FAQ_HelpService.getAllHelps(undefined, searchQuery)
        if (isActive && response.success) {
          setSuggestions(response.data);
        }
        else if (isActive && !response.success) {
          setHelpError(response.error);
        }
      }
    })();

    return () => {
      isActive = false;
    };
  }, [searchQuery]);


  const getCategoryDetails = async () => {
    setLoading(true);
    const response = await FAQ_HelpService.getHelp(route?.params?.id);

    if (!response.success) {
      setHelpError("Error getting help details.");
      setLoading(false);
      return;
    }
    setCategoryData(response.data);
    setLoading(false);
  }

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      getCategoryDetails();
      setHasScreenLoaded(true);
    });
  }, [route?.params?.id]);

  if (loading || !hasScreenLoaded) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }
  return (
    <AppLayout illustration={false}>
      <ScrollView
        ref={scrollViewRef}
        style={styles.container}
        contentContainerStyle={[{ flexGrow: 1 }]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              setRefreshing(true);
              getCategoryDetails();
              setRefreshing(false);
            }}
            colors={[Colors.primaryGreen]}
            progressViewOffset={24}
          />
        }
      >
        <TouchableWithoutFeedback>
          <View style={{ flex: 1, paddingBottom: 90 }}>
            <Text style={styles.headerText}>Hi! What do you need help with?</Text>
            {/* Search Section */}
            <View style={styles.searchSection}>
              <CustomSearch
                value={searchQuery}
                onChangeText={handleSearch}
                placeholder="Search For Options"
              />
              {suggestions.length > 0 && (
                <View style={styles.suggestionsContainer}>
                  {suggestions.map((suggestion, index) => (
                    <SearchSuggestions
                      key={index}
                      suggestion={suggestion}
                      setSearchQuery={setSearchQuery}
                      setSuggestions={setSuggestions}
                      handleCategorySelect={handleCategorySelect}
                      isLastChild={index === suggestions.length - 1}
                    />
                  ))}
                </View>
              )}
            </View>
            {/* Categories and Topics Container */}
            <View style={styles.mainContainer}>
              {/* Categories */}
              <Animated.View style={[styles.topicsContainer]}>
                <View
                  style={{
                    width: screenWidth,
                    paddingHorizontal: 20,
                  }}
                >
                  <FlatList
                    data={categoryData?.topics}
                    keyExtractor={(item, index) => index}
                    scrollEnabled={false}
                    renderItem={({ item, index }) => {
                      return (
                        <CategoryTopicCard
                          topic={item}
                          currentTopicId={currentTopicId}
                          setCurrentTopicId={(ind) => setCurrentTopicId(ind)}
                          index={index}
                        />
                      );
                    }}
                  />
                </View>
              </Animated.View>
              <CustomToast error={helpError} clearErrors={() => setHelpError(null)} position="top" />
              {/* )} */}
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth,
    left: -20,
    backgroundColor: Colors.white,
    // padding: 20,
    borderRadius: 10,
    // overflow: 'hidden'
  },
  headerText: {
    fontSize: 32,
    top: -15,
    color: Colors.black,
    textAlign: "start",
    marginHorizontal: 24,
    marginVertical: 10,
    fontFamily: "Exo_700Bold",
    padding: 20,
    paddingBottom: 0,
  },
  searchSection: {
    position: "relative",
    marginBottom: 20,
    marginHorizontal: 20,
  },
  suggestionsContainer: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: Colors.white,
    borderRadius: 10,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: Colors.primaryPurple,
  },
  mainContainer: {
    position: "relative",
  },
  categoriesContainer: {
    marginBottom: 10,
  },
  topicsContainer: {
    // position: 'absolute',
    // top: -25,
    flexDirection: "row",
    left: 0,
    right: 0,
    backgroundColor: Colors.white,
    // paddingHorizontal: 20,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});
