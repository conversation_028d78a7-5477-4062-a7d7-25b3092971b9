import { Pressable, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { Colors } from 'constants/theme/colors'
import Icon from 'react-native-vector-icons/Ionicons';
import Animated from 'react-native-reanimated';

const CategoryTopicCard = ({ topic, currentTopicId, setCurrentTopicId, index }) => {
    return (
        <Pressable
            style={styles.topicCard}
            onPress={() => {
                setCurrentTopicId((prevId) => prevId == index ? null : index);
            }}
        >
            <View style={{ flex: 1 }}>
                <Text style={styles.topicTitle}>{topic.title}</Text>
                <Text numberOfLines={index == currentTopicId ? undefined : 1} ellipsizeMode="tail" style={[
                    styles.topicDescription,
                ]}>{topic.description}</Text>
            </View>
            <Animated.View style={{ transform: [{ rotate: currentTopicId == index ? '180deg' : '0deg' }] }}>
                <Icon name="chevron-down" size={20} color={Colors.primaryGreen} />
            </Animated.View>
        </Pressable>
    )
}

export default CategoryTopicCard

const styles = StyleSheet.create({
    topicCard: {
        backgroundColor: Colors.backgroundLight,
        padding: 15,
        borderRadius: 12,
        marginBottom: 10,
        flexDirection: 'row',
        alignItems: 'flex-start',
    },
    topicContent: {
        flex: 1,
    },
    topicTitle: {
        fontSize: 16,
        fontFamily: 'Exo_700Bold',
        color: Colors.textDark,
        marginBottom: 4,
    },
    topicDescription: {
        fontSize: 14,
        color: Colors.textLight,
        fontFamily: 'Exo_400Regular',
    },
})