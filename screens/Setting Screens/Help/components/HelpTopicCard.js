import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { Colors } from 'constants/theme/colors'

const HelpTopicCard = ({ category, handleCategorySelect }) => {

    return (
        <TouchableOpacity
            activeOpacity={.8}
            key={category.id}
            style={styles.categoryCard}
            onPress={() => handleCategorySelect(category.id)}
        >
            <View style={styles.categoryContent}>
                <Text style={styles.categoryTitle}>{category.category}</Text>
                <Text style={styles.suggestionText}>See all {category.topics.length} articles</Text>
            </View>
        </TouchableOpacity>
    )
}

export default HelpTopicCard

const styles = StyleSheet.create({
    categoryCard: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.lightGreen,
        padding: 15,
        borderRadius: 25,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    categoryContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    categoryTitle: {
        fontSize: 18,
        fontFamily: 'Exo_700Bold',
        color: Colors.primaryGreen,
        marginBottom: 4,
        textTransform: "capitalize",
        textAlign: "center"
    },
    suggestionText: {
        marginHorizontal: 10,
        fontSize: 12,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
        textTransform: "capitalize",
        textAlign: "center"
    },
})