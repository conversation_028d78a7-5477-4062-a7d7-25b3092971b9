import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect } from 'react'
import { Colors } from 'constants/theme/colors';

const SearchSuggestions = ({ suggestion, setSearchQuery, setSuggestions, handleCategorySelect, isLastChild }) => {
    return (
        <TouchableOpacity
            key={suggestion.id}
            activeOpacity={.6}
            style={[styles.suggestionItem, isLastChild ? {
                borderBottomWidth: 0
            } : {
                borderBottomWidth: 1,
            }]}
            onPress={() => {
                setSearchQuery('');
                setSuggestions([]);
                handleCategorySelect(suggestion.id);
            }}
        >
            <Text style={styles.suggestionText}>{suggestion.category}</Text>
        </TouchableOpacity>
    )
}

export default SearchSuggestions

const styles = StyleSheet.create({
    suggestionItem: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 15,
        borderBottomColor: Colors.border,
    },
    suggestionText: {
        marginHorizontal: 10,
        fontSize: 14,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
        textTransform: "capitalize",
        textAlign: "left"
    },
})