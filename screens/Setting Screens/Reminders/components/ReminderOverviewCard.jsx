import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { CustomButton } from 'components/CustomAction'
import { Colors } from 'constants/theme/colors'
import { useNavigation } from '@react-navigation/native';
import { ThemeFonts } from 'constants/theme/fonts';

const ReminderOverviewCard = ({ reminder, isActive }) => {
    const navigation = useNavigation();

    return (
        <View key={reminder.id} style={[styles.reminderCard, { opacity: isActive ? 1 : 0.7 }]}>
            <View style={styles.reminderContent}>
                <View style={styles.reminderHeader}>
                    <Text style={styles.reminderTitle}>{reminder.label}</Text>
                    <Text style={styles.reminderTime}>
                        {new Date(reminder.time).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true,
                        })}
                    </Text>
                </View>
                <CustomButton
                    title="Edit"
                    onPress={isActive ? () => navigation.navigate("Edit Reminder", { id: reminder.id }) : () => { }}
                    style={styles.editButton}
                    fontFamily="Exo_600SemiBold"
                />
            </View>
        </View>
    )
}

export default ReminderOverviewCard

const styles = StyleSheet.create({
    reminderCard: {
        backgroundColor: Colors.primaryGreen,
        borderRadius: 24,
        padding: 18,
        paddingHorizontal: 25,
        // marginVertical: 16,
        // marginBottom: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    reminderContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
    },
    reminderHeader: {
        flex: 1,
    },
    reminderTitle: {
        fontSize: 19,
        color: Colors.lightGreen,
        fontFamily: 'Exo_700Bold',
        marginBottom: 2,
        textTransform: "capitalize"
    },
    reminderTime: {
        fontSize: 30,
        fontFamily: ThemeFonts.Lexend_400,
        color: Colors.white,
    },
    editButton: {
        // marginLeft: 10,
        maxWidth: 72
    },
})