import React, { useState, useCallback, useEffect } from "react";
import { View, Text, StyleSheet, TouchableOpacity, Linking, TextInput, ScrollView, TouchableWithoutFeedback, InteractionManager } from "react-native";
import {
  CustomAlert,
  CustomButton,
  CustomLoader,
  CustomSelect,
} from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import Icon from "react-native-vector-icons/Ionicons";
import * as DocumentPicker from "expo-document-picker";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenWidth } from "constants/sizes";
import contactUsService from "services/contactUsService";
import { ThemeFonts } from "constants/theme/fonts";
import * as Progress from 'react-native-progress';

const QUERY_CATEGORIES = ["Application", "Account", "Device", "Feedback"];

const MAX_ATTACHMENTS = 3;

export const ContactScreen = () => {
  // Form state
  const [formState, setFormState] = useState({
    selectedCategory: "",
    message: "",
    attachments: [],
  });

  // UI state
  const [showAlert, setShowAlert] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [errors, setErrors] = useState({
    category: "",
    message: "",
    attachments: "",
  });

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [hasScreenLoaded, setHasScreenLoaded] = useState(false);

  // Helper to update form state
  const updateFormState = useCallback(
    (field, value) => {
      setFormState((prev) => ({ ...prev, [field]: value }));

      // Clear error for this field when user makes changes
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: "" }));
      }
    },
    [errors]
  );

  const handleAttachment = async () => {
    setCurrentOpenDropdown(null);
    try {
      // Don't allow more than MAX_ATTACHMENTS
      if (formState.attachments.length >= MAX_ATTACHMENTS) {
        setErrors((prev) => ({
          ...prev,
          attachments: `You can attach up to ${MAX_ATTACHMENTS} files only.`,
        }));
        return;
      }

      const result = await DocumentPicker.getDocumentAsync({
        type: ["image/*"],
        copyToCacheDirectory: true,
        multiple: true,
      });

      // Handle result
      if (!result.canceled) {
        // Some versions may return `assets` as a single object instead of an array
        const newFiles = Array.isArray(result.assets)
          ? result.assets
          : result.assets
            ? [result.assets]
            : [];

        // Check if adding these files would exceed the limit
        if (formState.attachments.length + newFiles.length > MAX_ATTACHMENTS) {
          setErrors((prev) => ({
            ...prev,
            attachments: `You can attach up to ${MAX_ATTACHMENTS} files only.`,
          }));

          // Add only enough files to reach the limit
          const availableSlots = MAX_ATTACHMENTS - formState.attachments.length;
          if (availableSlots > 0) {
            updateFormState("attachments", [
              ...formState.attachments,
              ...newFiles.slice(0, availableSlots),
            ]);
          }
        } else {
          updateFormState("attachments", [
            ...formState.attachments,
            ...newFiles,
          ]);
          // Clear any attachment errors
          setErrors((prev) => ({ ...prev, attachments: "" }));
        }
      }
    } catch (error) {
      console.error("Error picking document:", error);
      setErrors((prev) => ({
        ...prev,
        attachments: "Failed to select document. Please try again.",
      }));
    }
  };

  const validateForm = () => {
    setErrors({
      category: "",
      message: "",
      attachments: "",
    });

    const newErrors = {};
    let isValid = true;

    if (!formState.selectedCategory) {
      newErrors.category = "Please choose category";
      isValid = false;
    }

    if (!formState.message) {
      newErrors.message = "Please enter your message";
      isValid = false;
    }

    setErrors((prev) => ({ ...prev, ...newErrors }));
    return isValid;
  };

  const handleDeleteAttachment = (uriToDelete) => {
    updateFormState(
      "attachments",
      formState.attachments.filter((file) => file.uri !== uriToDelete)
    );

    // Clear attachment errors when removing files
    if (errors.attachments) {
      setErrors((prev) => ({ ...prev, attachments: "" }));
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    const formData = new FormData();

    // Text fields expected in the DTO
    formData.append("category", formState.selectedCategory.toLowerCase());
    formData.append("description", formState.message);

    // Files - match 'attachments' field expected by FilesInterceptor
    formState.attachments.forEach((file, index) => {
      formData.append("attachments", {
        uri: file.uri,
        name: file.name || `file_${index}.${file.uri.split(".").pop()}`, // Add proper extension
        type: file.type || file.mimeType || "application/octet-stream",
      });
    });

    setLoading(true);
    const result = await contactUsService.sendQuery(formData);

    if (result.success) {
      setCurrentProgess(1);
      setTimeout(() => {
        setLoading(false);
        setShowAlert(true);
        setFormState({
          selectedCategory: "",
          message: "",
          attachments: [],
        });
        setCurrentProgess(0);
      }, 400);
    } else {
      setCurrentProgess(1);
      setTimeout(() => {
        setLoading(false);
        setErrors((prev) => ({
          ...prev,
          form:
            result?.error || "Failed to send your query. Please try again.",
        }));
        setCurrentProgess(0);
      }, 400);
    }
  };

  const renderAttachments = () => {
    if (formState.attachments.length === 0) return null;

    return formState.attachments.map((file) => (
      <View key={file.uri} style={styles.attachedFileContainer}>
        <View style={styles.attachedFileContent}>
          <Icon
            name={
              file.mimeType?.startsWith("image/")
                ? "image-outline"
                : "document-text-outline"
            }
            size={16}
            color={Colors.primaryPurple}
          />
          <View style={styles.fileInfoContainer}>
            <Text
              style={styles.attachedFileName}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {file.name}
            </Text>
          </View>
          <TouchableOpacity onPress={() => handleDeleteAttachment(file.uri)} disabled={isLoading}>
            <Icon name="trash-outline" size={16} color={Colors.primaryPurple} />
          </TouchableOpacity>
        </View>
      </View>
    ));
  };

  const [currentProgess, setCurrentProgess] = useState(0);

  useEffect(() => {
    if (isLoading) {
      setCurrentProgess(0);
      const interval = setInterval(() => {
        setCurrentProgess((prev) => {
          const increment = Math.random() * 0.03 + 0.005;

          if (prev >= 0.9) {
            clearInterval(interval);
            return 0.9;
          }

          return prev + (prev > 0.7 ? increment / 2 : increment);
        });
      }, 150);

      return () => clearInterval(interval);
    }
  }, [isLoading]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      setHasScreenLoaded(true);
    });
  }, []);

  if (!hasScreenLoaded) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout illustration={false}>
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        contentContainerStyle={{ paddingBottom: 25, flexGrow: 1 }}
      >
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View style={{ flex: 1 }}>
            <View>
              <Text style={styles.headerText}>Contact Us</Text>
              <Text style={styles.subHeading}>Write to us here.</Text>

              <View style={styles.section}>
                <CustomSelect
                  options={QUERY_CATEGORIES.map((category) => ({
                    label: category,
                    value: category,
                  }))}
                  selectedValue={formState.selectedCategory}
                  onValueChange={(value) =>
                    updateFormState("selectedCategory", value)
                  }
                  label="Query Category"
                  dropdownId={1}
                  currentOpenDropdown={currentOpenDropdown}
                  setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                  clearValidationError={() => {
                    setErrors((prev) => ({ ...prev, category: "" }));
                  }}
                  disabled={isLoading}
                />
                {errors.category ? (
                  <Text style={styles.errorText}>{errors.category}</Text>
                ) : null}
              </View>

              <View style={styles.section}>
                <TextInput
                  value={formState.message}
                  onChangeText={(text) => updateFormState("message", text)}
                  placeholder="Type your message..."
                  multiline
                  numberOfLines={10}
                  style={[styles.messageInput, {
                    fontFamily: formState?.message ? ThemeFonts.Exo_400 : ThemeFonts.Exo_400_Italic
                  }]}
                  placeholderTextColor={Colors.black}
                  onPressIn={() => {
                    setErrors((prev) => ({ ...prev, message: "" }));
                  }}
                  editable={!isLoading}
                />
                {errors.message ? (
                  <Text style={styles.errorText}>{errors.message}</Text>
                ) : null}
              </View>

              <View style={styles.section}>
                {renderAttachments()}

                <TouchableOpacity
                  style={[
                    styles.attachmentButton,
                    formState.attachments.length > 0 &&
                    styles.smallAttachmentButton && {
                      height: "auto",
                      justifyContent: "flex-start",
                      alignItems: "center",
                      borderRadius: 10,
                      borderWidth: 2,
                      // padding: 5,
                      paddingVertical: 2,
                      paddingHorizontal: 4,
                    },
                  ]}
                  onPress={handleAttachment}
                  disabled={isLoading}
                >
                  <Icon
                    name="add-circle-outline"
                    size={20}
                    color={Colors.black}
                  />
                  <Text
                    style={styles.attachmentButtonText}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                  >
                    Add Attachments
                  </Text>
                </TouchableOpacity>

                {errors.attachments ? (
                  <Text style={styles.errorText}>{errors.attachments}</Text>
                ) : null}
                {errors.form ? (
                  <Text style={styles.errorText}>{errors.form}</Text>
                ) : null}
              </View>

              {
                isLoading ? (
                  <View>
                    <Progress.Bar progress={currentProgess} width={screenWidth - 40} height={16} color={Colors.primaryGreen} style={{
                      marginVertical: 20,
                      marginBottom: 10,
                      borderRadius: 10,
                      alignSelf: "center"
                    }} />
                    <Text style={{ color: Colors.black, fontFamily: ThemeFonts.Exo_500, fontSize: 12, marginHorizontal: 8 }}  >Sending query..</Text>
                  </View>
                ) : (

                  <CustomButton
                    title="Send"
                    // onPress={() => setShowAlert(true)}
                    onPress={handleSubmit}
                    style={styles.submitButton}
                    disabled={isLoading}
                  />
                )
              }

            </View>

            <View style={styles.directContact}>
              <TouchableOpacity
                onPress={() => Linking.openURL("tel:+91-4556783456")}
                style={styles.callButton}
              >
                <Icon name="call" size={24} color={Colors.black} />
                <Text style={styles.callText}>Call Us</Text>
                <Text style={styles.phoneNumber}>+91-4556783456</Text>
              </TouchableOpacity>
            </View>
            <CustomAlert
              visible={showAlert}
              title="Your Query has been sent!"
              ionIcon="checkmark-circle-outline"
              message="Our Agency will get back to you through email."
              buttons={[
                {
                  text: "OK",
                  onPress: () => setShowAlert(false),
                  style: "allowButton",
                },
              ]}
              onClose={() => setShowAlert(false)}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: screenWidth,
    left: -20,
    backgroundColor: Colors.white,
    padding: 20,
    borderRadius: 10,
    zIndex: 100,
  },
  headerText: {
    fontSize: 32,
    top: -15,
    color: Colors.black,
    textAlign: "start",
    marginHorizontal: 24,
    marginVertical: 10,
    fontFamily: "Exo_700Bold",
  },
  subHeading: {
    fontSize: 14,
    color: Colors.black,
    textAlign: "start",
    fontFamily: "Exo_500Medium",
    marginHorizontal: 24,
    marginTop: -25,
    marginBottom: 10,
  },
  section: {
    marginBottom: 5,
    borderRadius: 25,
    marginVertical: 5,
  },
  messageInput: {
    height: 100,
    textAlignVertical: "top",
    borderColor: Colors.primaryPurple,
    borderWidth: 2,
    borderRadius: 20,
    paddingVertical: 5,
    paddingHorizontal: 23,
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_400,
    color: Colors.black,
  },
  attachmentButton: {
    height: 140,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.white,
    // padding: 5,
    paddingHorizontal: 10,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: Colors.primaryPurple,
  },
  smallAttachmentButton: {
    height: 30,
    justifyContent: "flex-start",
    borderRadius: 20,
    borderWidth: 2,
  },
  attachmentButtonText: {
    marginLeft: 5,
    color: Colors.black,
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_400,
  },
  attachedFileContainer: {
    width: "100%",
    marginBottom: 5,
  },
  attachedFileContent: {
    flexDirection: "row",
    alignItems: "center",
    // padding: 5,
    paddingHorizontal: 4,
    paddingVertical: 6,
    borderWidth: 2,
    borderColor: Colors.primaryPurple,
    borderRadius: 10,
    backgroundColor: Colors.gray,
  },
  fileInfoContainer: {
    flex: 1,
    marginHorizontal: 10,
  },
  attachedFileName: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.black,
    marginLeft: -1,
  },
  submitButton: {
    marginVertical: 20,
    left: screenWidth / 1.72,
    backgroundColor: Colors.primaryPurple,
    borderRadius: 25,
    alignItems: "center",
  },
  directContact: {
    flex: 1,
    marginTop: 20,
    marginBottom: 54,
    justifyContent: "flex-end",
  },
  callButton: {
    backgroundColor: Colors.lightGreen,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    padding: 8,
    borderRadius: 25,
  },
  callText: {
    fontSize: 16,
    fontFamily: "Exo_500Medium",
    color: Colors.black,
  },
  phoneNumber: {
    fontSize: 16,
    color: "steelblue",
    fontFamily: ThemeFonts.Lexend_400,
    textDecorationLine: "underline",
  },
  errorText: {
    color: Colors.red,
    fontSize: 12,
    marginTop: 5,
    marginHorizontal: 24,
    fontFamily: ThemeFonts.Exo_500,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});
