import { Pressable, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import Icon from 'react-native-vector-icons/Ionicons';
import { Colors } from 'constants/theme/colors';

const FAQCard = ({ index, id, question, answer, expandedQuestion, setExpandedQuestion }) => {
    return (
        <Pressable
            style={styles.questionContainer}
            onPress={() => setExpandedQuestion(expandedQuestion === index ? null : index)}
        >
            <View style={styles.questionHeader}>
                <Text style={styles.questionNumber}>Q{index + 1}</Text>
                <Text style={styles.questionText}>{question}</Text>
                <Icon
                    name={expandedQuestion === index ? 'chevron-up' : 'chevron-down'}
                    size={24}
                    color={Colors.primaryPurple}
                />
            </View>

            {expandedQuestion === index && (
                <View style={styles.answerContainer}>
                    <Text style={styles.answerText}>{answer}</Text>
                </View>
            )}
        </Pressable>
    )
}

export default FAQCard

const styles = StyleSheet.create({
    questionContainer: {
        marginBottom: 15,
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        backgroundColor: Colors.white,
        borderBottomWidth: 1.5,
        borderBottomColor: Colors.gray,
        borderStyle: 'dashed',
        width: '100%',
    },
    questionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 15,
        backgroundColor: Colors.backgroundLight,
        width: '100%',
    },
    questionNumber: {
        fontSize: 18,
        fontFamily: 'Exo_700Bold',
        color: Colors.textDark,
        marginRight: 5,
    },
    questionText: {
        fontSize: 16,
        fontFamily: 'Exo_400Regular',
        color: Colors.textDark,
        flex: 1,
    },
    answerContainer: {
        width: '100%',
        paddingHorizontal: 18,
        paddingVertical: 10,
    },
    answerText: {
        fontSize: 14,
        color: Colors.black,
        textAlign: 'start',
        lineHeight: 20,
        fontFamily: 'Exo_400Regular',
    },
})