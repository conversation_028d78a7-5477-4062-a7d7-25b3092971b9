import React, { useState, useEffect } from "react";
import {
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  RefreshControl,
  View,
  TouchableWithoutFeedback,
  InteractionManager,
} from "react-native";
import { CustomInput, CustomLoader, CustomSearch } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import AppLayout from "navigations/components/Layouts/AppLayout";
import FAQ_HelpService from "services/FAQ_HelpService";
import FAQCard from "./components/FAQCard";
import FlatListBottomLoader from "components/Loaders/FlatListBottomLoader";
import CustomToast from "components/CustomAction/CustomToast";

export const FAQScreen = () => {
  const [searchQuery, setSearchQuery] = useState("");

  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [FAQs, setFAQs] = useState([]);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  const [expandedQuestion, setExpandedQuestion] = useState(null);
  const [hasScreenLoaded, setHasScreenLoaded] = useState(false);

  useEffect(() => {
    let isMounted = true;


    InteractionManager.runAfterInteractions(() => {
      setLoading(true);
      setPage(1);

      const fetchData = async () => {
        const response = await FAQ_HelpService.getAllFAQs({
          page: 1,
          searchQuery,
        });

        if (!isMounted) return;

        if (response.success) {
          setFAQs(response.data);
          setPage(2);
          setHasMore(response.data.length > 0);
        }
        else {
          setFAQs([]);
          setHasMore(false);
          setError(response.error);
        }

        setLoading(false);
      };

      fetchData();
      setHasScreenLoaded(true);
    });

    return () => {
      isMounted = false;
    };
  }, [searchQuery]);

  const getFAQs = async () => {
    if (loading || !hasMore) return;
    setLoading(true);

    try {
      const response = await FAQ_HelpService.getAllFAQs({
        page: page,
        searchQuery,
      });


      if (response.success) {
        if (response.data.length > 0) {
          setFAQs((prevData) => [...prevData, ...response.data]);
          setPage((prevPage) => prevPage + 1);
        } else {
          setHasMore(false);
        }
      }
      else {
        setHasMore(false);
        setError(response.error);
      }
    } catch (error) {
      setError(error);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      getFAQs();
    }
  };

  const onRefresh = async () => {
    setLoading(true);
    setPage(1);
    setHasMore(true);
    setFAQs([]);

    const response = await FAQ_HelpService.getAllFAQs({
      page: 1,
      searchQuery,
    });

    if (response.success) {
      setFAQs(response.data);
      setPage(2);
      setHasMore(response.data.length > 0);
    } else {
      setError(response.error);
      setHasMore(false);
    }

    setLoading(false);
  };

  if (!hasScreenLoaded) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout illustration={false}>
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        contentContainerStyle={[
          { flexGrow: 1 },
        ]}
        onScroll={(e) => {
          let paddingToBottom = 90;
          paddingToBottom += e.nativeEvent.layoutMeasurement.height;
          if (
            e.nativeEvent.contentOffset.y >=
            e.nativeEvent.contentSize.height - paddingToBottom
          ) {
            handleLoadMore();
          }
        }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              setRefreshing(true);
              onRefresh();
              setRefreshing(false);
            }}
            colors={[Colors.primaryGreen]}
            progressViewOffset={24}
          />
        }
      >
        <TouchableWithoutFeedback>
          <View style={[
            { flex: 1 },
            hasMore ? { paddingBottom: 150 } : { paddingBottom: 90 },
          ]}>
            <Text style={styles.headerText}>FAQs</Text>

            <CustomSearch
              value={searchQuery}
              onChangeText={(value) => setSearchQuery(value)}
              placeholder="Search For FAQs"
            />

            {(FAQs.length === 0 && !loading) ? (
              <Text
                style={{
                  textAlign: "center",
                  marginTop: 20,
                  color: Colors.black,
                  fontSize: 16,
                  fontFamily: "Exo_400Regular",
                }}
              >
                No FAQs Found
              </Text>
            ) : (
              <FlatList
                data={FAQs}
                keyExtractor={(item) => item.id.toString()}
                scrollEnabled={false}
                renderItem={({ item, index }) => (
                  <FAQCard
                    index={index}
                    id={item.id}
                    question={item.question}
                    answer={item.answer}
                    expandedQuestion={expandedQuestion}
                    setExpandedQuestion={setExpandedQuestion}
                  />
                )}
                ListFooterComponent={
                  loading && hasMore ? <FlatListBottomLoader /> : null
                }
              />
            )}
            <CustomToast error={error} clearErrors={() => setError(null)} />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    paddingVertical: 20,
    borderRadius: 15,
  },
  headerText: {
    fontSize: 32,
    color: Colors.black,
    textAlign: "start",
    marginHorizontal: 24,
    top: -10,
    marginBottom: 20,
    fontFamily: "Exo_700Bold",
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});

export default FAQScreen;
