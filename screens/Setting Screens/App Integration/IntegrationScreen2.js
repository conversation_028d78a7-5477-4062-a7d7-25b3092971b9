import { Platform, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import IosIntegrationScreen from './IosIntegrationScreen'
import AndroidIntegrationScreen from './AndroidIntegrationScreen'
import AppLayout from 'navigations/components/Layouts/AppLayout'

const IntegrationScreen = () => {
    return (
        <AppLayout>
            {
                Platform.OS === 'ios' ? <IosIntegrationScreen /> : <AndroidIntegrationScreen />
            }
        </AppLayout>
    )
}

export default IntegrationScreen

const styles = StyleSheet.create({})