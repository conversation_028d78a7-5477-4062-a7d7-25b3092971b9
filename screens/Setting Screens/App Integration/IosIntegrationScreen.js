import React, { useState, useEffect, useCallback } from "react";
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    AppState,
    Platform,
} from "react-native";
import { Colors } from "constants/theme/colors";
import CustomAlert from "components/CustomAction/CustomAlert";
import { useAuth } from "context/AuthContext";
import { CustomLoader } from "components/CustomAction";
import { screenWidth } from "constants/sizes";
import AppleHealth from "assets/icons/apple_health.png";
import CustomHealthPermissionCard from "components/CustomCards/CustomHealthPermissionCard";
import GoogleHealthInstallationModel from "../../../components/PopUps/GoogleHealthInstallationPopUp";
import useHealthPermissionStore from "store/healthPermissionStore";
import Toast from "react-native-toast-message";

import { handleManagePermissions } from "utils/GoogleHealth";
import { Typography } from "constants/typography";
import { useHealthData } from "hooks/useHealthData";
import { useFocusEffect } from "@react-navigation/native";
import { getIsFirstCall } from 'services/health/healthDataService';


export default AndroidIntegrationScreen = () => {
    const healthDataHook = useHealthData();

    // Initialize the hook with try-catch for error handling
    let healthData = {};
    try {
        healthData = healthDataHook();
    } catch (error) {
        console.error('Error initializing health data hook:', error);
    }

    // Extract values with default fallbacks
    const {
        hasApplePermissions = false,
        hasPermissions = false, // Direct access to the internal permission state
        requestPermission = async () => ({ granted: false }),
        removePermission = async () => false,
        fetchAllAppleHealthData = async () => ({ error: 'Function not available' }),
        sendHealthDataToBackend = async () => ({ success: false, error: 'Function not available' }),
    } = healthData;


    const [isRequestingPermission, setIsRequestingPermission] = useState(false);


    const {
        isLoadingHealthPermissions,
        hasHealthPermission,
        hasAllHealthPermissions,
        hasHealthPermissionLastStatus,
        showHealthAppInstallationPopUp,
        setShowHealthAppInstallationAlert,
        showPermissionAlert,
        permissionRequestCalled,
        setShowPermissionAlert,
        setHealthPermission,
        healthPermissionError,
        hasBackgroundPermission,
    } = useHealthPermissionStore((state) => state);

    useEffect(() => {
        if (healthPermissionError) {
            Toast.show({
                type: "success",
                text1: "Something went wrong. Please try again.",
                position: "bottom",
                autoHide: true,
                bottomOffset: 80,
                hideOnPress: true,
            });
        }
    }, [healthPermissionError]);


    // Handle the toggle button click for health permissions
    const handleToggleChange = async () => {
        if (Platform.OS === "ios") {
            setIsRequestingPermission(true);

            try {
                // The alert is now shown inside the useHealthDataIOS hook
                const result = await requestPermission("apple");

                if (result.granted) {
                    // Show success message
                    Toast.show({
                        type: "success",
                        text1: "Apple Health permissions updated",
                        position: "bottom",
                        autoHide: true,
                        bottomOffset: 80,
                        hideOnPress: true,
                    });

                    // Send health data to backend after permissions are granted
                    try {
                        // Check if this is the first call to determine how many days to fetch
                        const isFirstCall = await getIsFirstCall();
                        const daysToFetch = isFirstCall ? 30 : 7; // First time: 30 days, subsequent: 7 days

                        await sendHealthDataToBackend(daysToFetch, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
                        console.log(`iOS health data sent to backend successfully (${daysToFetch} days)`);
                    } catch (error) {
                        console.error("Error sending iOS health data to backend:", error);
                    }
                } else {
                    // User declined or there was an error with missing permissions
                    if (result.missingPermissions && result.missingPermissions.length > 0) {

                        // Show a more detailed message about missing permissions
                        Toast.show({
                            type: "warning",
                            text1: "Some health permissions were not granted",
                            text2: "Please enable all permissions in Settings > Privacy > Health",
                            position: "bottom",
                            autoHide: true,
                            bottomOffset: 80,
                            hideOnPress: true,
                            visibilityTime: 4000,
                        });
                    } else {
                        // User declined all permissions
                        Toast.show({
                            type: "info",
                            text1: "Health permissions were not granted",
                            position: "bottom",
                            autoHide: true,
                            bottomOffset: 80,
                            hideOnPress: true,
                        });
                    }
                }
            } catch (error) {
                console.error("Error requesting health permissions:", error);

                // Show error message
                Toast.show({
                    type: "error",
                    text1: "Failed to update health permissions",
                    position: "bottom",
                    autoHide: true,
                    bottomOffset: 80,
                    hideOnPress: true,
                });
            } finally {
                setIsRequestingPermission(false);
            }
        } else {
            handleToggle("google", hasHealthPermission);
        }
    };

    const handleToggle = async (platform, currentStatus) => {
        try {
            setIsRequestingPermission(true);

            // If currently enabled, disable it; otherwise enable it
            if (currentStatus) {
                // Remove health permissions
                const removed = await removePermission(platform);

                if (removed) {
                    // Show success message
                    Toast.show({
                        type: "success",
                        text1: `${platform === 'apple' ? 'Apple Health' : 'Google Health'} permissions removed`,
                        position: "bottom",
                        autoHide: true,
                        bottomOffset: 80,
                        hideOnPress: true,
                    });
                } else {
                    // Show error message
                    Toast.show({
                        type: "error",
                        text1: `Failed to remove ${platform === 'apple' ? 'Apple Health' : 'Google Health'} permissions`,
                        position: "bottom",
                        autoHide: true,
                        bottomOffset: 80,
                        hideOnPress: true,
                    });
                }
            } else {
                // Request health permissions
                const result = await requestPermission(platform);

                if (result.granted) {
                    // Show success message
                    Toast.show({
                        type: "success",
                        text1: `${platform === 'apple' ? 'Apple Health' : 'Google Health'} permissions updated`,
                        position: "bottom",
                        autoHide: true,
                        bottomOffset: 80,
                        hideOnPress: true,
                    });

                    // For iOS, fetch health data and send to backend immediately after permissions are granted
                    if (platform === 'apple' && hasApplePermissions) {
                        fetchAllAppleHealthData();
                        // Send health data to backend similar to Android implementation
                        try {
                            const isFirstCall = await getIsFirstCall();
                            const daysToFetch = isFirstCall ? 30 : 7; // First time: 30 days, subsequent: 7 days
                            await sendHealthDataToBackend(daysToFetch, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
                            console.log(`iOS health data sent to backend (${daysToFetch} days)`);
                        } catch (error) {
                            console.error("Error sending iOS health data to backend:", error);
                        }
                    }
                } else {
                    // Check for missing permissions
                    if (platform === 'apple' && result.missingPermissions && result.missingPermissions.length > 0) {

                        // Show a more detailed message about missing permissions
                        Toast.show({
                            type: "warning",
                            text1: "Some health permissions were not granted",
                            text2: "Please enable all permissions in Settings > Privacy > Health",
                            position: "bottom",
                            autoHide: true,
                            bottomOffset: 80,
                            hideOnPress: true,
                            visibilityTime: 4000,
                        });
                    } else {
                        // User declined permissions
                        Toast.show({
                            type: "info",
                            text1: `${platform === 'apple' ? 'Apple Health' : 'Google Health'} permissions were not granted`,
                            position: "bottom",
                            autoHide: true,
                            bottomOffset: 80,
                            hideOnPress: true,
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Error toggling health permissions:', error);

            // Show error message
            Toast.show({
                type: "error",
                text1: "Failed to update health permissions",
                position: "bottom",
                autoHide: true,
                bottomOffset: 80,
                hideOnPress: true,
            });
        } finally {
            setIsRequestingPermission(false);
        }
    };

    // Subscribe to store changes and refresh health data on app state changes
    useEffect(() => {
        // Create a subscription to the store
        const unsubscribe = useHealthPermissionStore.subscribe(() => {
            // Store updated, no action needed
        });

        // Add listeners for app state changes to refresh data when app comes to foreground
        const handleAppStateChange = (nextAppState) => {
            if (nextAppState === 'active' && Platform.OS === 'ios') {
                // Use our safe function to check permissions and refresh data
                checkPermissionsAndRefreshData();
            }
        };

        const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);

        // Clean up subscriptions on unmount
        return () => {
            unsubscribe();
            appStateSubscription.remove();
        };
    }, [checkPermissionsAndRefreshData]);

    // Fetch health data and send to backend when permissions are granted
    useEffect(() => {
        if (Platform.OS === 'ios' && hasApplePermissions) {
            fetchAllAppleHealthData();
            // Also send health data to backend when permissions are available
            (async () => {
                try {
                    const isFirstCall = await getIsFirstCall();
                    const daysToFetch = isFirstCall ? 30 : 7; // First time: 30 days, subsequent: 7 days
                    const result = await sendHealthDataToBackend(daysToFetch, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
                    console.log(`iOS health data sent to backend (${daysToFetch} days):`, result);
                } catch (error) {
                    console.error("Error sending iOS health data to backend:", error);
                }
            })();
        }
    }, [hasApplePermissions, fetchAllAppleHealthData, sendHealthDataToBackend]);

    // Track permission changes
    useEffect(() => {
        // Permission state has changed, no logging needed
    }, [hasApplePermissions, hasPermissions, healthData.apiPermissions]);

    // Function to check permissions and refresh data
    const checkPermissionsAndRefreshData = useCallback(async () => {
        if (Platform.OS === 'ios') {
            try {
                // If permissions are granted, fetch health data and send to backend
                if (hasApplePermissions) {
                    await fetchAllAppleHealthData();
                    // Also send health data to backend (use 1 day for refresh operations)
                    await sendHealthDataToBackend(1, ["HeartRate", "Steps", "TotalCaloriesBurned", "Distance"], true, true);
                }
            } catch (error) {
                // Error handled silently
                console.error("Error in checkPermissionsAndRefreshData:", error);
            }
        }
    }, [hasApplePermissions, fetchAllAppleHealthData, sendHealthDataToBackend]);

    // Use React Navigation's useFocusEffect to refresh data when screen comes into focus
    useFocusEffect(
        useCallback(() => {
            // Call the refresh function without re-initializing hooks
            checkPermissionsAndRefreshData();

            // Return cleanup function
            return () => {
                // Screen lost focus
            };
        }, [checkPermissionsAndRefreshData])
    );

    if (isLoadingHealthPermissions || isRequestingPermission) {
        return (
            <View style={styles.loaderContainer}>
                <CustomLoader />
            </View>
        );
    }

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.headerText}>App Integrations</Text>

            {/* Google Health Dropdown */}
            <View style={styles.cardContainer}>
                <Text style={styles.sectionTitle}>Apple Health</Text>
                <CustomHealthPermissionCard
                    iconSource={AppleHealth}
                    description="Appetec would like to access and update your health data. For syncing your data with Apple Health, we need your permission to read health metrics."
                    isEnabled={hasApplePermissions}
                    onToggle={() => {
                        // If permissions are not granted, show the permission request
                        if (!hasApplePermissions) {
                            handleToggleChange();
                        } else {
                            // If permissions are already granted, just toggle them off
                            handleToggle("apple", hasApplePermissions);
                        }
                    }}
                    title="Workout & Health data"
                    lastUpdated={"30 minutes"}
                />
            </View>

            {/* Google Health Installation Modal */}
            <GoogleHealthInstallationModel
                showModal={showHealthAppInstallationPopUp}
                setShowModal={setShowHealthAppInstallationAlert}
            />

            <CustomAlert
                visible={
                    (permissionRequestCalled > 2 &&
                        showPermissionAlert &&
                        !hasHealthPermission) ||
                    (showPermissionAlert && !hasAllHealthPermissions) ||
                    (!hasBackgroundPermission && showPermissionAlert)
                }
                title={"Health permission required"}
                message={"Appetec would like to request health data."}
                buttons={[
                    {
                        text: "Manage permissions",
                        onPress: handleManagePermissions,
                        style: "button",
                    },
                ]}
                onClose={() => {
                    setShowPermissionAlert();
                }}
            />
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        width: screenWidth,
        backgroundColor: Colors.white,
        padding: 20,
    },
    headerText: {
        fontSize: 32,
        color: Colors.black,
        textAlign: "start",
        marginBottom: 20,
        fontFamily: "Exo_700Bold",
    },
    dropdownContainer: {
        backgroundColor: Colors.white,
        borderRadius: 10,
        marginBottom: 15,
        left: -20,
        // marginVertical: 5,
        // elevation: 2,
        padding: 10,
        // shadowColor: '#000',
        // shadowOffset: { width: 0, height: 2 },
        // shadowOpacity: 0.1,
        // shadowRadius: 4,
    },
    dropdownButton: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        padding: 10,
        // paddingVertical: 10,
        paddingHorizontal: 25,
        backgroundColor: Colors.primaryPurple,
        borderRadius: 20,
    },
    dropdownText: {
        fontSize: 16,
        color: Colors.white,
        fontFamily: "Exo_500Medium",
    },
    dropdownItem: {
        padding: 10,
        borderTopWidth: 1,
        borderColor: Colors.white,
    },
    rowContainer: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-around",
        width: screenWidth * 0.73,
        margin: 8,
    },
    imageContainer: {
        margin: 10,
        borderWidth: 1,
        borderColor: Colors.gray,
        borderRadius: 10,
        padding: 5,
    },
    healthIcon: {
        width: 40,
        height: 40,
        resizeMode: "contain",
    },
    textContainer: {
        flex: 1,
        paddingHorizontal: 10,
    },
    sectionHeading: {
        fontSize: 16,
        fontFamily: "Exo_700Bold",
        color: Colors.black,
        marginBottom: 5,
    },
    sectionDescription: {
        fontSize: 14,
        color: Colors.black,
        fontFamily: "Exo_500Medium",
    },
    toggleContainer: {
        flexDirection: "row",
        width: 50,
        alignItems: "center",
    },
    modalContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
    modalContent: {
        backgroundColor: Colors.white,
        padding: 20,
        borderRadius: 15,
        width: "80%",
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: "bold",
        color: Colors.darkGray,
        marginBottom: 10,
    },
    modalMessage: {
        fontSize: 14,
        color: Colors.darkGray,
        marginBottom: 20,
    },
    cancelButton: {
        marginTop: 10,
    },
    loaderContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "stretch",
        bottom: 50,
    },
    cardContainer: {
        backgroundColor: Colors.white,
        borderRadius: 10,
        marginBottom: 15,
        padding: 10,
    },
    sectionTitle: {
        ...Typography.STYLES.H4,
        marginBottom: 10,
    },
});