import React, { useState, useEffect, useCallback } from "react";
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
} from "react-native";
import { Colors } from "constants/theme/colors";
import CustomAlert from "components/CustomAction/CustomAlert";
import { CustomLoader } from "components/CustomAction";
import { screenWidth } from "constants/sizes";
import GoogleHealth from "assets/icons/google_health.png";
import CustomHealthPermissionCard from "components/CustomCards/CustomHealthPermissionCard";
import GoogleHealthInstallationModel from "../../../components/PopUps/GoogleHealthInstallationPopUp";
import useHealthPermissionStore from "store/healthPermissionStore";
import Toast from "react-native-toast-message";

import { handleManagePermissions } from "utils/GoogleHealth";
import { Typography } from "constants/typography";


export default AndroidIntegrationScreen = () => {

    const {
        isLoadingHealthPermissions,
        hasHealthPermission,
        hasAllHealthPermissions,
        hasHealthPermissionLastStatus,
        showHealthAppInstallationPopUp,
        setShowHealthAppInstallationAlert,
        showPermissionAlert,
        permissionRequestCalled,
        setShowPermissionAlert,
        setHealthPermission,
        healthPermissionError,
        hasBackgroundPermission,
    } = useHealthPermissionStore((state) => state);

    useEffect(() => {
        if (healthPermissionError) {
            Toast.show({
                type: "success",
                text1: "Something went wrong. Please try again.",
                position: "bottom",
                autoHide: true,
                bottomOffset: 80,
                hideOnPress: true,
            });
        }
    }, [healthPermissionError]);

    if (isLoadingHealthPermissions) {
        return (
            <View style={styles.loaderContainer}>
                <CustomLoader />
            </View>
        );
    }

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.headerText}>App Integrations</Text>

            {/* Google Health Dropdown */}
            <View style={styles.cardContainer}>
                <Text style={styles.sectionTitle}>Google Health</Text>
                <CustomHealthPermissionCard
                    iconSource={GoogleHealth}
                    description="Appetec would like to access and update your health data.For syncing your data with app download any health metric tracker,we recommend using google fit."
                    isEnabled={hasHealthPermission}
                    onToggle={setHealthPermission}
                    title="Workout & Health data"
                    lastUpdated={"30 minutes"}
                />
            </View>

            {/* Google Health Installation Modal */}
            <GoogleHealthInstallationModel
                showModal={showHealthAppInstallationPopUp}
                setShowModal={setShowHealthAppInstallationAlert}
            />

            <CustomAlert
                visible={
                    (permissionRequestCalled > 2 &&
                        showPermissionAlert &&
                        !hasHealthPermission) ||
                    (showPermissionAlert && !hasAllHealthPermissions) ||
                    (!hasBackgroundPermission && showPermissionAlert)
                }
                title={"Health permission required"}
                message={"Appetec would like to request health data."}
                buttons={[
                    {
                        text: "Manage permissions",
                        onPress: handleManagePermissions,
                        style: "button",
                    },
                ]}
                onClose={() => {
                    setShowPermissionAlert();
                }}
            />
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        width: screenWidth,
        backgroundColor: Colors.white,
        padding: 20,
    },
    headerText: {
        fontSize: 32,
        color: Colors.black,
        textAlign: "start",
        marginBottom: 20,
        fontFamily: "Exo_700Bold",
    },
    dropdownContainer: {
        backgroundColor: Colors.white,
        borderRadius: 10,
        marginBottom: 15,
        left: -20,
        // marginVertical: 5,
        // elevation: 2,
        padding: 10,
        // shadowColor: '#000',
        // shadowOffset: { width: 0, height: 2 },
        // shadowOpacity: 0.1,
        // shadowRadius: 4,
    },
    dropdownButton: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        padding: 10,
        // paddingVertical: 10,
        paddingHorizontal: 25,
        backgroundColor: Colors.primaryPurple,
        borderRadius: 20,
    },
    dropdownText: {
        fontSize: 16,
        color: Colors.white,
        fontFamily: "Exo_500Medium",
    },
    dropdownItem: {
        padding: 10,
        borderTopWidth: 1,
        borderColor: Colors.white,
    },
    rowContainer: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-around",
        width: screenWidth * 0.73,
        margin: 8,
    },
    imageContainer: {
        margin: 10,
        borderWidth: 1,
        borderColor: Colors.gray,
        borderRadius: 10,
        padding: 5,
    },
    healthIcon: {
        width: 40,
        height: 40,
        resizeMode: "contain",
    },
    textContainer: {
        flex: 1,
        paddingHorizontal: 10,
    },
    sectionHeading: {
        fontSize: 16,
        fontFamily: "Exo_700Bold",
        color: Colors.black,
        marginBottom: 5,
    },
    sectionDescription: {
        fontSize: 14,
        color: Colors.black,
        fontFamily: "Exo_500Medium",
    },
    toggleContainer: {
        flexDirection: "row",
        width: 50,
        alignItems: "center",
    },
    modalContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
    },
    modalContent: {
        backgroundColor: Colors.white,
        padding: 20,
        borderRadius: 15,
        width: "80%",
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: "bold",
        color: Colors.darkGray,
        marginBottom: 10,
    },
    modalMessage: {
        fontSize: 14,
        color: Colors.darkGray,
        marginBottom: 20,
    },
    cancelButton: {
        marginTop: 10,
    },
    loaderContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "stretch",
        bottom: 50,
    },
    cardContainer: {
        backgroundColor: Colors.white,
        borderRadius: 10,
        marginBottom: 15,
        padding: 10,
    },
    sectionTitle: {
        ...Typography.STYLES.H4,
        marginBottom: 10,
    },
});