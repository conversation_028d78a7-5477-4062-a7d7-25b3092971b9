import React, { useEffect, useState } from "react";
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Image,
    ScrollView,
    TouchableWithoutFeedback,
    Linking,
    InteractionManager,
} from "react-native";
import { CustomAlert, CustomButton, CustomLoader } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import deviceService from "services/deviceService";
import { useNavigation } from "@react-navigation/native";
import AppLayout from "navigations/components/Layouts/AppLayout";
import CustomSelectWithFixedHeightAndLabel from "components/CustomAction/CustomSelectWithFixedHeightAndLabel";
import { CameraView, useCameraPermissions } from "expo-camera";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import useDeviceManagementStore from "store/DeviceManagementStore";
import { ThemeFonts } from "constants/theme/fonts";
import CustomToast from "components/CustomAction/CustomToast";

export default AddDeviceScreen = () => {

    const navigation = useNavigation();
    const [permission, requestPermission] = useCameraPermissions();

    const [devices, setDevices] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [successMessage, setSuccessMessage] = useState(null);
    const [selectedDevice, setSelectedDevice] = useState(-1);
    const [deviceDetails, setDeviceDetails] = useState(null);
    const [isAddingDevice, setIsAddingDevice] = useState(false);

    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
    const [showAlert, setShowAlert] = useState(false);

    const [barCodeData, setBarCodeData] = useState(null);
    const [deviceData, setDeviceData] = useState(null);

    const [scanned, setScanned] = useState(false);
    const addDevice = useDeviceManagementStore(state => state.addDevice);
    const connectedDevices = useDeviceManagementStore(state => state.devices);

    const [validationError, setValidationError] = useState(null);
    const [hasScreenLoaded, setHasScreenLoaded] = useState(false);

    const formatDeviceData = (device) => ({
        ...device,
        name:
            device.name.charAt(0).toUpperCase() + device.name.slice(1).toLowerCase(),
        version: `V${device.version}`,
        image: device.thumbnailUrl,
    });

    const fetchDevices = async () => {
        setLoading(true);
        const res = await deviceService.getDevices();

        if (res.success) {
            if (res.data.length > 0) {
                const formattedDevices = res.data.map(formatDeviceData);
                setDevices(formattedDevices);
            }
        } else {
            setError(res.error);
        }

        setLoading(false);
    };


    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            fetchDevices();
            setHasScreenLoaded(true);
        });
    }, []);

    const handleDeviceSelect = (index) => {
        setSelectedDevice(index);
    };

    useEffect(() => {
        if (selectedDevice !== -1) {
            setDeviceDetails(devices.find((device, index) => device?.id === selectedDevice));
        }
    }, [selectedDevice]);

    const handleBarCodeScanned = ({ type, data }) => {
        setBarCodeData(data);
        setScanned(true);
    };

    const handleAddDevice = () => {
        if (selectedDevice <= -1) {
            setValidationError({
                selectDeviceError: "Please select a device",
            });
            return;
        }

        if (!deviceData) {
            setValidationError({
                scanDeviceError: "Please scan a device",
            });
            return;
        }

        setIsAddingDevice(true);
        addDevice({
            ...deviceDetails,
            serialId: deviceData.serialId,
        });
        setSuccessMessage("Device connected successfully");
        setIsAddingDevice(false);
        navigation.goBack();
    }

    useEffect(() => {
        if (scanned) {
            setValidationError(null);
        }
    }, [scanned]);

    if (!hasScreenLoaded) {
        return (
            <AppLayout>
                <View style={styles.loaderContainer}>
                    <CustomLoader />
                </View>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <>
                {
                    (devices.length === 0 && !loading) ? (
                        <View style={[styles.container, { justifyContent: "center", alignItems: "center", marginBottom: 90 }]}>
                            <Text style={styles.noDevicesText}>No Devices Found</Text>
                        </View>
                    ) : (

                        <ScrollView
                            contentContainerStyle={{ flex: 1 }}
                            showsVerticalScrollIndicator={false}
                        >

                            <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
                                <View style={styles.container}>
                                    <View style={styles.content}>
                                        <Text style={styles.title}>Add Device</Text>
                                        <View style={{ gap: 16 }}>
                                            <View style={styles.devicesContainer}>
                                                <CustomSelectWithFixedHeightAndLabel
                                                    options={[
                                                        ...devices.map((device, index) => ({
                                                            label: device?.name,
                                                            value: device?.id,
                                                        })),
                                                    ]}
                                                    selectedValue={selectedDevice}
                                                    onValueChange={handleDeviceSelect} setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                                    currentOpenDropdown={currentOpenDropdown}
                                                    dropdownId={"device_dropdown"} triggerZ={3000} listZ={1000}
                                                    label="Select Device"
                                                    loading={loading}
                                                    maxOptionsShown={3}
                                                    error={validationError?.selectDeviceError}
                                                    clearValidationError={() => {
                                                        setValidationError((prev) => ({
                                                            ...prev,
                                                            selectDeviceError: "",
                                                        }));
                                                    }}
                                                    isLoading={loading}
                                                />
                                            </View>
                                            {
                                                deviceDetails && (
                                                    <View>
                                                        <Text style={styles.scanDeviceHeading}>Scan your device</Text>
                                                        <View style={styles.referenceImage}>
                                                            {!permission?.granted ? (
                                                                <TouchableOpacity
                                                                    style={styles.cameraPermissionButton}
                                                                    activeOpacity={0.8}
                                                                    onPress={async () => {
                                                                        const result = await requestPermission();
                                                                        if (result.canAskAgain === false) {
                                                                            setShowAlert(true);
                                                                        }
                                                                    }}
                                                                >
                                                                    <View style={styles.cameraPermissionContainer}>
                                                                        <MaterialCommunityIcons name="barcode-scan" size={24} color={Colors.primaryPurple} />
                                                                        <Text style={styles.cameraPermissionText}>
                                                                            Scan Barcode
                                                                        </Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                            ) : deviceData?.serialId ? (
                                                                <View>
                                                                    <Image
                                                                        source={{ uri: 'https://cdn11.bigcommerce.com/s-ilgxsy4t82/images/stencil/1280x1280/products/323073/1136161/61--uDS1DJL._AC_SL1500___09686.1739336657.jpg?c=1&imbypass=on' }}
                                                                        style={styles.deviceImageStyle}
                                                                        resizeMode="cover"
                                                                    />
                                                                    <View style={styles.deviceInfoContainer}>
                                                                        <Text style={styles.serialNoLabel}>Serial Number of product</Text>
                                                                        <Text style={styles.serialNoText}>Bob marley - 1234</Text>
                                                                    </View>
                                                                </View>
                                                            ) : (
                                                                <>
                                                                    <CameraView
                                                                        style={styles.camera}
                                                                        facing={'back'}
                                                                        barcodeScannerSettings={{
                                                                            barcodeTypes: ["qr", "pdf417", "ean13", "ean8", "code39", "code128"],
                                                                        }}
                                                                        mode="picture"
                                                                        onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
                                                                        flash='off'
                                                                    >
                                                                        <View style={styles.scanOverlay}>
                                                                            <View style={styles.scanFrame} />
                                                                            <Text style={styles.scanText}>
                                                                                {scanned ? 'Device ID scanned!' : 'Align QR code within frame'}
                                                                            </Text>
                                                                        </View>
                                                                    </CameraView>
                                                                    <CustomAlert
                                                                        visible={barCodeData}
                                                                        title={"Device ID Scanned"}
                                                                        message={barCodeData}
                                                                        buttons={[
                                                                            {
                                                                                text: "Scan Again",
                                                                                onPress: () => {
                                                                                    setBarCodeData(null);
                                                                                    setScanned(false);
                                                                                },
                                                                            },
                                                                            {
                                                                                text: "Next",
                                                                                onPress: () => {
                                                                                    setDeviceData({
                                                                                        serialId: barCodeData,
                                                                                    });
                                                                                    setBarCodeData(null);
                                                                                },
                                                                            },
                                                                        ]}
                                                                        onClose={() => {
                                                                            setBarCodeData(null);
                                                                            setScanned(false)
                                                                        }}
                                                                    />
                                                                </>
                                                            )}
                                                        </View>
                                                        {validationError?.scanDeviceError && (
                                                            <Text style={styles.error}>{validationError?.scanDeviceError}</Text>
                                                        )}
                                                    </View>
                                                )
                                            }
                                            <CustomButton
                                                title="Connect"
                                                onPress={handleAddDevice}
                                                disabled={isAddingDevice}
                                                disabledBgColor={Colors.primaryPurple}
                                                disabledTextColor={Colors.white}
                                                style={{
                                                    width: 110,
                                                    alignSelf: "flex-end",
                                                }}
                                                isLoading={loading}
                                            />
                                        </View>
                                    </View>
                                    <CustomAlert
                                        visible={!!error}
                                        title="Error"
                                        message={error}
                                        buttons={[
                                            { text: "OK", onPress: () => setError(null), style: "allowButton" },
                                        ]}
                                        onClose={() => setError(null)}
                                    />

                                    <CustomAlert
                                        visible={showAlert}
                                        title="Camera Permission Required"
                                        message="Please grant camera permission to scan your device."
                                        buttons={[
                                            {
                                                text: "OK",
                                                onPress: () => {
                                                    setShowAlert(false);
                                                    Linking.openSettings();
                                                },
                                                style: "allowButton"
                                            }
                                        ]}
                                        onClose={() => setShowAlert(false)}
                                    />
                                </View>
                            </TouchableWithoutFeedback>
                        </ScrollView>
                    )
                }
                <CustomToast
                    error={error || successMessage}
                    clearErrors={() => {
                        setError(null);
                        setSuccessMessage(null);
                    }}
                    isError={!!error}
                />
            </>
        </AppLayout>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    content: {
        flex: 1,
    },
    title: {
        fontSize: 35,
        fontFamily: "Exo_700Bold",
        color: Colors.black,
        marginHorizontal: 16
    },
    devicesContainer: {
        marginTop: 16,
    },
    centerSingleDevice: {
        justifyContent: "center",
        alignItems: "center",
    },
    rowTwoDevices: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
    },
    rowMultipleDevices: {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "center",
        alignItems: "center",
    },
    deviceBox: {
        backgroundColor: Colors.white,
        padding: 20,
        borderRadius: 15,
        justifyContent: "center",
        width: 100,
        height: 100,
        borderColor: Colors.darkGreen,
        borderRightWidth: 7,
        borderBottomWidth: 7,
    },
    selectedDevice: {
        backgroundColor: Colors.white,
        borderColor: Colors.primaryPurple,
        borderWidth: 2,
        transform: [{ scale: 1.0 }],
        // Add subtle shadow for selected state
        shadowColor: Colors.primaryPurple,
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 4.65,
        elevation: 8,
    },
    centerContent: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    loadingText: {
        color: Colors.white,
        fontSize: 18,
        fontFamily: "Exo_400Regular",
    },
    errorText: {
        color: Colors.white,
        fontSize: 16,
        fontFamily: "Exo_400Regular",
        textAlign: "center",
    },
    deviceHeading: {
        color: Colors.black,
        fontSize: 18,
        fontFamily: "Exo_700Bold",
        textAlign: "center",
    },
    deviceText: {
        color: Colors.black,
        fontSize: 12,
        fontFamily: "Exo_400Regular",
        textAlign: "center",
    },
    footer: {
        height: "25%",
        backgroundColor: Colors.white,
        borderTopLeftRadius: 50,
        borderTopRightRadius: 50,
        justifyContent: "center",
        padding: 20,
    },
    buttonContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        zIndex: 99,
    },
    lemon: {
        position: "absolute",
        top: "71%",
        left: "-17%",
        width: "55%",
        height: "55%",
        resizeMode: "contain",
        zIndex: 99, // Lower than the content and form containers
        transform: [{ translateY: "-50%" }, { rotate: "10deg" }],
    },
    backgroundImage: {
        position: "absolute",
        top: "8%",
        right: "-10%",
        width: "50%",
        height: "50%",
        resizeMode: "contain",
        zIndex: 99, // Lower than the content and form containers
        transform: [{ translateY: "-50%" }, { rotate: "-5deg" }],
    },
    deviceBGImage: {
        width: "100%",
        aspectRatio: 16 / 10.5,
        resizeMode: "cover",
        borderRadius: 25,
    },
    referenceImage: {
        backgroundColor: Colors.white,
        width: "100%",
        aspectRatio: 16 / 10.5,
        marginBottom: 10,
        borderRadius: 25,
        borderColor: Colors.primaryPurple,
        borderWidth: 2,
        overflow: 'hidden',
    },
    deviceImageStyle: {
        width: '100%',
        height: '100%',
    },
    deviceInfoContainer: {
        position: 'absolute',
        bottom: 10,
        left: 8,
        right: 8,
        backgroundColor: Colors.white,
        padding: 4,
        paddingHorizontal: 12,
        borderRadius: 25,
        color: Colors.black,
        textAlign: 'left',
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
    },
    serialNoLabel: {
        fontSize: 11,
        color: Colors.darkGray,
    },
    serialNoText: {
        fontSize: 14,
    },
    error: {
        color: 'red',
        marginLeft: 16
    },
    footer: {
        alignItems: 'center',
        width: '100%',
        marginTop: 20,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: 20,
    },
    skipText: {
        color: Colors.primaryPurple,
        textDecorationLine: 'underline',
        fontFamily: 'Exo_400Regular',
    },
    lemon: {
        position: 'absolute',
        bottom: '-51%',
        left: '-15%',
        width: '55%',
        height: '55%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '0deg' }],
    },
    lemon_2: {
        position: 'absolute',
        top: '3%',
        right: '-4%',
        width: '25%',
        height: '25%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '-26deg' }],
    },
    leaf: {
        position: 'absolute',
        bottom: '-43%',
        right: '-15%',
        width: '50%',
        height: '50%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '45deg' }],
    },
    leaf_2: {
        position: 'absolute',
        top: '22%',
        left: '-15%',
        width: '50%',
        height: '50%',
        resizeMode: 'contain',
        zIndex: 9997,
        transform: [{ translateY: '-50%' }, { rotate: '10deg' }],
    },
    camera: {
        flex: 1,
        width: '100%',
        height: '100%',
    },
    scanOverlay: {
        flex: 1,
        backgroundColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
        gap: 5,
    },
    scanFrame: {
        flex: 1,
        aspectRatio: 1,
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
        backgroundColor: 'transparent',
    },
    scanText: {
        marginTop: 10,
        fontSize: 14,
        color: '#ffffff',
        backgroundColor: 'transparent',
        borderRadius: 5,
    },
    cameraControls: {
        flexDirection: 'row',
        justifyContent: 'center',
    },
    torchButton: {
        backgroundColor: 'rgba(0,0,0,0.6)',
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 20,
        marginHorizontal: 5,
    },
    scanAgainButton: {
        backgroundColor: 'rgba(0,0,0,0.6)',
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 20,
        marginHorizontal: 5,
    },
    buttonText: {
        color: '#ffffff',
        fontSize: 12,
    },
    cameraPermissionButton: {
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        borderRadius: 15,
    },
    cameraPermissionText: {
        color: Colors.primaryPurple,
        fontFamily: 'Exo_500Medium',
        fontSize: 16,
        textAlign: 'center',
    },
    cameraPermissionContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 10,
    },
    scanDeviceHeading: {
        color: Colors.black,
        fontSize: 18,
        fontFamily: "Exo_700Bold",
        textAlign: "center",
        marginBottom: 10,
    },
    noDevicesText: {
        color: Colors.darkGray,
        fontSize: 18,
        fontFamily: ThemeFonts.Exo_500,
        marginBottom: 10,
        textAlign: "center",
        marginTop: 10,
    },
    loaderContainer: {
        flex: 1,
        bottom: 50,
        justifyContent: "center",
        alignItems: "stretch",
    },
});

