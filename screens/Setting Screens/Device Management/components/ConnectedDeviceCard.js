import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { Colors } from 'constants/theme/colors'

const ConnectedDeviceCard = ({ device, onClick }) => {
    return (
        <View key={device.id} style={styles.deviceCard}>
            <View style={styles.deviceHeader}>
                <Text style={styles.deviceName}>{device.name}</Text>
                <TouchableOpacity
                    style={[
                        styles.statusButton,
                        {
                            backgroundColor:
                                true ? Colors.primaryGreen : Colors.textLight,
                        },
                    ]}
                    onPress={() => onClick(device.id)}
                >
                    <Text style={styles.statusText}>{"Connected"}</Text>
                </TouchableOpacity>
            </View>

            {/* <View style={styles.deviceInfo}>
                        <View style={styles.infoItem}>
                            <Icon name="time-outline" size={20} color={Colors.textDark} />
                            <Text style={styles.infoText}>Last Sync: {device.lastSync}</Text>
                        </View>
                        <View style={styles.infoItem}>
                            <Icon name="battery-half-outline" size={20} color={Colors.textDark} />
                            <Text style={styles.infoText}>Battery: {device.batteryLevel}</Text>
                        </View>
                    </View> */}
        </View>
    )
}

export default ConnectedDeviceCard

const styles = StyleSheet.create({

    deviceCard: {
        backgroundColor: Colors.white,
        padding: 15,
        // borderRadius: 15,
        borderRadius: 24,
        marginBottom: 15,
        borderColor: Colors.primaryPurple,
        borderWidth: 2,
        shadowColor: Colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        elevation: 2,
    },
    deviceHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        // marginBottom: 10,
    },
    deviceName: {
        fontSize: 18,
        fontFamily: "Exo_400Regular",
        color: Colors.textDark,
    },
    statusButton: {
        paddingHorizontal: 14,
        paddingVertical: 7,
        borderRadius: 20,
    },
    statusText: {
        color: Colors.white,
        fontSize: 13,
        fontWeight: "bold",
    },
    deviceInfo: {
        marginTop: 5,
    },
    infoItem: {
        flexDirection: "row",
        alignItems: "center",
        marginBottom: 5,
    },
    infoText: {
        marginLeft: 8,
        color: Colors.textDark,
        fontSize: 14,
    },
})