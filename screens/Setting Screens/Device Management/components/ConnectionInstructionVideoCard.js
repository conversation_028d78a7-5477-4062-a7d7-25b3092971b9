import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { Colors } from 'constants/theme/colors'
import { useNavigation } from '@react-navigation/native';
import Icon from "react-native-vector-icons/Ionicons";
import { getYouTubeThumbnail } from 'utils/youtube';

const ConnectionInstructionVideoCard = () => {
    const navigation = useNavigation();
    return (
        <View style={styles.helpCard}>
            <Text style={styles.helpHeader}>How to connect a device?</Text>
            <View style={styles.videoContainer}></View>

            <TouchableOpacity
                activeOpacity={.8}
                style={styles.videoCard}
                onPress={() => navigation.navigate("Video Details", {
                    videoId: "sZeTW6K4j00",
                    title: "How to connect a device?",
                    description: "Watch this video to learn how to connect a device",
                    videoURL: "https://youtu.be/embed/bsQZ_j4hZs8",
                })}
            >
                <Image
                    source={{ uri: getYouTubeThumbnail("https://youtu.be/bsQZ_j4hZs8?feature=shared") }}
                    style={styles.videoTutorialImage}
                />
            </TouchableOpacity>
        </View>
    )
}

export default ConnectionInstructionVideoCard

const styles = StyleSheet.create({
    videoTutorialImage: {
        width: "100%",
        aspectRatio: 16 / 10,
        borderRadius: 25,
    },
    helpCard: {
        backgroundColor: Colors.lightPurple,
        padding: 20,
        borderRadius: 24,
        marginTop: 20,
    },
    helpHeader: {
        fontSize: 30,
        color: Colors.white,
        fontFamily: "Exo_700Bold",
        marginBottom: 5,
    },
    helpText: {
        color: Colors.white,
        fontSize: 14,
        fontFamily: "Exo_400Regular",
        marginBottom: 10,
    },
})