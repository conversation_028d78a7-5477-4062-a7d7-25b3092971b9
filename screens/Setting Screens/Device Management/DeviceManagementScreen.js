import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Linking,
  FlatList,
  InteractionManager,
} from "react-native";
import { Colors } from "constants/theme/colors";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenWidth } from "constants/sizes";
import { CustomAlert, CustomButton, CustomLoader } from "components/CustomAction";
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from "@react-navigation/native";
import useDeviceManagementStore from "store/DeviceManagementStore";
import ConnectedDeviceCard from "./components/ConnectedDeviceCard";
import { ThemeFonts } from "constants/theme/fonts";
import ConnectionInstructionVideoCard from "./components/ConnectionInstructionVideoCard";

export const DeviceManagementScreen = () => {
  const navigation = useNavigation();

  const connectedDevices = useDeviceManagementStore(state => state.devices);
  const removeDevice = useDeviceManagementStore(state => state.removeDevice);
  const [showDisconnectDeviceId, setShowDisconnectDeviceId] = useState(null);

  const [hasPageLoaded, setHasPageLoaded] = useState(false);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      setHasPageLoaded(true);
    });
  }, []);

  if (!hasPageLoaded) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout illustration={false}>
      <ScrollView style={styles.container}>
        <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
          <Text style={styles.menuHeader} numberOfLines={1} ellipsizeMode="tail">Device</Text>
          <CustomButton
            title="Add Device"
            Icon={
              <MaterialIcons name="add" size={24} color={Colors.white} />
            }
            onPress={() => navigation.navigate("Add Device")}
            style={{ width: "auto", paddingHorizontal: 8 }}
          />
        </View>
        {
          (connectedDevices && connectedDevices?.length > 0) ? (
            <FlatList data={connectedDevices} keyExtractor={(item) => item.id} scrollEnabled={false} style={{ marginTop: 16 }} renderItem={({ item }) => (
              <ConnectedDeviceCard device={item} onClick={(id) => {
                setShowDisconnectDeviceId(id);
              }} />
            )} />
          ) : (
            <Text style={styles.noDevicesText}>No devices connected</Text>
          )
        }
        <ConnectionInstructionVideoCard />
        <CustomAlert visible={showDisconnectDeviceId} title="Disconnect Device" message="Are you sure you want to disconnect this device?" buttons={[
          {
            text: "Cancel",
            onPress: () => setShowDisconnectDeviceId(null),
            style: "cancelButton"
          },
          {
            text: "Disconnect",
            onPress: () => {
              removeDevice(showDisconnectDeviceId);
              setShowDisconnectDeviceId(null);
            },
            style: "allowButton"
          }
        ]}
          onClose={() => setShowDisconnectDeviceId(null)}
        />
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth,
    left: -20,
    backgroundColor: Colors.white,
    padding: 20,
    borderRadius: 10,
    zIndex: 100,
  },
  menuHeader: {
    fontSize: 28,
    color: Colors.black,
    textAlign: "left",
    marginLeft: 20,
    marginBottom: 15,
    fontFamily: "Exo_700Bold",
  },
  helpCard: {
    height: 250,
    backgroundColor: Colors.lightPurple,
    padding: 20,
    borderRadius: 24,
    marginTop: 20,
  },
  helpHeader: {
    fontSize: 30,
    color: Colors.white,
    fontFamily: "Exo_700Bold",
    marginBottom: 5,
  },
  helpText: {
    color: Colors.white,
    fontSize: 14,
    fontFamily: "Exo_400Regular",
    marginBottom: 10,
  },
  noDevicesText: {
    color: Colors.darkGray,
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_500,
    marginBottom: 10,
    textAlign: "center",
    marginTop: 10,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});

export default DeviceManagementScreen;
