import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, KeyboardAvoidingView, Platform, Keyboard, TouchableWithoutFeedback, ScrollView } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Logo from 'assets/icon.png';  // Assuming this is your logo
import Image1 from 'assets/illustrator/illustrator_1.png';
import { CustomAlert, CustomButton, CustomInput, CustomLoader } from 'components/CustomAction';
import { Colors } from 'constants/theme/colors';
import authService from 'services/authService';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

export const ResendVerificationMail = () => {
	const route = useRoute();
	const navigation = useNavigation();
	const [error, setError] = useState('');
	const [showAlert, setShowAlert] = useState(false);
	const [loading, setLoading] = useState(false);

	const handleResendVerification = async () => {
		setLoading(true);
		setError('');

		const response = await authService.resendVerificationLink(route?.params?.email);

		if (response.success) {
			setShowAlert(true);
		} else {
			setError(response.error || 'Failed to send verification email. Please try again.');
		}

		setLoading(false);
	};

	return (
		<KeyboardAwareScrollView
			style={styles.container}
			contentContainerStyle={styles.scrollContainer}
			behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
			keyboardVerticalOffset={Platform.OS === 'ios' ? 80 : -38}
			showsVerticalScrollIndicator={false}
		>
			<CustomAlert
				visible={showAlert}
				title="Email Sent"
				message="Another verification email has been resent. Please check your inbox."
				buttons={[
					{
						text: "OK",
						onPress: () => {
							setShowAlert(false);
							navigation.goBack();
						},
						style: "allowButton"
					}
				]}
				onClose={() => setShowAlert(false)}
			/>
			{loading && <CustomLoader />}
			<Text style={styles.text}>Resend Verification Link</Text>
			<CustomInput
				value={route?.params?.email}
				onChangeText={() => { }}
				placeholder="Email"
				editable={false}
				clearValidationError={() => setError("")}
			/>
			{error && <Text style={styles.error}>{error}</Text>}
			<View style={styles.buttonContainer}>
				<CustomButton
					title="Back"
					onPress={() => navigation.goBack()}
					textColor={Colors.primaryPurple}
					backgroundColor={Colors.lightGreen}
					textStyle={{ fontFamily: "Exo_700Bold" }}
				/>
				<CustomButton
					title="Resend"
					onPress={handleResendVerification}
					textColor={Colors.primaryPurple}
					backgroundColor={Colors.lightGreen}
					textStyle={{ fontFamily: "Exo_700Bold" }}
				/>
			</View>
		</KeyboardAwareScrollView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: 20,
		backgroundColor: Colors.primaryGreen,
	},
	scrollContainer: {
		flexGrow: 1,
		justifyContent: 'center',
		paddingBottom: 20,
	},
	error: {
		color: 'red',
		fontSize: 12,
		marginHorizontal: 10,
		fontFamily: 'Exo_400Regular',
	},
	buttonContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		marginTop: 20,
	},
	text: {
		fontSize: 32,
		marginBottom: 38,
		fontFamily: 'Exo_700Bold',
		textAlign: 'center',
		color: Colors.white,
	},
});