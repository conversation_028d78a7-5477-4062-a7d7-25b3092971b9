import React, { useState, useEffect, useCallback, memo } from 'react';
import { View, Text, Image, Platform, StyleSheet, Alert, Linking } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { CustomButton, CustomLoader } from 'components/CustomAction';
import { HealthOptionRow } from 'components/CustomCards/HealthOptionRow';
import { useAuth } from 'context/AuthContext';
import CustomAlert from 'components/CustomAction/CustomAlert';
import AppleHealth from 'assets/icons/apple_health.png';
import GoogleHealth from 'assets/icons/google_health.png';
import Lemon from 'assets/illustrator/illustrator_1.png';
import Leaf from 'assets/illustrator/Illustrator_6.png';
import { Colors } from 'constants/theme/colors';
import useHealthPermissionStore from 'store/healthPermissionStore';
import GoogleHealthInstallationModel from 'components/PopUps/GoogleHealthInstallationPopUp';
import Toast from 'react-native-toast-message';
import { handleManagePermissions } from 'utils/GoogleHealth';
import useOnboardingFormStore from 'store/onboadingFormStore';
import { useHealthData } from 'hooks/useHealthData';

const AppPermission = () => {
    const { currentOnboardingStep } = useOnboardingFormStore();
    const { setOnboardingStep, setAppOnboardingPermissions } = useOnboardingFormStore();

    const { state: session } = useAuth();

    const {
        isLoadingHealthPermissions,
        hasHealthPermission,
        hasAllHealthPermissions,
        showHealthAppInstallationPopUp,
        setShowHealthAppInstallationAlert,
        showPermissionAlert,
        permissionRequestCalled,
        setShowPermissionAlert,
        setHealthPermission,
        healthPermissionError,
        hasBackgroundPermission
    } = useHealthPermissionStore(state => state);

    // Get the platform-specific health data hook
    const healthDataHook = useHealthData();

    // Initialize the hook with try-catch for error handling
    let healthData = {};
    try {
        healthData = healthDataHook();
    } catch (error) {
        console.error('Error initializing health data hook:', error);
    }

    // Extract values with default fallbacks
    const {
        hasApplePermissions = false,
        requestPermission = async () => ({ granted: false }),
        removePermission = async () => false,
    } = healthData;

    const [isRequestingPermission, setIsRequestingPermission] = useState(false);
    const [hasShownHealthDataAccessAlert, setHasShownHealthDataAccessAlert] = useState(false);

    const [alertConfig, setAlertConfig] = useState({
        title: '',
        message: '',
        type: 'initial',
        show: false
    });

    // Handle iOS health permission toggle
    const handleIOSHealthToggle = async () => {
        if (hasApplePermissions) {
            // If permissions are already granted, remove them
            setIsRequestingPermission(true);
            try {
                const result = await removePermission('apple');
                if (result) {
                    Toast.show({
                        type: 'success',
                        text1: 'Apple Health permissions removed',
                        position: 'bottom',
                        autoHide: true,
                        bottomOffset: 80,
                        hideOnPress: true,
                    });
                }
            } catch (error) {
                console.error('Error removing health permissions:', error);
                Toast.show({
                    type: 'error',
                    text1: 'Failed to remove health permissions',
                    position: 'bottom',
                    autoHide: true,
                    bottomOffset: 80,
                    hideOnPress: true,
                });
            } finally {
                setIsRequestingPermission(false);
            }
        } else {
            // If permissions are not granted, show the permission alert
            // This is similar to how it's done in IntegrationScreen.js
            setIsRequestingPermission(true);

            // Show an alert asking for health permission
            Alert.alert(
                "Health Permissions",
                "Appetec would like to access your health data to provide personalized insights and track your progress.",
                [
                    {
                        text: "Cancel",
                        style: "cancel",
                        onPress: () => {
                            setIsRequestingPermission(false);
                            Toast.show({
                                type: 'info',
                                text1: 'Health permissions were not granted',
                                position: 'bottom',
                                autoHide: true,
                                bottomOffset: 80,
                                hideOnPress: true,
                            });
                        }
                    },
                    {
                        text: "Allow",
                        onPress: async () => {
                            try {
                                const result = await requestPermission('apple');

                                if (result.granted) {
                                    Toast.show({
                                        type: 'success',
                                        text1: 'Apple Health permissions granted',
                                        position: 'bottom',
                                        autoHide: true,
                                        bottomOffset: 80,
                                        hideOnPress: true,
                                    });
                                } else {
                                    // User declined or there was an error
                                    if (result.missingPermissions && result.missingPermissions.length > 0) {
                                        Alert.alert(
                                            "Missing Permissions",
                                            "Some health permissions were not granted. Please go to Settings > Privacy > Health to enable all permissions for Appetec.",
                                            [{ text: "OK", style: "default" }]
                                        );

                                        Toast.show({
                                            type: 'warning',
                                            text1: 'Some health permissions were not granted',
                                            text2: 'Please enable all permissions in Settings > Privacy > Health',
                                            position: 'bottom',
                                            autoHide: true,
                                            bottomOffset: 80,
                                            hideOnPress: true,
                                            visibilityTime: 4000,
                                        });
                                    } else {
                                        Toast.show({
                                            type: 'info',
                                            text1: 'Health permissions were not granted',
                                            position: 'bottom',
                                            autoHide: true,
                                            bottomOffset: 80,
                                            hideOnPress: true,
                                        });
                                    }
                                }
                            } catch (error) {
                                console.error('Error requesting health permissions:', error);
                                Toast.show({
                                    type: 'error',
                                    text1: 'Failed to request health permissions',
                                    position: 'bottom',
                                    autoHide: true,
                                    bottomOffset: 80,
                                    hideOnPress: true,
                                });
                            } finally {
                                setIsRequestingPermission(false);
                            }
                        }
                    }
                ],
                { cancelable: false }
            );
        }
    };

    useFocusEffect(useCallback(() => {
        setHasShownHealthDataAccessAlert(false);
    }, []));

    useEffect(() => {
        const app_permissions = [];

        if (hasHealthPermission && Platform.OS === "android") app_permissions.push("google");
        else if ((hasHealthPermission || hasApplePermissions) && Platform.OS === "ios") app_permissions.push("apple");

        setAppOnboardingPermissions([...app_permissions]);
    }, [hasHealthPermission, hasApplePermissions]);


    useEffect(() => {
        if (healthPermissionError) {
            Toast.show({
                type: 'success',
                text1: 'Something went wrong. Please try again.',
                position: 'bottom',
                autoHide: true,
                bottomOffset: 80,
                hideOnPress: true,
            });
        }
    }, [healthPermissionError]);


    // Handle next step navigation
    const handleNext = useCallback(() => {
        if (!hasHealthPermission && !hasShownHealthDataAccessAlert) {
            setAlertConfig({
                title: 'Permissions Required',
                message: 'For better experience please grant health data access permission.',
                type: 'required',
                show: true
            });
            setHasShownHealthDataAccessAlert(true);
            return;
        }

        currentOnboardingStep < 5 && setOnboardingStep(currentOnboardingStep + 1);
    }, [currentOnboardingStep, setOnboardingStep, hasHealthPermission, hasShownHealthDataAccessAlert]);

    const handleBack = useCallback(() => {
        currentOnboardingStep > 1 && setOnboardingStep(currentOnboardingStep - 1);
    }, [currentOnboardingStep, setOnboardingStep]);

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.headerText}>Welcome, {session?.user?.name?.trim().split(/\s+/)[0] || ''}!</Text>
                <Text style={styles.text}>
                    Please provide some information to get started.
                </Text>
            </View>

            {isLoadingHealthPermissions ? (
                <View style={styles.loaderContainer}>
                    <CustomLoader />
                </View>
            ) : (
                <View style={styles.content}>
                    <Text style={styles.title}>App Permission</Text>
                    {isRequestingPermission ? (
                        <View style={styles.loaderContainer}>
                            <CustomLoader />
                        </View>
                    ) : (
                        Platform.OS === 'ios' ?
                            <HealthOptionRow
                                icon={AppleHealth}
                                onToggle={handleIOSHealthToggle}
                                checked={hasApplePermissions}
                            /> :
                            <HealthOptionRow
                                icon={GoogleHealth}
                                onToggle={setHealthPermission}
                                checked={hasHealthPermission}
                            />
                    )}
                    <View style={styles.buttonContainer}>
                        <CustomButton title="Back" onPress={handleBack} />
                        <CustomButton title="Next" onPress={handleNext} />
                    </View>
                </View>
            )}

            <Image source={Lemon} style={styles.backgroundImage1} />
            <Image source={Leaf} style={styles.leaf_background} />

            {
                Platform.OS == "android" && <GoogleHealthInstallationModel showModal={showHealthAppInstallationPopUp} setShowModal={setShowHealthAppInstallationAlert} />
            }

            <CustomAlert
                visible={alertConfig?.show}
                title={alertConfig.title}
                message={alertConfig.message}
                buttons={[
                    {
                        text: "OK",
                        onPress: async () => {
                            await Linking.openSettings();
                        },
                        style: "button",
                    },
                ]}
                onClose={() => setAlertConfig((prev) => ({
                    ...prev,
                    show: false,
                }))}
            />

            <CustomAlert
                visible={(permissionRequestCalled > 2 && showPermissionAlert && !hasHealthPermission) || (showPermissionAlert && !hasAllHealthPermissions) || (!hasBackgroundPermission && showPermissionAlert)}
                title={"Health permission required"}
                message={"Appetec would like to request health data."}
                buttons={[
                    {
                        text: "Manage permissions",
                        onPress: handleManagePermissions,
                        style: "button",
                    },
                ]}
                onClose={() => {
                    setShowPermissionAlert();
                }}
            />
        </View>
    );
};

export default memo(AppPermission);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        backgroundColor: Colors.primaryGreen,
    },
    header: {
        height: '30%',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    headerText: {
        color: Colors.white,
        fontSize: 32,
        fontFamily: 'Exo_700Bold',
        textAlign: 'center',
    },
    text: {
        color: Colors.white,
        fontFamily: 'Exo_400Regular',
        marginHorizontal: 25,
        fontSize: 14,
        textAlign: 'center',
    },
    content: {
        flex: 1,
        width: '100%',
        zIndex: 9998,
        backgroundColor: Colors.white,
        padding: 20,
        borderTopLeftRadius: 50,
        borderTopRightRadius: 50,
        alignItems: 'center',
    },
    title: {
        fontSize: 24,
        fontFamily: 'Exo_700Bold',
        color: Colors.black,
        marginBottom: 20,
        textAlign: 'center',
    },
    rowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: 10,
    },
    imageContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        width: 35,
        height: 50,
        borderColor: Colors.gray,
        borderWidth: 1,
        borderRadius: 10,
        padding: 1,
    },
    healthIcon: {
        width: '100%',
        height: '100%',
        resizeMode: 'contain',
    },
    textContainer: {
        flex: 3,
        marginLeft: 10,
    },
    sectionHeading: {
        fontSize: 12,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
    },
    sectionDescription: {
        fontSize: 8,
        fontFamily: 'Exo_400Regular',
        color: Colors.darkGray,
    },
    toggleContainer: {
        flex: 1,
        alignItems: 'flex-end',
    },
    buttonContainer: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 300,
        fontFamily: 'Exo_400Regular',
    },
    backgroundImage: {
        position: 'absolute',
        top: '26%',
        right: '-19%',
        width: '40%',
        height: '40%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '90deg' }],
    },
    backgroundImage1: {
        position: 'absolute',
        top: '1%',
        left: '-8%',
        width: '35%',
        height: '35%',
        resizeMode: 'contain',
        zIndex: 1,
        transform: [{ translateY: '-50%' }, { rotate: '-10deg' }],
    },
    leaf_background: {
        position: 'absolute',
        bottom: '-13%',
        right: '12%',
        width: '70%',
        height: '20%',
        resizeMode: 'contain',
        zIndex: 9998,
        transform: [{ translateY: '-50%' }, { rotate: '-5deg' }],
    },
    loaderContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: 'center',
        backgroundColor: Colors.white,
        borderTopLeftRadius: 50,
        borderTopRightRadius: 50,
    },
});