import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
} from "react-native";
import { CustomButton, CustomLoader } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import Circle from "assets/illustrator/illustrator_3.png";
import Lemon_2 from "assets/illustrator/illustrator_4.png";
import { devices as fallbackDevices } from "constants/options";
import deviceService from "services/deviceService";
import { screenWidth } from "constants/sizes";
import useOnboardingFormStore from "store/onboadingFormStore";

export const SelectDeviceScreen = () => {
  const { currentOnboardingStep, deviceDetails } = useOnboardingFormStore(
    (state) => state
  );
  const { setDeviceOnboardingDetails, setOnboardingStep } =
    useOnboardingFormStore((state) => state);

  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDevice, setSelectedDevice] = useState({
    id: deviceDetails?.id || "",
    name: deviceDetails?.name || "",
    version: deviceDetails?.version || "",
    image: deviceDetails?.image || "",
    type: deviceDetails?.type || "",
    referenceId: deviceDetails?.referenceId || "",
  });

  // Format device data
  const formatDeviceData = (device) => ({
    ...device,
    name:
      device.name.charAt(0).toUpperCase() + device.name.slice(1).toLowerCase(),
    version: `V${device.version}`,
    image: device.thumbnailUrl,
  });

  useEffect(() => {
    const fetchDevices = async () => {
      try {
        setLoading(true);
        const res = await deviceService.getDevices();

        if (res.success && res.data.length > 0) {
          const formattedDevices = res.data.map(formatDeviceData);
          setDevices(formattedDevices);
        } else {
          const formattedFallbackDevices =
            fallbackDevices.map(formatDeviceData);
          setDevices(formattedFallbackDevices);
        }
      } catch (err) {
        setError("Failed to fetch devices");
        const formattedFallbackDevices = fallbackDevices.map(formatDeviceData);
        setDevices(formattedFallbackDevices);
      } finally {
        setLoading(false);
      }
    };

    fetchDevices();
  }, []);

  const handleDeviceSelect = (device) => {
    setSelectedDevice({
      id: device?.id || "",
      name: device?.name || "",
      version: device?.version || "",
      image: device?.image || "",
      type: device?.type || "",
      referenceId: device?.serialIds || "",
    });
  };

  const isDeviceSelected = (deviceId) => {
    return selectedDevice?.id === deviceId;
  };

  const handleNext = () => {
    if (currentOnboardingStep < 5) {
      setOnboardingStep(currentOnboardingStep + 1);
      setDeviceOnboardingDetails({
        ...selectedDevice,
      });
    }
  };

  const handleBack = () => {
    if (currentOnboardingStep > 1) {
      setOnboardingStep(currentOnboardingStep - 1);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.backgroundImage} pointerEvents="none">
        <Image
          source={Circle}
          style={{ width: "100%", height: "100%" }}
          resizeMode="contain"
        />
      </View>
      <View style={styles.content}>
        <Text style={styles.title}>Select a Device</Text>
        <Text style={styles.desc}>
          Pair your device for customised recommendations.
        </Text>

        {loading ? (
          <View style={styles.centerContent}>
            <CustomLoader />
          </View>
        ) : error ? (
          <View style={styles.centerContent}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        ) : (
          <ScrollView
            contentContainerStyle={[
              styles.scrollContainer,
              devices.length <= 2 && styles.centerContent,
            ]}
            showsVerticalScrollIndicator={false}
          >
            <View
              style={[
                styles.devicesContainer,
                devices.length === 1 && styles.centerSingleDevice,
                // devices.length === 2 && styles.rowTwoDevices,
                devices.length >= 2 && styles.rowMultipleDevices,
              ]}
            >
              {devices.map((device, index) => (
                <TouchableOpacity
                  key={`device-${index}`}
                  style={[
                    styles.deviceBox,
                    isDeviceSelected(device.id) && styles.selectedDevice,
                    { width: screenWidth * 0.5 - 75, aspectRatio: 1 },
                  ]}
                  onPress={() => handleDeviceSelect(device)}
                  activeOpacity={0.8}
                >
                  <Text style={styles.deviceHeading}>{device.name}</Text>
                  <Text style={styles.deviceText}>{device.version}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        )}
      </View>
      <View style={styles.lemon} pointerEvents="none">
        <Image
          source={Lemon_2}
          style={{ width: "100%", height: "100%" }}
          resizeMode="contain"
        />
      </View>
      <View style={styles.footer}>
        <View style={styles.buttonContainer}>
          <CustomButton title="Back" onPress={handleBack} isBack />
          <CustomButton title="Next" onPress={handleNext} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primaryGreen,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: "center",
    marginTop: 50,
  },
  title: {
    fontSize: 35,
    fontFamily: "Exo_700Bold",
    color: Colors.white,
    marginBottom: 10,
    textAlign: "center",
  },
  desc: {
    fontSize: 18,
    fontFamily: "Exo_400Regular",
    color: Colors.white,
    marginBottom: 30,
    textAlign: "center",
  },
  devicesContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 32,
  },
  centerSingleDevice: {
    justifyContent: "center",
    alignItems: "center",
  },
  rowTwoDevices: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  rowMultipleDevices: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    alignItems: "center",
  },
  deviceBox: {
    backgroundColor: Colors.white,
    padding: 20,
    borderRadius: 15,
    justifyContent: "center",
    width: 100,
    height: 100,
    borderColor: Colors.darkGreen,
    borderRightWidth: 7,
    borderBottomWidth: 7,
  },
  selectedDevice: {
    backgroundColor: Colors.white,
    borderColor: Colors.primaryPurple,
    borderWidth: 2,
    transform: [{ scale: 1.0 }],
    // Add subtle shadow for selected state
    shadowColor: Colors.primaryPurple,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  centerContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: Colors.white,
    fontSize: 18,
    fontFamily: "Exo_400Regular",
  },
  errorText: {
    color: Colors.white,
    fontSize: 16,
    fontFamily: "Exo_400Regular",
    textAlign: "center",
  },
  deviceHeading: {
    color: Colors.black,
    fontSize: 18,
    fontFamily: "Exo_700Bold",
    textAlign: "center",
  },
  deviceText: {
    color: Colors.black,
    fontSize: 12,
    fontFamily: "Exo_400Regular",
    textAlign: "center",
  },
  footer: {
    height: "25%",
    backgroundColor: Colors.white,
    borderTopLeftRadius: 50,
    borderTopRightRadius: 50,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-evenly",
    gap: 60,
    zIndex: 9999,
  },
  lemon: {
    position: "absolute",
    top: "71%",
    left: "-17%",
    width: "55%",
    height: "55%",
    resizeMode: "contain",
    zIndex: 9999, // Lower than the content and form containers
    transform: [{ translateY: "-50%" }, { rotate: "10deg" }],
  },
  backgroundImage: {
    position: "absolute",
    top: "8%",
    right: "-10%",
    width: "50%",
    height: "50%",
    resizeMode: "contain",
    zIndex: 9999, // Lower than the content and form containers
    transform: [{ translateY: "-50%" }, { rotate: "-5deg" }],
  },
});
