import React, { useState, useEffect, useRef } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, Alert, Linking } from 'react-native';
import { CustomButton, CustomInput } from 'components/CustomAction';
import { Colors } from 'constants/theme/colors';
import { useAuth } from 'context/AuthContext';
import userService from 'services/userService';
import CustomLoader from 'components/CustomAction/CustomLoader';
import CustomAlert from 'components/CustomAction/CustomAlert';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
// import * as Haptics from 'expo-haptics';

// Import images
import Circle from 'assets/illustrator/illustrator_3.png';
import Leaf from 'assets/illustrator/illustrator_2.png';
import Lemon_2 from 'assets/illustrator/illustrator_1.png';
import Lemon_1 from 'assets/illustrator/illustrator_5.png';
import useOnboardingFormStore from 'store/onboadingFormStore';
import { CameraView, useCameraPermissions } from 'expo-camera';
import { ThemeFonts } from 'constants/theme/fonts';

export const ScanDeviceScreen = ({ navigation }) => {
    const { currentOnboardingStep, profile, goals, deviceDetails, app_permissions } = useOnboardingFormStore(state => state);
    const { setOnboardingStep, resetOnboardingForm, setDeviceDetails } = useOnboardingFormStore(state => state);

    const [permission, requestPermission] = useCameraPermissions();
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [showAlert, setShowAlert] = useState(false);
    const { updateUserProfileLocal } = useAuth();
    const [barCodeData, setBarCodeData] = useState(null);
    const [deviceData, setDeviceData] = useState({});

    const [scanned, setScanned] = useState(false);

    const handleBack = () => {
        if (currentOnboardingStep > 1) {
            setOnboardingStep(currentOnboardingStep - 1);
        }
    };

    const handleBarCodeScanned = ({ type, data }) => {
        setBarCodeData(data);
        setScanned(true);
    };

    const handleNext = async () => {
        setIsLoading(true);
        setError('');

        if (!deviceData?.serialId) {
            setError('Please scan or enter a device ID');
            setIsLoading(false);
            return;
        }

        try {
            const userProfileData = {
                age: Number(profile.age) || 0,
                gender: profile.sex,
                height: Number(profile.height) || 0,
                weight: Number(profile.weight) || 0,
                diet_preference: profile.dietPreference,
                physical_goal: goals.physical,
                activity_goal: goals.movement,
                mind_goal: goals.mindfulness,
                sleep_goal: goals.sleep,
                deviceUsageLimit: goals.deviceUsageLimit == "null" ? null : goals.deviceUsageLimit,
                app_permissions: [...app_permissions]
            };

            const res = await userService.completeUserProfile(userProfileData);

            if (res.success) {
                await updateUserProfileLocal({
                    ...res.data,
                    isAccountCompleted: true
                });

                await resetOnboardingForm();
            } else {
                setError(res.error || 'Something went wrong. Please try again.');
            }
        } catch (err) {
            setError(err?.message || 'Something went wrong. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <View style={styles.container}>
            <Image source={Lemon_2} style={styles.lemon} />
            <Image source={Lemon_1} style={styles.lemon_2} />
            <Image source={Leaf} style={styles.leaf_2} />
            <Image source={Leaf} style={styles.leaf} />

            <View style={styles.content}>
                <Text style={styles.title}>Connect Your Device</Text>

                {isLoading ? (
                    <CustomLoader />
                ) : (
                    <>
                        <View style={styles.referenceImage}>
                            {!permission?.granted ? (
                                <TouchableOpacity
                                    style={styles.cameraPermissionButton}
                                    activeOpacity={0.8}
                                    onPress={async () => {
                                        const result = await requestPermission();

                                        if (result.canAskAgain === false) {
                                            setShowAlert(true);
                                        }
                                    }}
                                >
                                    <View style={styles.cameraPermissionContainer}>
                                        <MaterialCommunityIcons name="barcode-scan" size={24} color={Colors.primaryPurple} />
                                        <Text style={styles.cameraPermissionText}>
                                            Scan Barcode
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            ) : deviceData?.serialId ? (
                                <View>
                                    <Image
                                        source={{ uri: 'https://cdn11.bigcommerce.com/s-ilgxsy4t82/images/stencil/1280x1280/products/323073/1136161/61--uDS1DJL._AC_SL1500___09686.1739336657.jpg?c=1&imbypass=on' }}
                                        style={styles.deviceImageStyle}
                                        resizeMode="cover"
                                    />
                                    <View style={styles.deviceInfoContainer}>
                                        <Text style={styles.serialNoLabel}>Serial Number of product</Text>
                                        <Text style={styles.serialNoText}>Bob marley - 1234</Text>
                                    </View>
                                </View>
                            ) : (
                                <>
                                    <CameraView
                                        style={styles.camera}
                                        facing={'back'}
                                        barcodeScannerSettings={{
                                            barcodeTypes: ["qr", "pdf417", "ean13", "ean8", "code39", "code128"],
                                        }}
                                        mode="picture"
                                        onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
                                        flash='off'
                                    >
                                        <View style={styles.scanOverlay}>
                                            <View style={styles.scanFrame} />
                                            <Text style={styles.scanText}>
                                                {scanned ? 'Device ID scanned!' : 'Align QR code within frame'}
                                            </Text>
                                        </View>
                                    </CameraView>
                                    <CustomAlert
                                        visible={barCodeData}
                                        title={"Device ID Scanned"}
                                        message={barCodeData}
                                        buttons={[
                                            {
                                                text: "Scan Again",
                                                onPress: () => {
                                                    setBarCodeData(null);
                                                    setScanned(false);
                                                },
                                            },
                                            {
                                                text: "Next",
                                                onPress: () => {
                                                    setDeviceData({
                                                        serialId: barCodeData,
                                                    });
                                                    setBarCodeData(null);
                                                },
                                            },
                                        ]}
                                        onClose={() => {
                                            setBarCodeData(null);
                                            setScanned(false)
                                        }}
                                    />
                                </>
                            )}
                        </View>

                        {error && <Text style={styles.error}>{error}</Text>}
                    </>
                )}

                <View style={styles.footer}>
                    <View style={styles.buttonContainer}>
                        <CustomButton
                            title="Back"
                            onPress={handleBack}
                            isBack
                        />
                        <CustomButton
                            title="Next"
                            onPress={handleNext}
                            disabled={isLoading || !deviceData?.serialId}
                            disabledBgColor={!deviceData?.serialId ? Colors.gray : Colors.primaryPurple}
                            disabledTextColor={Colors.white}
                        />
                    </View>
                </View>
            </View>

            <CustomAlert
                visible={showAlert}
                title="Camera Permission Required"
                message="Please grant camera permission to scan your device."
                buttons={[
                    {
                        text: "OK",
                        onPress: () => {
                            setShowAlert(false);
                            Linking.openSettings();
                        },
                        style: "allowButton"
                    }
                ]}
                onClose={() => setShowAlert(false)}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: Colors.white,
    },
    content: {
        flex: 1,
        marginHorizontal: 0,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999,
    },
    title: {
        fontSize: 24,
        fontFamily: 'Exo_500Medium',
        marginBottom: 20,
    },
    referenceImage: {
        backgroundColor: Colors.white,
        width: "100%",
        aspectRatio: 16 / 10.5,
        marginBottom: 10,
        borderRadius: 25,
        borderColor: Colors.primaryPurple,
        borderWidth: 2,
        zIndex: 9999,
        overflow: 'hidden',
    },
    deviceImageStyle: {
        width: '100%',
        height: '100%',
    },
    deviceInfoContainer: {
        position: 'absolute',
        bottom: 10,
        left: 8,
        right: 8,
        backgroundColor: Colors.white,
        padding: 4,
        paddingHorizontal: 12,
        borderRadius: 25,
        color: Colors.black,
        textAlign: 'left',
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
    },
    serialNoLabel: {
        fontSize: 11,
        color: Colors.darkGray,
    },
    serialNoText: {
        fontSize: 14,
    },
    error: {
        color: 'red',
        marginBottom: 20,
    },
    footer: {
        alignItems: 'center',
        width: '100%',
        marginTop: 20,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: 20,
    },
    skipText: {
        color: Colors.primaryPurple,
        textDecorationLine: 'underline',
        fontFamily: 'Exo_400Regular',
    },
    lemon: {
        position: 'absolute',
        bottom: '-51%',
        left: '-15%',
        width: '55%',
        height: '55%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '0deg' }],
    },
    lemon_2: {
        position: 'absolute',
        top: '3%',
        right: '-4%',
        width: '25%',
        height: '25%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '-26deg' }],
    },
    leaf: {
        position: 'absolute',
        bottom: '-43%',
        right: '-15%',
        width: '50%',
        height: '50%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '45deg' }],
    },
    leaf_2: {
        position: 'absolute',
        top: '22%',
        left: '-15%',
        width: '50%',
        height: '50%',
        resizeMode: 'contain',
        zIndex: 9997,
        transform: [{ translateY: '-50%' }, { rotate: '10deg' }],
    },
    camera: {
        flex: 1,
        width: '100%',
        height: '100%',
    },
    scanOverlay: {
        flex: 1,
        backgroundColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
        gap: 5,
    },
    scanFrame: {
        flex: 1,
        aspectRatio: 1,
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
        backgroundColor: 'transparent',
    },
    scanText: {
        marginTop: 10,
        fontSize: 14,
        color: '#ffffff',
        backgroundColor: 'transparent',
        borderRadius: 5,
    },
    cameraControls: {
        flexDirection: 'row',
        justifyContent: 'center',
    },
    torchButton: {
        backgroundColor: 'rgba(0,0,0,0.6)',
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 20,
        marginHorizontal: 5,
    },
    scanAgainButton: {
        backgroundColor: 'rgba(0,0,0,0.6)',
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 20,
        marginHorizontal: 5,
    },
    buttonText: {
        color: '#ffffff',
        fontSize: 12,
    },
    cameraPermissionButton: {
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        borderRadius: 15,
    },
    cameraPermissionText: {
        color: Colors.primaryPurple,
        fontFamily: 'Exo_500Medium',
        fontSize: 16,
        textAlign: 'center',
    },
    cameraPermissionContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 10,
    }
});

export default ScanDeviceScreen;