import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableWithoutFeedback } from 'react-native';
import { CustomButton, CustomSelect } from "components/CustomAction";
import { Colors } from 'constants/theme/colors';
import Lemon from 'assets/illustrator/illustrator_1.png';
import Leaf from 'assets/illustrator/Illustrator_6.png';
import { useAuth } from 'context/AuthContext';
import { physicalOptions, deviceUsageLimitOptions, sleepOptions, movementOptions, mindfulnessOptions } from 'constants/options';
import useOnboardingFormStore from 'store/onboadingFormStore';


export const GoalsSetupScreen = () => {

    const { currentOnboardingStep, goals } = useOnboardingFormStore(state => state);
    const { setOnboardingStep, setGoalsOnboardingData } = useOnboardingFormStore(state => state);

    const [localErrors, setLocalErrors] = useState({});
    const { state: session } = useAuth();
    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

    const [userGoals, setUserGoals] = useState({
        physical: goals?.physical || "",
        movement: goals?.movement || "",
        mindfulness: goals?.mindfulness || "",
        sleep: goals?.sleep || "",
        deviceUsageLimit: goals?.deviceUsageLimit || 0
    });

    const validate = () => {
        const newErrors = {};
        if (!userGoals?.physical) {
            newErrors.physical = "Physical goal is required";
            setLocalErrors(newErrors);
            return;
        }
        if (!userGoals?.movement) {
            newErrors.movement = "Movement goal is required";
            setLocalErrors(newErrors);
            return;
        }
        if (!userGoals?.mindfulness) {
            newErrors.mindfulness = "Mindfulness goal is required";
            setLocalErrors(newErrors);
            return;
        }
        if (!userGoals?.sleep) {
            newErrors.sleep = "Sleep goal is required";
            setLocalErrors(newErrors);
            return;
        }
        if (!userGoals?.deviceUsageLimit) {
            newErrors.deviceUsageLimit = "Device Usage goal is required";
            setLocalErrors(newErrors);
            return;
        }

        setLocalErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleNext = () => {
        if (validate()) {
            if (currentOnboardingStep < 5) {
                setGoalsOnboardingData({ ...userGoals });
                setOnboardingStep(currentOnboardingStep + 1);
            }
        }
    };

    const handleBack = () => {
        if (currentOnboardingStep > 1) {
            setOnboardingStep(currentOnboardingStep - 1);
        }
    };

    return (
        <TouchableWithoutFeedback onPress={() => {
            setCurrentOpenDropdown(null);
        }}>
            <View style={styles.container}>
                <Image source={Lemon} style={styles.backgroundImage} />
                <View style={styles.header}>
                    <Text style={styles.headerText}>Welcome, {session?.user?.name?.trim().split(/\s+/)[0] || ''}!</Text>
                    <Text style={styles.text}>Please provide some information to get started.</Text>
                </View>
                <Image source={Lemon} style={styles.backgroundImage1} />

                <View style={styles.content}>
                    <Text style={styles.title}>Goals</Text>

                    <View style={styles.form}>
                        <View style={{ marginVertical: 5 }}>
                            <CustomSelect
                                options={physicalOptions}
                                selectedValue={userGoals?.physical}
                                onValueChange={(value) => {
                                    setUserGoals((prev) => ({
                                        ...prev,
                                        physical: value,
                                    }));
                                }}
                                label="Physical Goals"
                                iconType="activity"
                                containerStyle={{ marginBottom: 20 }}
                                triggerZ={10}
                                listZ={9}
                                currentOpenDropdown={currentOpenDropdown}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                dropdownId={1}
                                clearValidationError={() => {
                                    setLocalErrors((prevErrors) => {
                                        return { ...prevErrors, physical: "" };
                                    });
                                }}
                            />
                        </View>

                        {localErrors.physical && <Text style={styles.error}>{localErrors.physical}</Text>}
                        <View style={{ marginVertical: 10 }}>
                            <CustomSelect
                                options={movementOptions}
                                selectedValue={userGoals?.movement}
                                onValueChange={(value) => {
                                    setUserGoals((prev) => ({
                                        ...prev,
                                        movement: value,
                                    }));
                                }}
                                label="Movement Goals"
                                triggerZ={8}
                                listZ={7}
                                currentOpenDropdown={currentOpenDropdown}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                dropdownId={2}
                                clearValidationError={() => {
                                    setLocalErrors((prevErrors) => {
                                        return { ...prevErrors, movement: "" };
                                    });
                                }}
                            />
                        </View>
                        {localErrors.movement && <Text style={styles.error}>{localErrors.movement}</Text>}

                        <View style={{ marginVertical: 10 }}>
                            <CustomSelect
                                options={mindfulnessOptions}
                                selectedValue={userGoals?.mindfulness}
                                onValueChange={(value) => {
                                    setUserGoals((prev) => ({
                                        ...prev,
                                        mindfulness: value,
                                    }));
                                }}
                                label="Mindfulness Goals"
                                triggerZ={6}
                                listZ={5}
                                currentOpenDropdown={currentOpenDropdown}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                dropdownId={3}
                                clearValidationError={() => {
                                    setLocalErrors((prevErrors) => {
                                        return { ...prevErrors, mindfulness: "" };
                                    });
                                }}
                            />
                        </View>
                        {localErrors.mindfulness && <Text style={styles.error}>{localErrors.mindfulness}</Text>}

                        <View style={{ marginVertical: 10 }}>
                            <CustomSelect
                                options={sleepOptions}
                                selectedValue={userGoals?.sleep}
                                onValueChange={(value) => {
                                    setUserGoals((prev) => ({
                                        ...prev,
                                        sleep: value,
                                    }));
                                }}
                                label="Sleep Goals"
                                triggerZ={4}
                                listZ={3}
                                currentOpenDropdown={currentOpenDropdown}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                dropdownId={4}
                            />
                        </View>
                        {localErrors.sleep && <Text style={styles.error}>{localErrors.sleep}</Text>}

                        <View style={{ marginVertical: 10 }}>
                            <CustomSelect
                                options={deviceUsageLimitOptions}
                                selectedValue={userGoals?.deviceUsageLimit}
                                onValueChange={(value) => {
                                    setUserGoals((prev) => ({
                                        ...prev,
                                        deviceUsageLimit: value,
                                    }));
                                }}
                                label="Device Usage"
                                triggerZ={2}
                                listZ={1}
                                currentOpenDropdown={currentOpenDropdown}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                dropdownId={5}
                                clearValidationError={() => {
                                    setLocalErrors((prevErrors) => {
                                        return { ...prevErrors, deviceUsageLimit: "" };
                                    });
                                }}
                            />
                        </View>
                        {localErrors.deviceUsageLimit && <Text style={styles.error}>{localErrors.deviceUsageLimit}</Text>}

                        <View style={styles.buttonContainer}>
                            <CustomButton title="Back" onPress={handleBack} isBack />
                            <CustomButton title="Next" onPress={handleNext} />
                        </View>
                    </View>
                    <Image source={Leaf} style={styles.leaf_background} />
                </View>
            </View>
        </TouchableWithoutFeedback>
    );
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        backgroundColor: Colors.primaryGreen,
    },
    header: {
        height: '30%',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
        // backgroundColor: '#12705F', // Added from the second component
    },
    headerText: {
        color: Colors.white,
        fontSize: 32,
        fontFamily: 'Exo_700Bold',
        textAlign: 'center',
    },
    text: {
        color: Colors.white,
        fontFamily: 'Exo_400Regular',
        marginHorizontal: 25,
        fontSize: 14,
        textAlign: 'center',
    },
    content: {
        flex: 1,
        width: '100%',
        zIndex: 9, // Increased zIndex to stack above images
        backgroundColor: Colors.white,
        padding: 20,
        borderTopLeftRadius: 50,
        borderTopRightRadius: 50,
    },
    title: {
        fontSize: 20,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
        marginBottom: 20,
        textAlign: 'center',
    },
    form: {
        flex: 5,
        zIndex: 9998, // Increased zIndex to stack above images
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20,
        zIndex: -1,
        fontFamily: 'Exo_400Regular',
    },
    backgroundImage: {
        position: 'absolute',
        top: '26%',
        right: '-19%',
        width: '40%',
        height: '40%',
        resizeMode: 'contain',
        zIndex: 11, // Lower than the content and form containers
        transform: [{ translateY: '-50%' }, { rotate: '90deg' }],
    },
    backgroundImage1: {
        position: 'absolute',
        top: '1%',
        left: '-8%',
        width: '35%',
        height: '35%',
        resizeMode: 'contain',
        zIndex: 11, // Lower than the content and form containers
        transform: [{ translateY: '-50%' }, { rotate: '-10deg' }],
    },
    leaf_background: {
        position: 'absolute',
        bottom: '-23%',
        right: '21%',
        width: '70%',
        height: '30%',
        resizeMode: 'contain',
        zIndex: 9,
        transform: [{ translateY: '-50%' }, { rotate: '-5deg' }],
    },
    error: {
        color: 'red',
        marginHorizontal: 7,// Add error text style if needed
        fontSize: 12,
        marginBottom: 0,
    },
});