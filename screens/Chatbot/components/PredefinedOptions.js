import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';

const PredefinedOptions = ({ options, onOptionPress, textColor = Colors.black, backgroundColor = Colors.lightGray }) => {
    if (!options || options.length === 0) return null;

    return (
        <View style={styles.container}>
            <FlatList
                data={options}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                    <TouchableOpacity
                        style={[styles.optionButton, { backgroundColor }]}
                        onPress={() => onOptionPress(item)}
                    >
                        <Text style={[styles.optionText, { color: textColor }]}>{item.text}</Text>
                    </TouchableOpacity>
                )}
                scrollEnabled={false}
                contentContainerStyle={styles.optionsContainer}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginTop: 4,
        marginBottom: 16,
        paddingHorizontal: 16,
    },
    optionsContainer: {
        width: '100%',
    },
    optionButton: {
        borderRadius: 12,
        paddingVertical: 8,
        paddingHorizontal: 12,
        marginBottom: 8,
        borderWidth: 1,
        borderColor: Colors.gray,
    },
    optionText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_500,
    }
});

export default PredefinedOptions;
