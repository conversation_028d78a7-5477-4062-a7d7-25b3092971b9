import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, TouchableWithoutFeedback } from 'react-native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import Icon from 'react-native-vector-icons/Ionicons';

// Import AI icon
const aiIcon = require('assets/ai-icon.png');

const MessageBubble = ({ message, onAttachmentPress }) => {
    const isUserMessage = message.sender === 'user';

    const renderAttachments = () => {
        if (!message.attachments || message.attachments.length === 0) return null;

        return message.attachments.map((file) => (
            <TouchableOpacity
                key={file.uri}
                style={styles.attachmentContainer}
                onPress={() => onAttachmentPress(file)}
            >
                <Icon
                    name={file.mimeType?.startsWith('image/') ? 'image-outline' : 'document-text-outline'}
                    size={16}
                    color={isUserMessage ? Colors.white : Colors.primaryPurple}
                />
                <Text
                    style={[
                        styles.attachmentName,
                        { color: isUserMessage ? Colors.white : Colors.black }
                    ]}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                >
                    {file.name}
                </Text>
            </TouchableOpacity>
        ));
    };

    return (
        <TouchableWithoutFeedback>
            <View style={[
                styles.container,
                isUserMessage ? styles.userContainer : styles.botContainer
            ]}>
                {/* Bot Profile Icon - Left side */}
                {!isUserMessage && (
                    <View style={styles.avatarContainer}>
                        <Image source={aiIcon} style={styles.avatarImage} />
                    </View>
                )}

                <View style={styles.messageContentContainer}>
                    {/* User message with icon beside text */}
                    {isUserMessage ? (
                        <View style={styles.userMessageRow}>
                            <View style={styles.userBubbleContainer}>
                                <View style={[
                                    styles.bubble,
                                    styles.userBubble
                                ]}>
                                    <Text style={[
                                        styles.messageText,
                                        styles.userText
                                    ]}>
                                        {message.text}
                                    </Text>
                                    {renderAttachments()}
                                </View>
                                <View style={styles.userBubbleArrow} />
                            </View>

                            <View style={styles.userAvatarContainer}>
                                <Icon name="person-circle" size={28} color={Colors.primaryGreen} />
                            </View>
                        </View>
                    ) : (
                        /* Bot message */
                        <View style={styles.botBubbleContainer}>
                            <View style={[
                                styles.bubble,
                                styles.botBubble
                            ]}>
                                <Text style={[
                                    styles.messageText,
                                    styles.botText
                                ]}>
                                    {message.text}
                                </Text>
                                {message.id === 'welcome' && message.options && message.options.length > 0 && (
                                    <View style={[styles.inlineOptionsContainer]}>
                                        {message.options.map((option, index) => {
                                            const isLast = index === message.options.length - 1;
                                            return (
                                                <TouchableOpacity
                                                    activeOpacity={.60}
                                                    key={option.id}
                                                    style={[
                                                        styles.inlineOptionButton,
                                                        isLast && { borderBottomWidth: 0, marginBottom: 0 }
                                                    ]}
                                                    onPress={() => message.onOptionPress && message.onOptionPress(option)}
                                                >
                                                    <Text style={styles.inlineOptionText}>{option.text}</Text>
                                                </TouchableOpacity>
                                            );
                                        })}
                                    </View>
                                )}

                                {renderAttachments()}
                            </View>
                            <View style={styles.botBubbleArrow} />
                        </View>
                    )}

                    <Text style={styles.timestamp}>
                        {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Text>
                </View>
            </View>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    container: {
        marginVertical: 8,
        flexDirection: 'row',
        width: '100%',
    },
    userContainer: {
        justifyContent: 'flex-end',
        // paddingRight: 8,
    },
    botContainer: {
        justifyContent: 'flex-start',
        // paddingLeft: 2,
    },
    avatarContainer: {
        width: 32,
        height: 32,
        borderRadius: 20,
        backgroundColor: Colors.lightGreen,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 8,
    },
    userAvatarContainer: {
        marginRight: 0,
        marginLeft: 8,
    },
    avatarImage: {
        width: 36,
        height: 36,
        borderRadius: 18,
    },
    messageContentContainer: {
        maxWidth: '85%',
        flexDirection: 'column',
    },
    userMessageRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'flex-end',
        width: '100%',
    },
    userBubbleContainer: {
        position: 'relative',
    },
    botBubbleContainer: {
        position: 'relative',
    },
    userBubbleArrow: {
        position: 'absolute',
        right: -10,
        top: 0,
        width: 0,
        height: 0,
        backgroundColor: 'transparent',
        borderStyle: 'solid',
        borderLeftWidth: 10,
        borderRightWidth: 0,
        borderBottomWidth: 10,
        borderTopWidth: 10,
        borderLeftColor: Colors.primaryGreen,
        borderRightColor: 'transparent',
        borderBottomColor: 'transparent',
        borderTopColor: 'transparent',
    },
    botBubbleArrow: {
        position: 'absolute',
        left: -10,
        top: 0,
        width: 0,
        height: 0,
        backgroundColor: 'transparent',
        borderStyle: 'solid',
        borderLeftWidth: 0,
        borderRightWidth: 10,
        borderBottomWidth: 10,
        borderTopWidth: 10,
        borderLeftColor: 'transparent',
        borderRightColor: Colors.lightGray,
        borderBottomColor: 'transparent',
        borderTopColor: 'transparent',
    },
    bubble: {
        borderRadius: 20,
        padding: 12,
        minWidth: 60,
    },
    userBubble: {
        backgroundColor: Colors.primaryGreen,
        borderTopRightRadius: 0,
        position: 'relative',
    },
    botBubble: {
        backgroundColor: Colors.lightGray,
        borderWidth: 1,
        borderColor: Colors.gray,
        borderTopLeftRadius: 0,
        position: 'relative',
    },
    messageText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
    },
    userText: {
        color: Colors.white,
    },
    botText: {
        color: Colors.black,
    },
    timestamp: {
        fontSize: 12,
        color: Colors.darkGray,
        marginTop: 4,
        alignSelf: 'flex-end',
        fontFamily: ThemeFonts.Exo_400,
    },
    attachmentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
        padding: 6,
        borderRadius: 8,
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
    },
    attachmentName: {
        marginLeft: 6,
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        flex: 1,
    },
    inlineOptionsContainer: {
        marginTop: 6,
        width: '100%',
    },
    inlineOptionButton: {
        paddingVertical: 8,
        // paddingHorizontal: 3,
        borderBottomWidth: 1,
        borderBottomColor: Colors.primaryGreen,
    },
    inlineOptionText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.black,
        fontStyle: 'italic'
    }
});

export default MessageBubble;
