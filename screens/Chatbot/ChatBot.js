import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    FlatList,
    Platform,
    ActivityIndicator,
    Keyboard,
    Linking,
    InteractionManager,
    Modal,
    ScrollView,
    Animated,
    TouchableWithoutFeedback
} from 'react-native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import Icon from 'react-native-vector-icons/Ionicons';
import * as DocumentPicker from 'expo-document-picker';
import { CustomAlert, CustomLoader, CustomButton } from 'components/CustomAction';

import useChatbotStore from 'store/chatbotStore';
import MessageBubble from './components/MessageBubble';
import AppLayout from 'navigations/components/Layouts/AppLayout';
import {
    KeyboardAwareScrollView, KeyboardStickyView,
    KeyboardAvoidingView,
} from 'react-native-keyboard-controller';

const MAX_ATTACHMENTS = 1;
const SUPPORTED_MIME_TYPES = ['image/*', 'application/pdf'];

const SessionPromptModal = ({ visible, onContinue, onNewChat }) => (
    <Modal
        visible={visible}
        transparent
        animationType="fade"
    >
        <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Previous Chat Found</Text>
                <Text style={styles.modalText}>
                    Would you like to continue your previous conversation or start a new chat?
                </Text>
                <View style={styles.modalButtons}>
                    {/* <TouchableOpacity
                        style={[styles.modalButton, styles.continueButton]}
                        onPress={onContinue}
                    >
                        <Text style={styles.buttonText}>Continue Chat</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[styles.modalButton, styles.newChatButton]}
                        onPress={onNewChat}
                    >
                        <Text style={styles.buttonText}>Start New Chat</Text>
                    </TouchableOpacity> */}

                    <CustomButton
                        title="Continue Chat"
                        onPress={onContinue}
                        style={styles.continueButton}
                        textStyle={styles.buttonText}
                    />
                    <CustomButton
                        title="Start New Chat"
                        onPress={onNewChat}
                        style={styles.newChatButton}
                        textStyle={styles.buttonText}
                    />
                </View>
            </View>
        </View>
    </Modal>
);

const ChatBot = () => {
    const scrollViewRef = useRef(null);
    const flatListRef = useRef(null);

    // Store
    const messages = useChatbotStore(state => state.messages);
    const isLoading = useChatbotStore(state => state.isLoading);
    const isBotTyping = useChatbotStore(state => state.isBotTyping);
    const showSessionPrompt = useChatbotStore(state => state.showSessionPrompt);
    const predefinedOptions = useChatbotStore(state => state.predefinedOptions);
    const initializeChatbot = useChatbotStore(state => state.initializeChatbot);
    const loadPreviousChat = useChatbotStore(state => state.loadPreviousChat);
    const resetChat = useChatbotStore(state => state.resetChat);
    const sendMessage = useChatbotStore(state => state.sendMessage);


    // Local state
    const [message, setMessage] = useState('');
    const [attachments, setAttachments] = useState([]);
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');

    const [hasScreenLoaded, setHasScreenLoaded] = useState(false);

    useEffect(() => {
        if (!hasScreenLoaded) return;
        if (scrollViewRef?.current && !isLoading) scrollViewRef?.current.scrollToEnd({ animated: true });

    }, [hasScreenLoaded, messages, scrollViewRef, isLoading]);

    // Initialize chatbot
    useEffect(() => {
        if (!hasScreenLoaded) return;
        initializeChatbot();
    }, [hasScreenLoaded]);

    // Handle sending message
    const handleSendMessage = async (selectedMessage) => {

        const trimmedMessage = selectedMessage ? selectedMessage : message.trim();
        if ((!trimmedMessage && attachments.length === 0) || isLoading) return;

        setMessage('');
        setAttachments([]);

        // Send message and/or attachments
        await sendMessage(trimmedMessage, attachments);

    };

    // Handle attachment selection
    const handleAttachment = async () => {
        try {
            if (attachments.length >= MAX_ATTACHMENTS) {
                setAlertMessage(`You can attach up to ${MAX_ATTACHMENTS} files only.`);
                setShowAlert(true);
                return;
            }

            const result = await DocumentPicker.getDocumentAsync({
                type: SUPPORTED_MIME_TYPES,
                copyToCacheDirectory: true,
                multiple: true,
            });

            if (!result.canceled) {
                const newFiles = Array.isArray(result.assets)
                    ? result.assets
                    : result.assets
                        ? [result.assets]
                        : [];

                // Keep track of total attachments
                const remainingSlots = MAX_ATTACHMENTS - attachments.length;
                const filesToAdd = newFiles.slice(0, remainingSlots);

                setAttachments(current => [...current, ...filesToAdd]);
            }
        } catch (error) {
            console.error('Error picking document:', error);
            setAlertMessage('Failed to attach file. Please try again.');
            setShowAlert(true);
        }
    };

    // Handle delete attachment
    const handleDeleteAttachment = (uri) => {
        setAttachments(current => current.filter(file => file.uri !== uri));
    };

    // Handle predefined option selection
    const handleOptionPress = async (option) => {
        await handleSendMessage(option.text);
    };

    // Handle attachment preview
    const handleAttachmentPress = (file) => {
        if (file && file.uri) {
            Linking.openURL(file.uri).catch(() => {
                setAlertMessage('Unable to open the attachment');
                setShowAlert(true);
            });
        }
    };

    const renderMessageItem = ({ item }) => {
        // console.log("chatbot item render ===", item);
        if (item.id === 'welcome' && predefinedOptions.length > 0) {
            const messageWithOptions = messages?.length > 1 ? {
                ...item,
                options: [],
            } : {
                ...item,
                options: predefinedOptions,
                onOptionPress: handleOptionPress
            };


            return (
                <MessageBubble
                    message={messageWithOptions}
                    onAttachmentPress={handleAttachmentPress}
                />
            );
        }

        return (
            <MessageBubble
                message={item}
                onAttachmentPress={handleAttachmentPress}
            />
        );
    };


    // Render current attachments list
    const renderAttachments = () => {
        if (attachments.length === 0) return null;

        return (
            <View style={styles.attachmentsContainer}>
                {attachments.map((file) => (
                    <View key={file.uri} style={styles.attachedFileContainer}>
                        <View style={styles.attachedFileContent}>
                            <Icon
                                name={
                                    file.mimeType?.startsWith('image/')
                                        ? 'image-outline'
                                        : 'document-text-outline'
                                }
                                size={16}
                                color={Colors.primaryPurple}
                            />
                            <View style={styles.fileInfoContainer}>
                                <Text
                                    style={styles.attachedFileName}
                                    numberOfLines={1}
                                    ellipsizeMode="tail"
                                >
                                    {file.name || 'Attachment'}
                                </Text>
                            </View>
                            <TouchableOpacity
                                onPress={() => handleDeleteAttachment(file.uri)}
                                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                            >
                                <Icon name="trash-outline" size={16} color={Colors.primaryPurple} />
                            </TouchableOpacity>
                        </View>
                    </View>
                ))}
            </View>
        );
    };

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            setHasScreenLoaded(true);
        });
    }, []);


    if (!hasScreenLoaded || isLoading) {
        return (
            <AppLayout illustration={false}>
                <View style={styles.loaderContainer}>
                    <CustomLoader />
                </View>
            </AppLayout>
        );
    }

    return (
        <AppLayout illustration={false}>
            <View style={{ flex: 1 }}>
                <KeyboardAwareScrollView
                    showsVerticalScrollIndicator={false}
                    ref={scrollViewRef}
                    contentContainerStyle={{ flexGrow: 1, paddingBottom: 60 }}
                    style={[styles.container]}
                    keyboardShouldPersistTaps="handled"
                    extraKeyboardSpace={-60}
                >
                    <TouchableWithoutFeedback>
                        <View style={styles.innerContainer}>
                            <View style={styles.messagesContainer}>
                                <FlatList
                                    ref={flatListRef}
                                    data={messages}
                                    keyExtractor={(item) => item.id}
                                    renderItem={renderMessageItem}
                                    contentContainerStyle={styles.messagesContent}
                                    showsVerticalScrollIndicator={false}
                                    keyboardShouldPersistTaps="handled"
                                    scrollEnabled={false}
                                    ListHeaderComponent={() => (
                                        <View style={styles.headerContainer}>
                                            <Text style={styles.headerText}>Chatbot</Text>
                                            <Text style={styles.subHeaderText}>Your virtual health assistant</Text>
                                        </View>
                                    )}
                                    ListFooterComponent={() => (
                                        isBotTyping ? (
                                            <View style={styles.typingIndicator}>
                                                <Text style={styles.typingText}>Bot is typing...</Text>
                                                <ActivityIndicator size="small" color={Colors.primaryPurple} />
                                            </View>
                                        ) : null
                                    )}
                                />
                            </View>
                            {/* Alert */}
                            <CustomAlert
                                visible={showAlert}
                                title="Notice"
                                message={alertMessage}
                                buttons={[
                                    {
                                        text: "OK",
                                        onPress: () => setShowAlert(false),
                                        style: "allowButton",
                                    },
                                ]}
                                onClose={() => setShowAlert(false)}
                            />

                            {/* Session Prompt Modal */}
                            <SessionPromptModal
                                visible={showSessionPrompt}
                                onContinue={loadPreviousChat}
                                onNewChat={resetChat}
                            />
                        </View>
                    </TouchableWithoutFeedback>
                </KeyboardAwareScrollView>
                <KeyboardStickyView offset={{
                    closed: -60,
                    opened: 0
                }}>
                    {/* Input Area */}
                    <View style={styles.inputContainer}>
                        {renderAttachments()}
                        <View style={styles.inputRow}>
                            <View style={styles.inputWrapper}>
                                <TextInput
                                    style={styles.input}
                                    value={message}
                                    onChangeText={setMessage}
                                    placeholder="Type your message..."
                                    placeholderTextColor={Colors.darkGray}
                                    multiline
                                    maxLength={1000}
                                    onPressIn={() => {
                                        // scrollViewRef?.current.scrollToEnd({ animated: true });
                                    }}
                                />
                                <TouchableOpacity
                                    style={styles.attachButton}
                                    onPress={handleAttachment}
                                    disabled={true}
                                >
                                    <Icon
                                        name="attach"
                                        size={24}
                                        color={isLoading ? Colors.gray : Colors.primaryPurple}
                                    />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={[
                                        styles.sendButton,
                                        (!message.trim() && attachments.length === 0) && styles.sendButtonDisabled
                                    ]}
                                    onPress={() => handleSendMessage()}
                                    disabled={(!message.trim() && attachments.length === 0) || isLoading}
                                >
                                    {isLoading ? (
                                        <ActivityIndicator color={Colors.white} size="small" />
                                    ) : (
                                        <Icon name="send" size={20} color={Colors.white} />
                                    )}
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </KeyboardStickyView>
            </View>
        </AppLayout>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    innerContainer: {
        flex: 1,
        paddingHorizontal: 16,
    },
    headerContainer: {
        paddingVertical: 16,
        paddingTop: 0,
        borderBottomWidth: 1,
        borderBottomColor: Colors.lightGray,
        marginBottom: 8
    },
    headerText: {
        fontSize: 24,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.primaryPurple,
    },
    subHeaderText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.darkGray,
        marginTop: 4,
    },
    messagesContainer: {
        flex: 1,
        paddingVertical: 16,
    },
    messagesContent: {
        // paddingBottom: 16,
    },
    inputContainer: {
        paddingVertical: 8,
        backgroundColor: Colors.white,
        borderTopWidth: 1,
        borderTopColor: Colors.lightGray,
    },
    inputRow: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'space-between',
        paddingHorizontal: 8,
        paddingBottom: 8,
    },
    inputWrapper: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'flex-end',
        backgroundColor: Colors.lightGray,
        borderRadius: 20,
        paddingHorizontal: 12,
        paddingVertical: 8,
    },
    input: {
        flex: 1,
        maxHeight: 100,
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.black,
        paddingTop: 8,
        paddingBottom: 8,
    },
    attachButton: {
        marginVertical: 8,
        // marginHorizontal: 2,
        justifyContent: 'center',
        alignItems: 'center',
    },
    sendButton: {
        backgroundColor: Colors.primaryGreen,
        borderRadius: 20,
        width: 36,
        height: 36,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 1,
        marginHorizontal: 2,
    },
    sendButtonDisabled: {
        backgroundColor: Colors.gray,
    },
    attachmentsContainer: {
        marginBottom: 8,
    },
    attachedFileContainer: {
        width: '100%',
        marginBottom: 5,
    },
    attachedFileContent: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 4,
        paddingVertical: 6,
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
        borderRadius: 10,
        backgroundColor: Colors.lightGray,
    },
    fileInfoContainer: {
        flex: 1,
        marginHorizontal: 10,
    },
    attachedFileName: {
        fontSize: 12,
        fontFamily: ThemeFonts.Exo_600,
        color: Colors.black,
        marginLeft: -1,
    },
    loaderContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        bottom: 50,
    },
    typingIndicator: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
        marginBottom: 8,
    },
    typingText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.darkGray,
        marginRight: 8,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: Colors.white,
        borderRadius: 16,
        padding: 24,
        width: '80%',
        maxWidth: 400,
    },
    modalTitle: {
        fontSize: 20,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.primaryPurple,
        marginBottom: 16,
        textAlign: 'center',
    },
    modalText: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.black,
        marginBottom: 24,
        textAlign: 'center',
    },
    modalButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    modalButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        marginHorizontal: 8,
    },
    continueButton: {
        backgroundColor: Colors.primaryPurple,
        fontFamily: ThemeFonts.Exo_400,
    },
    newChatButton: {
        backgroundColor: Colors.primaryGreen,
    },
    buttonText: {
        color: Colors.white,
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_600,
        textAlign: 'center',
    },
});

export default ChatBot;
