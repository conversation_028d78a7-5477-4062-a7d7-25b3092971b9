{"name": "appetec", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/exo": "^0.2.3", "@expo-google-fonts/lexend": "^0.2.3", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "@shopify/react-native-performance": "^4.1.2", "@shopify/react-native-skia": "^1.12.4", "axios": "^1.7.9", "date-fns": "^4.1.0", "expo": "~52.0.46", "expo-apple-authentication": "~7.1.3", "expo-application": "~6.0.2", "expo-asset": "~11.0.3", "expo-av": "~15.0.2", "expo-background-fetch": "~13.0.6", "expo-blur": "~14.0.3", "expo-calendar": "~14.0.6", "expo-camera": "~16.0.18", "expo-constants": "~17.0.7", "expo-contacts": "~14.0.5", "expo-crypto": "~14.0.2", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.3", "expo-health-connect": "^0.1.1", "expo-image-picker": "~16.0.6", "expo-intent-launcher": "~12.0.2", "expo-keep-awake": "~14.0.2", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "~18.0.10", "expo-notifications": "~0.29.13", "expo-secure-store": "~14.0.1", "expo-sensors": "~14.0.2", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "firebase": "^11.4.0", "lottie-react-native": "7.1.0", "react": "18.3.1", "react-native": "0.76.9", "react-native-background-actions": "^4.0.1", "react-native-calendars": "^1.1310.0", "react-native-chart-kit": "^6.12.0", "react-native-charts-wrapper-microcosmworks": "^0.6.0", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "~2.20.2", "react-native-health": "^1.19.0", "react-native-health-connect": "^3.3.2", "react-native-keyboard-controller": "^1.17.2", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.13.1", "react-native-performance": "^5.1.4", "react-native-progress": "^5.0.1", "react-native-reanimated": "~3.16.1", "react-native-reanimated-carousel": "^3.5.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-toast-message": "^2.2.1", "react-native-webview": "13.12.5", "react-native-youtube-iframe": "^2.3.0", "zod": "^3.25.56", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-react": "^7.26.3", "@react-native-community/cli": "^15.1.3", "babel-plugin-module-resolver": "^5.0.2", "expo-build-properties": "^0.13.2"}, "overrides": {"@expo/config-plugins": "9.0.0"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}