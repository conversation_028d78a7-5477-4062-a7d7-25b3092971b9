
import { useEffect, useState } from "react";
import { InteractionManager } from "react-native";
import { activityService } from "services/activityService";
import { highlightsService } from "services/highlightService";

export const useActivityHighlights = () => {
    const [activityHighlights, setActivityHighlights] = useState([]);
    const [isLoadingActivityHighlights, setIsLoadingActivityHighlights] = useState(false);
    const [activityHighlightsError, setActivityHighlightsError] = useState(null);

    const getActivityHighlights = async () => {
        setIsLoadingActivityHighlights(true);

        const res = await activityService.getHighlights();

        if (res.success) {
            setActivityHighlights(res.data);
            setActivityHighlightsError(null);
        } else {
            setActivityHighlightsError(res.error);
        }

        setIsLoadingActivityHighlights(false);
    };

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            getActivityHighlights();
        });
    }, []);

    return {
        activityHighlights,
        isLoadingActivityHighlights,
        getActivityHighlights,
        activityHighlightsError,
        setActivityHighlightsError,
    };
};
