
import { useEffect, useState } from "react";
import { InteractionManager } from "react-native";
import { activityService } from "services/activityService";

export const useRecentActivity = () => {
    const [recentActivity, setRecentActivity] = useState(null);
    const [isLoadingRecentActivity, setIsLoadingRecentActivity] = useState(false);
    const [recentActivityError, setRecentActivityError] = useState(null);

    const getRecentActivity = async () => {
        setIsLoadingRecentActivity(true);

        const res = await activityService.getRecentActivity();

        if (res.success) {
            setRecentActivity(res.data);
            setRecentActivityError(null);
        } else {
            setRecentActivityError(res.error);
        }

        setIsLoadingRecentActivity(false);
    };

    useEffect(() => {

        InteractionManager.runAfterInteractions(() => {
            getRecentActivity();
        });
    }, []);

    return {
        recentActivity,
        isLoadingRecentActivity,
        getRecentActivity,
        recentActivityError,
        setRecentActivityError,
    };
};
