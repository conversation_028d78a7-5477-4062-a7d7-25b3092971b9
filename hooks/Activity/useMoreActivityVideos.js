import { startTransition, useEffect, useState } from "react";
import { InteractionManager } from "react-native";
import videosService from "services/videosService";

export const useMoreActivityVideos = ({ filters = ["yoga", "exercise", "abs"] } = {}) => {
    const [moreActivityVideos, setMoreActivityVideos] = useState([]);
    const [isLoadingMoreActivityVideos, setIsLoadingMoreActivityVideos] = useState(false);
    const [moreActivityVideosError, setMoreActivityVideosError] = useState(null);

    const getAllActivityVideos = async () => {
        try {
            setIsLoadingMoreActivityVideos(true);

            const fetchPromises = filters.map((filter) =>
                videosService.getAllVideos({
                    page: 1,
                    tag: "workout",
                    filter,
                })
            );

            const responses = await Promise.all(fetchPromises);

            responses.forEach((res, index) => {
                if (!res.success) {
                    throw new Error(`Failed to fetch videos for ${filters[index]}`);
                }
            });

            const filteredVideos = responses
                .map((res) => res?.data || [])
                .filter((videos) => videos.length > 0).map((videos, index) => ({
                    heading: filters[index],
                    video: videos[0]
                }));

            // setTimeout(() => {
            setMoreActivityVideos(filteredVideos);
            // }, 1000);
        } catch (error) {
            setMoreActivityVideosError("Error fetching more activity videos");
        } finally {
            setIsLoadingMoreActivityVideos(false);
        }
    };


    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            getAllActivityVideos();
        });
    }, [])

    return {
        moreActivityVideos,
        isLoadingMoreActivityVideos,
        getAllActivityVideos,
        moreActivityVideosError,
        setMoreActivityVideosError,
    };
};
