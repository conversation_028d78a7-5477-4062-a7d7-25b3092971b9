import { useEffect, useState } from "react";
import { activityService } from "services/activityService";

export const useActivityGraph = () => {
    const [activityGraphData, setActivityGraphData] = useState([]);
    const [isLoadingActivityGraph, setIsLoadingActivityGraph] = useState(false);
    const [activityGraphError, setActivityGraphError] = useState(null);
    const [activityGraphTimeRange, setActivityGraphTimeRange] = useState(null);
    const [activityGraphFilter, setActivityGraphFilter] = useState("weekly");

    const getActivityGraph = async () => {
        setIsLoadingActivityGraph(true);

        const res = await activityService.getActivityGraph(
            { filter: activityGraphFilter }
        );

        if (res.success) {
            setActivityGraphData(res.data);
            setActivityGraphError(null);
        } else {
            setActivityGraphError(res.error);
        }

        setIsLoadingActivityGraph(false);
    };

    useEffect(() => {
        getActivityGraph();
    }, []);

    return {
        activityGraphData,
        isLoadingActivityGraph,
        getActivityGraph,
        activityGraphError,
        setActivityGraphError,
        activityGraphTimeRange,
        setActivityGraphTimeRange,
        activityGraphFilter,
        setActivityGraphFilter,
    };
};
