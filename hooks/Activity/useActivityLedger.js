
import { useEffect, useState } from "react";
import { InteractionManager } from "react-native";
import { activityService } from "services/activityService";

export const useActivityLedger = () => {
    const [activityLedgerData, setActivityLedgerData] = useState(null);
    const [isLoadingActivityLedger, setIsLoadingActivityLedger] = useState(false);
    const [activityLedgerError, setActivityLedgerError] = useState(null);

    const getTodaysLedger = async () => {
        setIsLoadingActivityLedger(true);

        const res = await activityService.getTodaysLedger();

        if (res.success) {
            setActivityLedgerData(res.data);
            setActivityLedgerError(null);
        } else {
            setActivityLedgerError(res.error);
        }

        setIsLoadingActivityLedger(false);
    };

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            getTodaysLedger();
        });
    }, []);

    return {
        activityLedgerData,
        isLoadingActivityLedger,
        getTodaysLedger,
        activityLedgerError,
        setActivityLedgerError,
    };
};
