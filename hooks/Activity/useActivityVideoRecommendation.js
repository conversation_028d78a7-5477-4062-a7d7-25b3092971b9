
import { useEffect, useState } from "react";
import { InteractionManager } from "react-native";
import { activityService } from "services/activityService";

export const useActivityVideoRecommendation = () => {
    const [recommendedActivityVideo, setRecommendedActivityVideo] = useState(null);
    const [isLoadingRecommendedActivityVideo, setIsLoadingRecommendedActivityVideo] = useState(true);
    const [recommendedActivityVideoError, setRecommendedActivityVideoError] = useState(null);

    const getRecommendedActivityVideo = async () => {
        setIsLoadingRecommendedActivityVideo(true);

        const res = await activityService.getRecommendedVideo();

        if (res.success) {
            setRecommendedActivityVideo(res.data);
            setRecommendedActivityVideoError(null);
        } else {
            setRecommendedActivityVideoError(res.error);
        }

        setIsLoadingRecommendedActivityVideo(false);
    };

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            getRecommendedActivityVideo();
        });
    }, []);

    return {
        recommendedActivityVideo,
        isLoadingRecommendedActivityVideo,
        getRecommendedActivityVideo,
        recommendedActivityVideoError,
        setRecommendedActivityVideoError,
    };
};
