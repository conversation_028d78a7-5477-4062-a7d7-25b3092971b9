import { Platform } from 'react-native';

/**
 * This hook serves as a platform-specific router for health data functionality.
 * It dynamically imports the appropriate platform-specific hook (iOS or Android)
 * to avoid loading unnecessary native modules on the wrong platform.
 *
 * The platform-specific hooks (useHealthDataIOS and useHealthDataAndroid) implement
 * the same interface, making them interchangeable from the consumer's perspective.
 */
export const useHealthData = () => {
    // Use dynamic imports to ensure we only load the platform-specific code
    if (Platform.OS === 'ios') {
        // Only import iOS health data hook on iOS devices
        const { useHealthDataIOS } = require('./useHealthDataIOS');
        return useHealthDataIOS;
    } else if (Platform.OS === 'android') {
        // Only import Android health data hook on Android devices
        const { useHealthDataAndroid } = require('./useHealthDataAndroid');
        return useHealthDataAndroid;
    }

    // Fallback for other platforms (should not happen in practice)
    return () => ({
        // Common interface properties
        hasPermissions: false,
        hasApplePermissions: false,
        hasGooglePermissions: false,
        steps: 0,
        flights: 0,
        distance: 0,
        error: 'Unsupported platform',
        requestPermission: () => Promise.resolve({ granted: false }),
        removePermission: () => Promise.resolve(false),
        fetchPermissions: () => Promise.resolve(null),
        isInitialized: false
    });
};
