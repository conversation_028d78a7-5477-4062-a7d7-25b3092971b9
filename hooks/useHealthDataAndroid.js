import { useState, useEffect, useCallback } from 'react';
import deviceService from 'services/deviceService';
import { NativeModules, PermissionsAndroid, Platform } from 'react-native';

// Only import HealthConnect if we're on Android
let HealthConnect = null;
if (Platform.OS === 'android') {
    try {
        HealthConnect = require('react-native-health-connect');
    } catch (error) {
        console.warn('Failed to load react-native-health-connect:', error);
    }
}

// Only get the native module if we're on Android
const connect = Platform.OS === 'android' ? NativeModules.HealthConnect : null;


const HEALTH_METRICS = {
    CALORIES_BURNED: 'ActiveCaloriesBurned',
    BASEL_BODY_TEMP: 'BasalBodyTemperature',
    BASEL_METABOLIC_RATE: 'BasalMetabolicRate',
    BLOOD_GLUCOSE: 'BloodGlucose',
    BLOOD_PRESSURE: "BloodPressure",
    BODY_FAT: 'BodyFat',
    BODY_TEMP: 'BodyTemperature',
    BONE_MASS: 'BoneMass',
    CERVICAL_MUCUS: 'CervicalMucus',
    CYCLING_PEDALING_CADENCE: 'CyclingPedalingCadence',
    DISTANCE: 'Distance',
    ELEVATION_GAINED: 'ElevationGained',
    EXPERCISE_SESSION: 'ExerciseSession',
    FLOORS: 'FloorsClimbed',
    HEART_RATE: "HeartRate",
    HEIGHT: 'Height',
    HYDRATION: 'Hydration',
    LEAN_BODY_MASS: 'LeanBodyMass',
    MENSTRUATION_FLOW: 'MenstruationFlow',
    MENSTRUATION_PERIOD: 'MenstruationPeriod',
    NUTRITION: 'Nutrition',
    OVULATION_TEST: 'OvulationTest',
    OXYGEN_SATURATION: 'OxygenSaturation',
    POWER: 'Power',
    RESPIRATORY_RATE: 'RespiratoryRate',
    RESTING_HEART_RATE: 'RestingHeartRate',
    SEXUAL_ACTIVITY: 'SexualActivity',
    SLEEP_SESSION: 'SleepSession',
    SPEED: "Speed",
    STEPS: 'Steps',
    STEPS_CANDENCE: 'StepsCadence',
    TOTAL_CALORIES_BURNED: 'TotalCaloriesBurned',
    VO2MAX: 'Vo2Max',
    WEIGHT: 'Weight',
    WHEEL_CHAIR_PUSHES: 'WheelchairPushes'
};

export const useHealthDataAndroid = () => {

    const [healthData, setHealthData] = useState({
        steps: 0,
        distance: 0,
        flights: 0,
        // energy: 0
    });
    const [permissions, setPermissions] = useState([]);
    const [error, setError] = useState(null);
    const [isInitialized, setIsInitialized] = useState(false);
    const [permissionStatus, setPermissionStatus] = useState({
        required: Object.keys(HEALTH_METRICS).length,
        granted: 0
    });
    const [apiPermissions, setApiPermissions] = useState({
        apple: false,
        google: false
    });

    // Helper function to filter relevant permissions
    const filterRelevantPermissions = (grantedPermissions) => {
        return grantedPermissions.filter(permission =>
            Object.values(HEALTH_METRICS).includes(permission)
        ).length;
    };

    // Fetch permissions from the API
    const fetchPermissions = useCallback(async () => {
        try {
            const response = await deviceService.getPermission();
            if (!response.error && response.appPermissions) {
                const permissions = response.appPermissions.reduce((acc, perm) => {
                    acc[perm.app_permission] = perm.isPermissionAllowed;
                    return acc;
                }, {});

                console.log("fetchPermissions ===", permissions);
                setApiPermissions(permissions);
                return permissions;
            }
            return null;
        } catch (err) {
            console.error('Error fetching permissions from API:', err);
            return null;
        }
    }, [deviceService]);

    // Update permission in the API
    const updatePermissionApi = useCallback(async (platform, isAllowed) => {
        try {
            await deviceService.updatePermission({
                app_permission: platform,
                isPermissionAllowed: isAllowed
            });

            // Refresh permissions after update
            await fetchPermissions();
            return true;
        } catch (err) {
            console.error('Error updating permissions in API:', err);
            return false;
        }
    }, [deviceService, fetchPermissions]);

    const initialize = useCallback(async () => {
        // If not on Android or HealthConnect is not available, return false
        if (Platform.OS !== 'android' || !HealthConnect) {
            console.log("HealthConnect not available on this platform");
            setError('HealthConnect not available on this platform');
            return false;
        }

        try {
            const initialized = await HealthConnect.initialize();
            console.log("initialized :", initialized);
            if (initialized) {
                const grantedPermissions = await HealthConnect.getGrantedPermissions();
                const relevantPermissionsCount = filterRelevantPermissions(grantedPermissions);

                setPermissionStatus(prev => ({
                    ...prev,
                    granted: relevantPermissionsCount
                }));

                // Also fetch permissions from API
                await fetchPermissions();
            }
            setIsInitialized(initialized);
            return initialized;
        } catch (err) {
            console.error('Initialization failed:', err);
            setError('Initialization failed');
            return false;
        }
    }, [fetchPermissions]);

    const requestHealthPermission = useCallback(async (platform = 'google') => {
        // If not on Android or HealthConnect is not available, return false
        if (Platform.OS !== 'android' || !HealthConnect) {
            console.log("HealthConnect not available on this platform");
            setError('HealthConnect not available on this platform');
            return { granted: false, missingPermissions: true };
        }

        try {
            if (!isInitialized) {
                const initialized = await initialize();
                if (!initialized) return { granted: false, missingPermissions: true };
            }

            const permissionsToRequest = Object.values(HEALTH_METRICS).map(metric => ({
                accessType: 'read',
                recordType: metric
            }));

            // const writePermissionsToRequest = Object.values(HEALTH_METRICS).map(metric => ({
            //     accessType: 'write',
            //     recordType: metric
            // }));

            const granted = await HealthConnect.requestPermission([...permissionsToRequest]);
            const grantedPermissions = await HealthConnect.getGrantedPermissions();

            if (Platform.OS == 'android') {
                await PermissionsAndroid.requestMultiple([
                    'android.permission.health.READ_HEALTH_DATA_IN_BACKGROUND',
                ]);
            }

            const relevantPermissionsCount = filterRelevantPermissions(grantedPermissions);

            setPermissions(granted);
            setPermissionStatus(prev => ({
                ...prev,
                granted: relevantPermissionsCount
            }));

            // Update API if permissions were granted
            if (relevantPermissionsCount > 0) {
                await updatePermissionApi(platform, true);
            }

            return {
                granted: relevantPermissionsCount > 0,
                missingPermissions: relevantPermissionsCount < permissionStatus.required
            };
        } catch (err) {
            console.error('Permission request error:', err);
            setError('Permission request failed');
            return { granted: false, missingPermissions: true };
        }
    }, [isInitialized, initialize, permissionStatus.required, updatePermissionApi]);

    // New function to remove health permissions
    const removeHealthPermission = useCallback(async (platform = 'google') => {
        // If not on Android or HealthConnect is not available, return false
        if (Platform.OS !== 'android' || !HealthConnect) {
            console.log("HealthConnect not available on this platform");
            setError('HealthConnect not available on this platform');
            return false;
        }

        try {
            // First update the API to reflect permission removal
            const apiUpdateSuccess = await updatePermissionApi(platform, false);

            if (!apiUpdateSuccess) {
                throw new Error('Failed to update permission in API');
            }

            await HealthConnect.revokeAllPermissions();

            // We can't programmatically revoke permissions in Android,
            // but we can direct users to settings to do so manually
            // Here we'll just update our local state to reflect the change
            setPermissions([]);
            setPermissionStatus(prev => ({
                ...prev,
                granted: 0
            }));

            // Clear health data when permissions are removed
            setHealthData({
                steps: 0,
                distance: 0,
                flights: 0,
            });

            return true;
        } catch (err) {
            console.error('Remove permission error:', err);
            setError('Failed to remove health permissions');
            return false;
        }
    }, [updatePermissionApi]);

    const fetchHealthData = useCallback(async () => {
        // If not on Android or HealthConnect is not available, return
        if (Platform.OS !== 'android' || !HealthConnect) {
            console.log("HealthConnect not available on this platform");
            return;
        }

        if (!permissions.length) return;

        const today = new Date();
        const timeRangeFilter = {
            startTime: new Date(today.setHours(0, 0, 0, 0)).toISOString(),
            endTime: new Date(today.setHours(23, 59, 59, 999)).toISOString(),
        };

        try {
            const [stepsData, distanceData, floorsData] = await Promise.all([
                HealthConnect.readRecords(HEALTH_METRICS.STEPS, { timeRangeFilter }),
                HealthConnect.readRecords(HEALTH_METRICS.DISTANCE, { timeRangeFilter }),
                HealthConnect.readRecords(HEALTH_METRICS.FLOORS, { timeRangeFilter }),
            ]);

            setHealthData({
                steps: stepsData.reduce((acc, record) => acc + record.count, 0),
                distance: distanceData.reduce((acc, record) => acc + record.distance, 0),
                flights: floorsData.reduce((acc, record) => acc + record.floors, 0),
            });
        } catch (err) {
            console.error('Failed to fetch health data:', err);
            setError('Failed to fetch health data');
        }
    }, [permissions]);

    useEffect(() => {
        initialize();
    }, [initialize]);

    useEffect(() => {
        if (permissions.length > 0) {
            fetchHealthData();
        }
    }, [permissions, fetchHealthData]);

    // Check if permissions are granted based on both device and API state
    const hasPermissions = useCallback((platform = 'google') => {
        // If not on Android, return false
        if (Platform.OS !== 'android') {
            return false;
        }

        // Check permissions from the device (permissions array)
        const deviceHasPermissions = permissions.length > 0;

        // We could also check API permissions if needed
        // const apiHasPermissions = apiPermissions[platform] === true;
        // return deviceHasPermissions && apiHasPermissions;

        return deviceHasPermissions;
    }, [permissions]);

    // Add debug logging
    useEffect(() => {
        console.log('Permission Status:', {
            required: permissionStatus.required,
            granted: permissionStatus.granted,
            // metrics: Object.keys(HEALTH_METRICS),
            apiPermissions
        });
    }, [permissionStatus, apiPermissions]);

    return {
        ...healthData,
        hasPermissions: hasPermissions(),
        hasGooglePermissions: hasPermissions('google'),
        hasApplePermissions: hasPermissions('apple'),
        apiPermissions,
        error,
        requestPermission: requestHealthPermission,
        removePermission: removeHealthPermission,
        fetchPermissions,
        isInitialized
    };
};