import { useEffect, useState } from 'react';
import { carouselService } from '../services/carouselService.js';
import { InteractionManager } from 'react-native';

export const useCarouselData = ({ deps = [] } = {}) => {
    const [carouselData, setCarouselData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [error, setError] = useState(null);

    const fetchCarouselData = async (isRefresh = false) => {
        if (isRefresh) {
            setRefreshing(true);
        } else {
            setLoading(true);
        }

        try {
            const res = await carouselService.getCarousalData();
            if (res.success) {
                setCarouselData(res.data);
                setError(null);
            } else {
                setError(res.error);
            }
        } catch (err) {
            setError(err.message || 'Something went wrong');
        } finally {
            if (isRefresh) {
                setRefreshing(false);
            } else {
                setLoading(false);
            }
        }
    };

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            fetchCarouselData();
        });
    }, [...deps]);

    return {
        carouselData,
        loadingCarouselData: loading,
        refreshingCarouselData: refreshing,
        carouselError: error,
        refreshCarousel: () => fetchCarouselData(true),
        fetchCarouselData,
        setCarouselError: (error) => setError(error)
    };
};
