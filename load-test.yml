config:
  # target: 'https://appetec.boilerplate.solutions/api'
  target: 'http://192.168.1.38:5000/api'

  phases:
    - duration: 60
      arrivalRate: 10
#   processor: './artillery-token.js'
scenarios:
  - name: 'User & Notification API Load Test'

    flow:
      # 1. Login to get Bear<PERSON> token
      - post:
          url: '/login'
          json:
            email: '<EMAIL>'
            password: 'Pass@123'
          capture:
            - json: '$.accessToken'
              as: 'authToken'
      # 2. User Profile Endpoints
      #   - get:
      #       url: '/profile'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      #   - put:
      #       url: '/profile/setup_profile'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      #       json:
      #         firstName: 'Test'
      #         lastName: 'User'
      #         dob: '1990-01-01'
      #         gender: 'male'
      #   - put:
      #       url: '/profile'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      #       json:
      #         firstName: 'Updated'
      #         lastName: 'User'
      - put:
          url: '/timezone_update'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            timeZone: 'UTC'
      #   - get:
      #       url: '/status'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      # 3. Reminders Endpoints
      #   - post:
      #       url: '/reminders'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      #       json:
      #         title: 'Test Reminder'
      #         time: '2024-01-01T09:00:00Z'
      #   - get:
      #       url: '/reminders'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      #   - get:
      #       url: '/reminders/1'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      #   - put:
      #       url: '/reminders/1'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      #       json:
      #         title: 'Updated Reminder'
      #         time: '2024-01-01T10:00:00Z'
      #   - put:
      #       url: '/reminders/delete/1'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      # 4. App Permissions
      #   - get:
      #       url: '/app_permissions'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      #   - put:
      #       url: '/app_permissions/update'
      #       headers:
      #         Authorization: 'Bearer {{ authToken }}'
      #       json:
      #         permissions:
      #           - 'apple'
      #           - 'google'
      # 5. Notification Token Endpoints
      # 2. Authenticated requests (token will be set by processor)
      - get:
          url: '/profile'
          headers:
            Authorization: 'Bearer {{ authToken }}'
      - get:
          url: '/meal_records'
          headers:
            Authorization: 'Bearer {{ authToken }}'
      - post:
          url: '/meal_records'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            # Add a valid payload for meal record creation here
            sampleField: 'sampleValue'
      - put:
          url: '/profile'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            # Add a valid payload for profile update here
            sampleField: 'sampleValue'
      - get:
          url: '/weight_records'
          headers:
            Authorization: 'Bearer {{ authToken }}'
      - get:
          url: '/status'
          headers:
            Authorization: 'Bearer {{ authToken }}'
      # Notification Token Endpoints
      - post:
          url: '/notification_token'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            deviceId: 'device123'
            notificationToken: 'notifToken123'
            osType: 'android'
            deviceType: 'mobile'
      - put:
          url: '/notification_token'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            deviceId: 'device123'
            isNotificationActive: true

      - get:
          url: '/notification_token/device123'
          headers:
            Authorization: 'Bearer {{ authToken }}'
      # Add more authenticated endpoints as needed
  - name: 'Public endpoints'
    flow:
      - get:
          url: '/health'
      # Add more public endpoints as needed
# Note: For a full test, expand the scenario with all discovered endpoints and provide valid payloads for POST/PUT requests.
# The processor (artillery-token.js) is optional if you want to manage tokens globally or add custom logic.
